object frmDataTransfer: TfrmDataTransfer
  Left = 0
  Top = 0
  Caption = '数据导入/导出'
  ClientHeight = 400
  ClientWidth = 600
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poScreenCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  TextHeight = 15
  object pnlMain: TPanel
    Left = 0
    Top = 0
    Width = 600
    Height = 400
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    object pcMain: TPageControl
      Left = 0
      Top = 0
      Width = 600
      Height = 400
      ActivePage = tsExport
      Align = alClient
      TabOrder = 0
      object tsExport: TTabSheet
        Caption = '导出'
        object pnlExportTop: TPanel
          Left = 0
          Top = 0
          Width = 592
          Height = 105
          Align = alTop
          BevelOuter = bvNone
          TabOrder = 0
          object lblExportTable: TLabel
            Left = 16
            Top = 16
            Width = 24
            Height = 15
            Caption = '表名:'
          end
          object lblExportFormat: TLabel
            Left = 16
            Top = 45
            Width = 36
            Height = 15
            Caption = '格式：'
          end
          object lblExportFile: TLabel
            Left = 16
            Top = 74
            Width = 48
            Height = 15
            Caption = '导出到：'
          end
          object cmbExportTable: TComboBox
            Left = 70
            Top = 12
            Width = 200
            Height = 23
            Style = csDropDownList
            TabOrder = 0
          end
          object cmbExportFormat: TComboBox
            Left = 70
            Top = 41
            Width = 200
            Height = 23
            Style = csDropDownList
            TabOrder = 1
            OnChange = cmbExportFormatChange
          end
          object edtExportFile: TEdit
            Left = 70
            Top = 70
            Width = 431
            Height = 23
            TabOrder = 2
          end
          object btnBrowseExport: TButton
            Left = 507
            Top = 69
            Width = 75
            Height = 25
            Caption = '浏览...'
            TabOrder = 3
            OnClick = btnBrowseExportClick
          end
        end
        object pnlExportOptions: TPanel
          Left = 0
          Top = 105
          Width = 592
          Height = 226
          Align = alClient
          BevelOuter = bvNone
          TabOrder = 1
          object lblExportDelimiter: TLabel
            Left = 16
            Top = 72
            Width = 48
            Height = 15
            Caption = '分隔符：'
          end
          object chkExportHeaders: TCheckBox
            Left = 16
            Top = 16
            Width = 97
            Height = 17
            Caption = '包含表头'
            TabOrder = 0
          end
          object chkExportQuotes: TCheckBox
            Left = 16
            Top = 39
            Width = 145
            Height = 17
            Caption = '字符串添加引号'
            TabOrder = 1
          end
          object cmbExportDelimiter: TComboBox
            Left = 70
            Top = 68
            Width = 200
            Height = 23
            TabOrder = 2
          end
        end
        object pnlExportBottom: TPanel
          Left = 0
          Top = 331
          Width = 592
          Height = 41
          Align = alBottom
          BevelOuter = bvNone
          TabOrder = 2
          object btnExport: TButton
            Left = 507
            Top = 8
            Width = 75
            Height = 25
            Action = actExport
            TabOrder = 0
          end
        end
      end
      object tsImport: TTabSheet
        Caption = '导入'
        ImageIndex = 1
        object pnlImportTop: TPanel
          Left = 0
          Top = 0
          Width = 592
          Height = 105
          Align = alTop
          BevelOuter = bvNone
          TabOrder = 0
          object lblImportTable: TLabel
            Left = 16
            Top = 16
            Width = 24
            Height = 15
            Caption = '表名:'
          end
          object lblImportFormat: TLabel
            Left = 16
            Top = 45
            Width = 36
            Height = 15
            Caption = '格式：'
          end
          object lblImportFile: TLabel
            Left = 16
            Top = 74
            Width = 48
            Height = 15
            Caption = '导入从：'
          end
          object cmbImportTable: TComboBox
            Left = 70
            Top = 12
            Width = 200
            Height = 23
            Style = csDropDownList
            TabOrder = 0
          end
          object cmbImportFormat: TComboBox
            Left = 70
            Top = 41
            Width = 200
            Height = 23
            Style = csDropDownList
            TabOrder = 1
            OnChange = cmbImportFormatChange
          end
          object edtImportFile: TEdit
            Left = 70
            Top = 70
            Width = 431
            Height = 23
            TabOrder = 2
          end
          object btnBrowseImport: TButton
            Left = 507
            Top = 69
            Width = 75
            Height = 25
            Caption = '浏览...'
            TabOrder = 3
            OnClick = btnBrowseImportClick
          end
        end
        object pnlImportOptions: TPanel
          Left = 0
          Top = 105
          Width = 592
          Height = 226
          Align = alClient
          BevelOuter = bvNone
          TabOrder = 1
          object lblImportDelimiter: TLabel
            Left = 16
            Top = 45
            Width = 48
            Height = 15
            Caption = '分隔符：'
          end
          object chkImportHeaders: TCheckBox
            Left = 16
            Top = 16
            Width = 97
            Height = 17
            Caption = '包含表头'
            TabOrder = 0
          end
          object cmbImportDelimiter: TComboBox
            Left = 70
            Top = 41
            Width = 200
            Height = 23
            TabOrder = 1
          end
          object chkCreateTable: TCheckBox
            Left = 16
            Top = 80
            Width = 97
            Height = 17
            Caption = '创建新表'
            TabOrder = 2
            OnClick = chkCreateTableClick
          end
          object edtNewTable: TEdit
            Left = 119
            Top = 78
            Width = 151
            Height = 23
            TabOrder = 3
          end
        end
        object pnlImportBottom: TPanel
          Left = 0
          Top = 331
          Width = 592
          Height = 41
          Align = alBottom
          BevelOuter = bvNone
          TabOrder = 2
          object btnImport: TButton
            Left = 507
            Top = 8
            Width = 75
            Height = 25
            Action = actImport
            TabOrder = 0
          end
        end
      end
    end
  end
  object alMain: TActionList
    Left = 40
    Top = 80
    object actExport: TAction
      Caption = '导出'
      OnExecute = actExportExecute
    end
    object actImport: TAction
      Caption = '导入'
      OnExecute = actImportExecute
    end
  end
end
