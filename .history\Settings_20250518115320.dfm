object frmSettings: TfrmSettings
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = '设置'
  ClientHeight = 350
  ClientWidth = 500
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poScreenCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  TextHeight = 15
  object pnlMain: TPanel
    Left = 0
    Top = 0
    Width = 500
    Height = 309
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    object pcSettings: TPageControl
      Left = 0
      Top = 0
      Width = 500
      Height = 309
      ActivePage = tsGeneral
      Align = alClient
      TabOrder = 0
      object tsGeneral: TTabSheet
        Caption = '常规'
        object grpGeneral: TGroupBox
          Left = 16
          Top = 16
          Width = 457
          Height = 249
          Caption = '常规设置'
          TabOrder = 0
          object lblLastDB: TLabel
            Left = 16
            Top = 56
            Width = 72
            Height = 15
            Caption = '默认数据库：'
          end
          object chkAutoConnect: TCheckBox
            Left = 16
            Top = 24
            Width = 425
            Height = 17
            Caption = '启动时自动连接到上次使用的数据库'
            TabOrder = 0
          end
          object edtLastDB: TEdit
            Left = 16
            Top = 77
            Width = 353
            Height = 23
            TabOrder = 1
          end
          object btnBrowseLastDB: TButton
            Left = 375
            Top = 76
            Width = 66
            Height = 25
            Caption = '浏览...'
            TabOrder = 2
            OnClick = btnBrowseLastDBClick
          end
          object chkConfirmExit: TCheckBox
            Left = 16
            Top = 120
            Width = 425
            Height = 17
            Caption = '退出时确认'
            TabOrder = 3
          end
        end
      end
      object tsDisplay: TTabSheet
        Caption = '显示'
        ImageIndex = 1
        object grpDisplay: TGroupBox
          Left = 16
          Top = 16
          Width = 457
          Height = 249
          Caption = '显示设置'
          TabOrder = 0
          object lblFontSize: TLabel
            Left = 16
            Top = 24
            Width = 60
            Height = 15
            Caption = '字体大小：'
          end
          object edtFontSize: TEdit
            Left = 82
            Top = 21
            Width = 50
            Height = 23
            TabOrder = 0
            Text = '10'
          end
          object udFontSize: TUpDown
            Left = 132
            Top = 21
            Width = 16
            Height = 23
            Associate = edtFontSize
            Min = 8
            Max = 24
            Position = 10
            TabOrder = 1
          end
          object chkShowGrid: TCheckBox
            Left = 16
            Top = 64
            Width = 425
            Height = 17
            Caption = '显示网格线'
            TabOrder = 2
          end
          object chkShowHeaders: TCheckBox
            Left = 16
            Top = 96
            Width = 425
            Height = 17
            Caption = '显示列标题'
            TabOrder = 3
          end
        end
      end
    end
  end
  object pnlButtons: TPanel
    Left = 0
    Top = 309
    Width = 500
    Height = 41
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    object btnOK: TButton
      Left = 254
      Top = 8
      Width = 75
      Height = 25
      Caption = '确定'
      Default = True
      TabOrder = 0
      OnClick = btnOKClick
    end
    object btnCancel: TButton
      Left = 335
      Top = 8
      Width = 75
      Height = 25
      Cancel = True
      Caption = '取消'
      TabOrder = 1
      OnClick = btnCancelClick
    end
    object btnApply: TButton
      Left = 416
      Top = 8
      Width = 75
      Height = 25
      Caption = '应用'
      TabOrder = 2
      OnClick = btnApplyClick
    end
  end
end
