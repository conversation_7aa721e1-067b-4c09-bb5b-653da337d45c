unit DBConnection;

interface

uses
  System.SysUtils, System.Classes, Vcl.ComCtrls, Data.DB,
  FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error, FireDAC.UI.Intf,
  FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool, FireDAC.Stan.Async,
  FireDAC.Phys, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs,
  FireDAC.VCLUI.Wait, FireDAC.Comp.Client;

type
  TDBConnectionManager = class
  private
    FConnection: TFDConnection;
    FTreeView: TTreeView;
    procedure LoadTables;
    procedure LoadViews;
    procedure LoadIndices;
    procedure LoadTriggers;
    function ExecuteQuery(const ASQL: string): TFDQuery;
  public
    constructor Create(AConnection: TFDConnection; ATreeView: TTreeView);
    destructor Destroy; override;
    procedure RefreshDatabaseObjects;
    function GetTableStructure(const ATableName: string): TFDQuery;
    function GetTableData(const ATableName: string): TFDQuery;
    function ExecuteSQL(const ASQL: string): TFDQuery;
    function GetDatabaseInfo: TStringList;
  end;

implementation

constructor TDBConnectionManager.Create(AConnection: TFDConnection; ATreeView: TTreeView);
begin
  FConnection := AConnection;
  FTreeView := ATreeView;
end;

destructor TDBConnectionManager.Destroy;
begin
  inherited;
end;

procedure TDBConnectionManager.RefreshDatabaseObjects;
var
  RootNode, TablesNode, ViewsNode, IndicesNode, TriggersNode: TTreeNode;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  FTreeView.Items.BeginUpdate;
  try
    FTreeView.Items.Clear;

    RootNode := FTreeView.Items.Add(nil, ExtractFileName(FConnection.Params.Database));

    TablesNode := FTreeView.Items.AddChild(RootNode, '��');
    ViewsNode := FTreeView.Items.AddChild(RootNode, '��ͼ');
    IndicesNode := FTreeView.Items.AddChild(RootNode, '����');
    TriggersNode := FTreeView.Items.AddChild(RootNode, '������');

    RootNode.Expand(False);

    LoadTables;
    LoadViews;
    LoadIndices;
    LoadTriggers;
  finally
    FTreeView.Items.EndUpdate;
  end;
end;

function TDBConnectionManager.ExecuteQuery(const ASQL: string): TFDQuery;
var
  Query: TFDQuery;
begin
  Query := TFDQuery.Create(nil);
  try
    Query.Connection := FConnection;
    Query.SQL.Text := ASQL;
    Query.Open;
    Result := Query;
  except
    on E: Exception do
    begin
      Query.Free;
      raise Exception.Create('ִ�в�ѯʧ��: ' + E.Message);
    end;
  end;
end;

procedure TDBConnectionManager.LoadTables;
var
  Query: TFDQuery;
  TablesNode: TTreeNode;
  TableNode: TTreeNode;
begin
  TablesNode := FTreeView.Items.GetFirstNode.GetFirstChild;

  Query := ExecuteQuery('SELECT name FROM sqlite_master WHERE type=''table'' AND name NOT LIKE ''sqlite_%'' ORDER BY name');
  try
    while not Query.Eof do
    begin
      TableNode := FTreeView.Items.AddChild(TablesNode, Query.FieldByName('name').AsString);
      TableNode.ImageIndex := 1; // ��ͼ������
      TableNode.SelectedIndex := 1;
      Query.Next;
    end;
    TablesNode.Expand(False);
  finally
    Query.Free;
  end;
end;

procedure TDBConnectionManager.LoadViews;
var
  Query: TFDQuery;
  ViewsNode: TTreeNode;
  ViewNode: TTreeNode;
begin
  ViewsNode := FTreeView.Items.GetFirstNode.GetFirstChild.GetNextSibling;

  Query := ExecuteQuery('SELECT name FROM sqlite_master WHERE type=''view'' ORDER BY name');
  try
    while not Query.Eof do
    begin
      ViewNode := FTreeView.Items.AddChild(ViewsNode, Query.FieldByName('name').AsString);
      ViewNode.ImageIndex := 2; // ��ͼͼ������
      ViewNode.SelectedIndex := 2;
      Query.Next;
    end;
    ViewsNode.Expand(False);
  finally
    Query.Free;
  end;
end;

procedure TDBConnectionManager.LoadIndices;
var
  Query: TFDQuery;
  IndicesNode: TTreeNode;
  IndexNode: TTreeNode;
begin
  IndicesNode := FTreeView.Items.GetFirstNode.GetFirstChild.GetNextSibling.GetNextSibling;

  Query := ExecuteQuery('SELECT name FROM sqlite_master WHERE type=''index'' AND name NOT LIKE ''sqlite_%'' ORDER BY name');
  try
    while not Query.Eof do
    begin
      IndexNode := FTreeView.Items.AddChild(IndicesNode, Query.FieldByName('name').AsString);
      IndexNode.ImageIndex := 3; // ����ͼ������
      IndexNode.SelectedIndex := 3;
      Query.Next;
    end;
    IndicesNode.Expand(False);
  finally
    Query.Free;
  end;
end;

procedure TDBConnectionManager.LoadTriggers;
var
  Query: TFDQuery;
  TriggersNode: TTreeNode;
  TriggerNode: TTreeNode;
begin
  TriggersNode := FTreeView.Items.GetFirstNode.GetFirstChild.GetNextSibling.GetNextSibling.GetNextSibling;

  Query := ExecuteQuery('SELECT name FROM sqlite_master WHERE type=''trigger'' ORDER BY name');
  try
    while not Query.Eof do
    begin
      TriggerNode := FTreeView.Items.AddChild(TriggersNode, Query.FieldByName('name').AsString);
      TriggerNode.ImageIndex := 4; // ������ͼ������
      TriggerNode.SelectedIndex := 4;
      Query.Next;
    end;
    TriggersNode.Expand(False);
  finally
    Query.Free;
  end;
end;

function TDBConnectionManager.GetTableStructure(const ATableName: string): TFDQuery;
begin
  Result := ExecuteQuery(Format('PRAGMA table_info(%s)', [ATableName]));
end;

function TDBConnectionManager.GetTableData(const ATableName: string): TFDQuery;
begin
  Result := ExecuteQuery(Format('SELECT * FROM %s LIMIT 1000', [ATableName]));
end;

function TDBConnectionManager.ExecuteSQL(const ASQL: string): TFDQuery;
begin
  Result := ExecuteQuery(ASQL);
end;

function TDBConnectionManager.GetDatabaseInfo: TStringList;
var
  Query: TFDQuery;
  Info: TStringList;
begin
  Info := TStringList.Create;

  Info.Add('���ݿ��ļ�: ' + FConnection.Params.Database);

  Query := ExecuteQuery('PRAGMA database_list');
  try
    while not Query.Eof do
    begin
      Info.Add(Format('���ݿ� #%d: %s (%s)', [
        Query.FieldByName('seq').AsInteger,
        Query.FieldByName('name').AsString,
        Query.FieldByName('file').AsString
      ]));
      Query.Next;
    end;
  finally
    Query.Free;
  end;

  Query := ExecuteQuery('PRAGMA compile_options');
  try
    Info.Add('');
    Info.Add('����ѡ��:');
    while not Query.Eof do
    begin
      Info.Add('- ' + Query.Fields[0].AsString);
      Query.Next;
    end;
  finally
    Query.Free;
  end;

  Result := Info;
end;

end.
