object frmStructureManager: TfrmStructureManager
  Left = 0
  Top = 0
  Caption = '数据库结构管理器'
  ClientHeight = 500
  ClientWidth = 700
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poScreenCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  TextHeight = 15
  object pnlMain: TPanel
    Left = 0
    Top = 26
    Width = 700
    Height = 474
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    object pcMain: TPageControl
      Left = 0
      Top = 0
      Width = 700
      Height = 474
      ActivePage = tsTable
      Align = alClient
      TabOrder = 0
      object tsTable: TTabSheet
        Caption = '表'
        object pnlTableTop: TPanel
          Left = 0
          Top = 0
          Width = 692
          Height = 41
          Align = alTop
          BevelOuter = bvNone
          TabOrder = 0
          object lblTableName: TLabel
            Left = 8
            Top = 14
            Width = 24
            Height = 15
            Caption = '表名:'
          end
          object edtTableName: TEdit
            Left = 38
            Top = 10
            Width = 200
            Height = 23
            TabOrder = 0
          end
        end
        object sgColumns: TStringGrid
          Left = 0
          Top = 41
          Width = 692
          Height = 364
          Align = alClient
          Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goEditing]
          TabOrder = 1
        end
        object pnlTableButtons: TPanel
          Left = 0
          Top = 405
          Width = 692
          Height = 41
          Align = alBottom
          BevelOuter = bvNone
          TabOrder = 2
          object btnAddColumn: TButton
            Left = 8
            Top = 8
            Width = 75
            Height = 25
            Caption = '添加列'
            TabOrder = 0
            OnClick = btnAddColumnClick
          end
          object btnRemoveColumn: TButton
            Left = 89
            Top = 8
            Width = 75
            Height = 25
            Caption = '删除列'
            TabOrder = 1
            OnClick = btnRemoveColumnClick
          end
          object btnCreateTable: TButton
            Left = 609
            Top = 8
            Width = 75
            Height = 25
            Action = actCreateTable
            TabOrder = 2
          end
        end
      end
      object tsIndex: TTabSheet
        Caption = '索引'
        ImageIndex = 1
        object pnlIndexTop: TPanel
          Left = 0
          Top = 0
          Width = 692
          Height = 73
          Align = alTop
          BevelOuter = bvNone
          TabOrder = 0
          object lblIndexName: TLabel
            Left = 8
            Top = 14
            Width = 48
            Height = 15
            Caption = '索引名称:'
          end
          object lblIndexTable: TLabel
            Left = 8
            Top = 43
            Width = 24
            Height = 15
            Caption = '表名:'
          end
          object edtIndexName: TEdit
            Left = 62
            Top = 10
            Width = 200
            Height = 23
            TabOrder = 0
          end
          object cmbIndexTable: TComboBox
            Left = 62
            Top = 39
            Width = 200
            Height = 23
            Style = csDropDownList
            TabOrder = 1
          end
          object chkUnique: TCheckBox
            Left = 280
            Top = 12
            Width = 97
            Height = 17
            Caption = '唯一索引'
            TabOrder = 2
          end
        end
        object sgIndexColumns: TStringGrid
          Left = 0
          Top = 73
          Width = 692
          Height = 332
          Align = alClient
          Options = [goFixedVertLine, goFixedHorzLine, goVertLine, goHorzLine, goRangeSelect, goEditing]
          TabOrder = 1
        end
        object pnlIndexButtons: TPanel
          Left = 0
          Top = 405
          Width = 692
          Height = 41
          Align = alBottom
          BevelOuter = bvNone
          TabOrder = 2
          object btnAddIndexColumn: TButton
            Left = 8
            Top = 8
            Width = 75
            Height = 25
            Caption = '添加列'
            TabOrder = 0
            OnClick = btnAddIndexColumnClick
          end
          object btnRemoveIndexColumn: TButton
            Left = 89
            Top = 8
            Width = 75
            Height = 25
            Caption = '删除列'
            TabOrder = 1
            OnClick = btnRemoveIndexColumnClick
          end
          object btnCreateIndex: TButton
            Left = 609
            Top = 8
            Width = 75
            Height = 25
            Action = actCreateIndex
            TabOrder = 2
          end
        end
      end
      object tsTrigger: TTabSheet
        Caption = '触发器'
        ImageIndex = 2
        object pnlTriggerTop: TPanel
          Left = 0
          Top = 0
          Width = 692
          Height = 105
          Align = alTop
          BevelOuter = bvNone
          TabOrder = 0
          object lblTriggerName: TLabel
            Left = 8
            Top = 14
            Width = 60
            Height = 15
            Caption = '触发器名称:'
          end
          object lblTriggerTable: TLabel
            Left = 8
            Top = 43
            Width = 24
            Height = 15
            Caption = '表名:'
          end
          object lblTriggerTime: TLabel
            Left = 8
            Top = 72
            Width = 60
            Height = 15
            Caption = '触发器时间:'
          end
          object lblTriggerEvent: TLabel
            Left = 280
            Top = 72
            Width = 60
            Height = 15
            Caption = '触发器事件:'
          end
          object edtTriggerName: TEdit
            Left = 74
            Top = 10
            Width = 200
            Height = 23
            TabOrder = 0
          end
          object cmbTriggerTable: TComboBox
            Left = 74
            Top = 39
            Width = 200
            Height = 23
            Style = csDropDownList
            TabOrder = 1
          end
          object cmbTriggerTime: TComboBox
            Left = 74
            Top = 68
            Width = 200
            Height = 23
            Style = csDropDownList
            TabOrder = 2
          end
          object cmbTriggerEvent: TComboBox
            Left = 346
            Top = 68
            Width = 200
            Height = 23
            Style = csDropDownList
            TabOrder = 3
          end
        end
        object memTriggerBody: TMemo
          Left = 0
          Top = 105
          Width = 692
          Height = 300
          Align = alClient
          Font.Charset = DEFAULT_CHARSET
          Font.Color = clWindowText
          Font.Height = -13
          Font.Name = 'Consolas'
          Font.Style = []
          ParentFont = False
          ScrollBars = ssBoth
          TabOrder = 1
        end
        object pnlTriggerButtons: TPanel
          Left = 0
          Top = 405
          Width = 692
          Height = 41
          Align = alBottom
          BevelOuter = bvNone
          TabOrder = 2
          object btnCreateTriggerPanel: TButton
            Left = 609
            Top = 8
            Width = 75
            Height = 25
            Action = actCreateTrigger
            TabOrder = 0
          end
        end
      end
    end
  end
  object tbMain: TToolBar
    Left = 0
    Top = 0
    Width = 700
    Height = 26
    Caption = 'tbMain'
    TabOrder = 1
    object btnCreateTableTool: TToolButton
      Left = 0
      Top = 0
      Action = actCreateTable
    end
    object btnCreateIndexTool: TToolButton
      Left = 23
      Top = 0
      Action = actCreateIndex
    end
    object btnCreateTriggerTool: TToolButton
      Left = 46
      Top = 0
      Action = actCreateTrigger
    end
    object btnSep1: TToolButton
      Left = 69
      Top = 0
      Width = 8
      Caption = 'btnSep1'
      ImageIndex = 3
      Style = tbsSeparator
    end
    object btnDropTable: TToolButton
      Left = 77
      Top = 0
      Action = actDropTable
    end
    object btnDropIndex: TToolButton
      Left = 100
      Top = 0
      Action = actDropIndex
    end
    object btnDropTrigger: TToolButton
      Left = 123
      Top = 0
      Action = actDropTrigger
    end
  end
  object alMain: TActionList
    Left = 40
    Top = 80
    object actCreateTable: TAction
      Caption = '创建表'
      OnExecute = actCreateTableExecute
    end
    object actCreateIndex: TAction
      Caption = '创建索引'
      OnExecute = actCreateIndexExecute
    end
    object actCreateTrigger: TAction
      Caption = '创建触发器'
      OnExecute = actCreateTriggerExecute
    end
    object actDropTable: TAction
      Caption = '删除表'
      OnExecute = actDropTableExecute
    end
    object actDropIndex: TAction
      Caption = '删除索引'
      OnExecute = actDropIndexExecute
    end
    object actDropTrigger: TAction
      Caption = '删除触发器'
      OnExecute = actDropTriggerExecute
    end
  end
end
