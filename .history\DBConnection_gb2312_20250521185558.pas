unit DBConnection;

interface

uses
  System.SysUtils, System.Classes, Vcl.ComCtrls, Data.DB, System.Variants, System.TypInfo,
  FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error, FireDAC.UI.Intf,
  FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool, FireDAC.Stan.Async,
  FireDAC.Phys, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs,
  FireDAC.VCLUI.Wait, FireDAC.Comp.Client, FireDAC.Phys.ODBC, FireDAC.Phys.ODBCDef,
  Data.Win.ADODB, System.Win.ComObj;

type
  TDBType = (dbtSQLite, dbtAccess, dbtParadox);

  TDBConnectionManager = class
  private
    FConnection: TFDConnection;
    FTreeView: TTreeView;
    FDBType: TDBType;
    FConnectionInfo: string;
    function ExecuteQuery(const ASQL: string): TFDQuery;
    function IsSQLite: Boolean;
    function IsAccess: Boolean;
    function IsParadox: Boolean;
    function GetAccessTableNames: TStringList;
    function GetAccessViewNames: TStringList;
    function GetAccessIndexNames(const ATableName: string): TStringList;
    function GetParadoxTableNames: TStringList;
    function GetParadoxViewNames: TStringList;
    function GetParadoxIndexNames(const ATableName: string): TStringList;
  public
    constructor Create(AConnection: TFDConnection; ATreeView: TTreeView);
    destructor Destroy; override;
    procedure LoadTables;
    procedure LoadViews;
    procedure LoadIndices;
    procedure LoadTriggers;
    function GetTableStructure(const ATableName: string): TFDQuery;
    function GetTableData(const ATableName: string): TFDQuery;
    function ExecuteSQL(const ASQL: string): TFDQuery;
    function GetDatabaseInfo: TStringList;
    function GetConnectionInfo: string;
    property DBType: TDBType read FDBType;
  end;

implementation

constructor TDBConnectionManager.Create(AConnection: TFDConnection; ATreeView: TTreeView);
var
  DriverName: string;
  ConnectionString: string;
begin
  FConnection := AConnection;
  FTreeView := ATreeView;
  FConnectionInfo := '';

  // 分析连接参数确定数据库类型
  if Assigned(FConnection) then
  begin
    if FConnection.Params.DriverID = 'SQLite' then
    begin
      FDBType := dbtSQLite;
      FConnectionInfo := '连接方式: FireDAC SQLite 直接连接';
    end
    else if FConnection.Params.DriverID = 'MSAcc' then
    begin
      FDBType := dbtAccess;
      FConnectionInfo := '连接方式: FireDAC MSAcc 直接连接 (Microsoft Access)';
    end
    else if FConnection.Params.DriverID = 'ODBC' then
    begin
      // 检查ODBC驱动程序名称或连接字符串确定是Access还是Paradox
      DriverName := FConnection.Params.Values['DriverName'];
      ConnectionString := FConnection.Params.Values['ConnectionString'];

      if (Pos('Access', DriverName) > 0) or (Pos('*.mdb', DriverName) > 0) or (Pos('*.accdb', DriverName) > 0) then
      begin
        FDBType := dbtAccess;
        FConnectionInfo := '连接方式: ODBC 驱动 (Microsoft Access)';
      end
      else if (Pos('Paradox', DriverName) > 0) or (Pos('*.db', DriverName) > 0) then
      begin
        FDBType := dbtParadox;
        FConnectionInfo := '连接方式: ODBC 驱动 (Paradox)';
      end
      // 检查连接字符串是否包含Paradox相关信息
      else if (Pos('Paradox', ConnectionString) > 0) or (Pos('*.db', ConnectionString) > 0) then
      begin
        FDBType := dbtParadox;
        FConnectionInfo := '连接方式: ODBC 驱动 (Paradox)';
      end
      else
      begin
        FDBType := dbtSQLite; // 默认为SQLite
        FConnectionInfo := '连接方式: ODBC 驱动 (SQLite)';
      end;
    end
    else
    begin
      FDBType := dbtSQLite; // 默认为SQLite
      FConnectionInfo := '连接方式: 未知驱动';
    end;
  end
  else
  begin
    FDBType := dbtSQLite; // 默认为SQLite
    FConnectionInfo := '连接方式: 未连接';
  end;
end;
