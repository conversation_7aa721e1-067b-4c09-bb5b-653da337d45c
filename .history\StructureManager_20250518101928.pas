unit StructureManager;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  System.StrUtils, System.UITypes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, Vcl.ComCtrls,
  Vcl.StdCtrls, Vcl.Grids, Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Error, FireDAC.UI.Intf, FireDAC.Phys.Intf, FireDAC.Stan.Def,
  FireDAC.Stan.Pool, FireDAC.Stan.Async, FireDAC.Phys, FireDAC.Phys.SQLite,
  FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs, FireDAC.VCLUI.Wait,
  FireDAC.Comp.Client, FireDAC.Comp.DataSet, System.Actions, Vcl.ActnList,
  Vcl.<PERSON>, <PERSON>cl<PERSON>, Vcl.Menus;

type
  TfrmStructureManager = class(TForm)
    pnlMain: TPanel;
    pcMain: TPageControl;
    tsTable: TTabSheet;
    tsIndex: TTabSheet;
    tsTrigger: TTabSheet;
    pnlTableTop: TPanel;
    lblTableName: TLabel;
    edtTableName: TEdit;
    sgColumns: TStringGrid;
    pnlTableButtons: TPanel;
    btnAddColumn: TButton;
    btnRemoveColumn: TButton;
    btnCreateTable: TButton;
    pnlIndexTop: TPanel;
    lblIndexName: TLabel;
    edtIndexName: TEdit;
    lblIndexTable: TLabel;
    cmbIndexTable: TComboBox;
    chkUnique: TCheckBox;
    sgIndexColumns: TStringGrid;
    pnlIndexButtons: TPanel;
    btnAddIndexColumn: TButton;
    btnRemoveIndexColumn: TButton;
    btnCreateIndexPanel: TButton;
    pnlTriggerTop: TPanel;
    lblTriggerName: TLabel;
    edtTriggerName: TEdit;
    lblTriggerTable: TLabel;
    cmbTriggerTable: TComboBox;
    lblTriggerTime: TLabel;
    cmbTriggerTime: TComboBox;
    lblTriggerEvent: TLabel;
    cmbTriggerEvent: TComboBox;
    memTriggerBody: TMemo;
    pnlTriggerButtons: TPanel;
    btnCreateTriggerPanel: TButton;
    alMain: TActionList;
    actCreateTable: TAction;
    actCreateIndex: TAction;
    actCreateTrigger: TAction;
    actDropTable: TAction;
    actDropIndex: TAction;
    actDropTrigger: TAction;
    tbMain: TToolBar;
    btnCreateTableTool: TToolButton;
    btnCreateIndexTool: TToolButton;
    btnCreateTriggerTool: TToolButton;
    btnSep1: TToolButton;
    btnDropTable: TToolButton;
    btnDropIndex: TToolButton;
    btnDropTrigger: TToolButton;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure actCreateTableExecute(Sender: TObject);
    procedure actCreateIndexExecute(Sender: TObject);
    procedure actCreateTriggerExecute(Sender: TObject);
    procedure actDropTableExecute(Sender: TObject);
    procedure actDropIndexExecute(Sender: TObject);
    procedure actDropTriggerExecute(Sender: TObject);
    procedure btnAddColumnClick(Sender: TObject);
    procedure btnRemoveColumnClick(Sender: TObject);
    procedure btnAddIndexColumnClick(Sender: TObject);
    procedure btnRemoveIndexColumnClick(Sender: TObject);
  private
    { Private declarations }
    FConnection: TFDConnection;

    procedure InitializeControls;
    procedure LoadTables;
    procedure ExecuteSQL(const ASQL: string);
  public
    { Public declarations }
    procedure Initialize(AConnection: TFDConnection);
  end;

var
  frmStructureManager: TfrmStructureManager;

implementation

{$R *.dfm}

procedure TfrmStructureManager.FormCreate(Sender: TObject);
begin
  InitializeControls;
end;

procedure TfrmStructureManager.FormDestroy(Sender: TObject);
begin
  // ������Դ
end;

procedure TfrmStructureManager.InitializeControls;
begin
  // ��ʼ��������
  with sgColumns do
  begin
    ColCount := 5;
    RowCount := 2;
    FixedRows := 1;

    Cells[0, 0] := '����';
    Cells[1, 0] := '��������';
    Cells[2, 0] := '����';
    Cells[3, 0] := '�ǿ�';
    Cells[4, 0] := 'Ĭ��ֵ';

    ColWidths[0] := 120;
    ColWidths[1] := 100;
    ColWidths[2] := 50;
    ColWidths[3] := 50;
    ColWidths[4] := 100;
  end;

  // ��ʼ�������б���
  with sgIndexColumns do
  begin
    ColCount := 3;
    RowCount := 2;
    FixedRows := 1;

    Cells[0, 0] := '����';
    Cells[1, 0] := '����';
    Cells[2, 0] := 'У�Թ���';

    ColWidths[0] := 120;
    ColWidths[1] := 80;
    ColWidths[2] := 100;
  end;

  // ��ʼ��������ʱ��������
  cmbTriggerTime.Items.Clear;
  cmbTriggerTime.Items.Add('BEFORE');
  cmbTriggerTime.Items.Add('AFTER');
  cmbTriggerTime.Items.Add('INSTEAD OF');
  cmbTriggerTime.ItemIndex := 0;

  // ��ʼ���������¼�������
  cmbTriggerEvent.Items.Clear;
  cmbTriggerEvent.Items.Add('INSERT');
  cmbTriggerEvent.Items.Add('UPDATE');
  cmbTriggerEvent.Items.Add('DELETE');
  cmbTriggerEvent.ItemIndex := 0;
end;

procedure TfrmStructureManager.Initialize(AConnection: TFDConnection);
begin
  FConnection := AConnection;

  LoadTables;
end;

procedure TfrmStructureManager.LoadTables;
var
  Query: TFDQuery;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  cmbIndexTable.Items.Clear;
  cmbTriggerTable.Items.Clear;

  Query := TFDQuery.Create(nil);
  try
    Query.Connection := FConnection;
    Query.SQL.Text := 'SELECT name FROM sqlite_master WHERE type=''table'' AND name NOT LIKE ''sqlite_%'' ORDER BY name';

    try
      Query.Open;

      while not Query.Eof do
      begin
        cmbIndexTable.Items.Add(Query.FieldByName('name').AsString);
        cmbTriggerTable.Items.Add(Query.FieldByName('name').AsString);
        Query.Next;
      end;

      if cmbIndexTable.Items.Count > 0 then
      begin
        cmbIndexTable.ItemIndex := 0;
        cmbTriggerTable.ItemIndex := 0;
      end;
    except
      on E: Exception do
      begin
        ShowMessage('���ر��б�ʧ��: ' + E.Message);
      end;
    end;
  finally
    Query.Free;
  end;
end;

procedure TfrmStructureManager.ExecuteSQL(const ASQL: string);
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  try
    FConnection.ExecSQL(ASQL);
    ShowMessage('SQLִ�гɹ�');
  except
    on E: Exception do
    begin
      ShowMessage('SQLִ��ʧ��: ' + E.Message);
    end;
  end;
end;

procedure TfrmStructureManager.btnAddColumnClick(Sender: TObject);
begin
  sgColumns.RowCount := sgColumns.RowCount + 1;
end;

procedure TfrmStructureManager.btnRemoveColumnClick(Sender: TObject);
begin
  if sgColumns.RowCount > 2 then
    sgColumns.RowCount := sgColumns.RowCount - 1;
end;

procedure TfrmStructureManager.btnAddIndexColumnClick(Sender: TObject);
begin
  sgIndexColumns.RowCount := sgIndexColumns.RowCount + 1;
end;

procedure TfrmStructureManager.btnRemoveIndexColumnClick(Sender: TObject);
begin
  if sgIndexColumns.RowCount > 2 then
    sgIndexColumns.RowCount := sgIndexColumns.RowCount - 1;
end;

procedure TfrmStructureManager.actCreateTableExecute(Sender: TObject);
var
  SQL, TableName, ColumnDefs: string;
  i: Integer;
begin
  TableName := Trim(edtTableName.Text);

  if TableName = '' then
  begin
    ShowMessage('���������');
    Exit;
  end;

  ColumnDefs := '';

  for i := 1 to sgColumns.RowCount - 1 do
  begin
    if Trim(sgColumns.Cells[0, i]) = '' then
      Continue;

    if ColumnDefs <> '' then
      ColumnDefs := ColumnDefs + ', ';

    ColumnDefs := ColumnDefs + Format('%s %s', [
      sgColumns.Cells[0, i],
      sgColumns.Cells[1, i]
    ]);

    if sgColumns.Cells[2, i] = 'Y' then
      ColumnDefs := ColumnDefs + ' PRIMARY KEY';

    if sgColumns.Cells[3, i] = 'Y' then
      ColumnDefs := ColumnDefs + ' NOT NULL';

    if sgColumns.Cells[4, i] <> '' then
      ColumnDefs := ColumnDefs + ' DEFAULT ' + sgColumns.Cells[4, i];
  end;

  if ColumnDefs = '' then
  begin
    ShowMessage('����������һ��');
    Exit;
  end;

  SQL := Format('CREATE TABLE %s (%s)', [TableName, ColumnDefs]);

  ExecuteSQL(SQL);
  LoadTables;
end;

procedure TfrmStructureManager.actCreateIndexExecute(Sender: TObject);
var
  SQL, IndexName, TableName, ColumnDefs: string;
  i: Integer;
  IsUnique: Boolean;
begin
  IndexName := Trim(edtIndexName.Text);
  TableName := cmbIndexTable.Text;
  IsUnique := chkUnique.Checked;

  if IndexName = '' then
  begin
    ShowMessage('������������');
    Exit;
  end;

  if TableName = '' then
  begin
    ShowMessage('��ѡ���');
    Exit;
  end;

  ColumnDefs := '';

  for i := 1 to sgIndexColumns.RowCount - 1 do
  begin
    if Trim(sgIndexColumns.Cells[0, i]) = '' then
      Continue;

    if ColumnDefs <> '' then
      ColumnDefs := ColumnDefs + ', ';

    ColumnDefs := ColumnDefs + sgIndexColumns.Cells[0, i];

    if sgIndexColumns.Cells[1, i] <> '' then
      ColumnDefs := ColumnDefs + ' ' + sgIndexColumns.Cells[1, i];

    if sgIndexColumns.Cells[2, i] <> '' then
      ColumnDefs := ColumnDefs + ' COLLATE ' + sgIndexColumns.Cells[2, i];
  end;

  if ColumnDefs = '' then
  begin
    ShowMessage('����������һ��');
    Exit;
  end;

  SQL := Format('CREATE %s INDEX %s ON %s (%s)', [
    IfThen(IsUnique, 'UNIQUE', ''),
    IndexName,
    TableName,
    ColumnDefs
  ]);

  ExecuteSQL(SQL);
end;

procedure TfrmStructureManager.actCreateTriggerExecute(Sender: TObject);
var
  SQL, TriggerName, TableName, TriggerTime, TriggerEvent, TriggerBody: string;
begin
  TriggerName := Trim(edtTriggerName.Text);
  TableName := cmbTriggerTable.Text;
  TriggerTime := cmbTriggerTime.Text;
  TriggerEvent := cmbTriggerEvent.Text;
  TriggerBody := Trim(memTriggerBody.Text);

  if TriggerName = '' then
  begin
    ShowMessage('�����봥������');
    Exit;
  end;

  if TableName = '' then
  begin
    ShowMessage('��ѡ���');
    Exit;
  end;

  if TriggerBody = '' then
  begin
    ShowMessage('�����봥��������');
    Exit;
  end;

  SQL := Format('CREATE TRIGGER %s %s %s ON %s BEGIN %s END;', [
    TriggerName,
    TriggerTime,
    TriggerEvent,
    TableName,
    TriggerBody
  ]);

  ExecuteSQL(SQL);
end;

procedure TfrmStructureManager.actDropTableExecute(Sender: TObject);
var
  TableName: string;
begin
  if cmbIndexTable.ItemIndex < 0 then
  begin
    ShowMessage('��ѡ��Ҫɾ���ı�');
    Exit;
  end;

  TableName := cmbIndexTable.Text;

  if MessageDlg(Format('ȷ��Ҫɾ���� %s �𣿴˲������ɳ�����', [TableName]),
    mtConfirmation, [mbYes, mbNo], 0) = mrYes then
  begin
    ExecuteSQL(Format('DROP TABLE %s', [TableName]));
    LoadTables;
  end;
end;

procedure TfrmStructureManager.actDropIndexExecute(Sender: TObject);
var
  IndexName: string;
begin
  IndexName := Trim(edtIndexName.Text);

  if IndexName = '' then
  begin
    ShowMessage('������Ҫɾ����������');
    Exit;
  end;

  if MessageDlg(Format('ȷ��Ҫɾ������ %s �𣿴˲������ɳ�����', [IndexName]),
    mtConfirmation, [mbYes, mbNo], 0) = mrYes then
  begin
    ExecuteSQL(Format('DROP INDEX %s', [IndexName]));
  end;
end;

procedure TfrmStructureManager.actDropTriggerExecute(Sender: TObject);
var
  TriggerName: string;
begin
  TriggerName := Trim(edtTriggerName.Text);

  if TriggerName = '' then
  begin
    ShowMessage('������Ҫɾ���Ĵ�������');
    Exit;
  end;

  if MessageDlg(Format('ȷ��Ҫɾ�������� %s �𣿴˲������ɳ�����', [TriggerName]),
    mtConfirmation, [mbYes, mbNo], 0) = mrYes then
  begin
    ExecuteSQL(Format('DROP TRIGGER %s', [TriggerName]));
  end;
end;

end.
