unit ParadoxDemo;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.Grids,
  Vcl.ExtCtrls, Vcl.Buttons, Vcl.ComCtrls, Data.DB, Vcl.DBGrids, ParadoxReader;

type
  TfrmParadoxDemo = class(TForm)
    pnlTop: TPanel;
    lblDirectory: TLabel;
    edtDirectory: TEdit;
    btnBrowse: TButton;
    lblTables: TLabel;
    lbTables: TListBox;
    pnlMain: TPanel;
    sgData: TStringGrid;
    pnlBottom: TPanel;
    btnSave: TButton;
    btnClose: TButton;
    dlgOpen: TOpenDialog;
    procedure FormCreate(Sender: TObject);
    procedure btnBrowseClick(Sender: TObject);
    procedure lbTablesClick(Sender: TObject);
    procedure sgDataSetEditText(Sender: TObject; ACol, ARow: Integer; const Value: string);
    procedure btnSaveClick(Sender: TObject);
    procedure btnCloseClick(Sender: TObject);
  private
    FParadoxReader: TParadoxReader;
    FCurrentTable: string;
    FModified: Boolean;
    procedure LoadTablesList(const Directory: string);
    procedure LoadTableData(const TableName: string);
    procedure ClearGrid;
    function GetCellValue(ACol, ARow: Integer): string;
    procedure SetCellValue(ACol, ARow: Integer; const Value: string);
  public
    destructor Destroy; override;
  end;

var
  frmParadoxDemo: TfrmParadoxDemo;

implementation

{$R *.dfm}

procedure TfrmParadoxDemo.FormCreate(Sender: TObject);
begin
  FParadoxReader := nil;
  FCurrentTable := '';
  FModified := False;
  
  // 初始化网格
  sgData.FixedRows := 1;
  sgData.FixedCols := 1;
  sgData.ColCount := 2;
  sgData.RowCount := 2;
  sgData.Cells[0, 0] := '#';
  sgData.Cells[1, 0] := '字段1';
  sgData.Cells[0, 1] := '1';
  sgData.Options := sgData.Options + [goEditing, goTabs, goRowSelect];
end;

destructor TfrmParadoxDemo.Destroy;
begin
  if Assigned(FParadoxReader) then
  begin
    if FModified then
      FParadoxReader.SaveChanges;
    FParadoxReader.Free;
  end;
  
  inherited;
end;

procedure TfrmParadoxDemo.btnBrowseClick(Sender: TObject);
begin
  if dlgOpen.Execute then
  begin
    edtDirectory.Text := ExtractFilePath(dlgOpen.FileName);
    LoadTablesList(edtDirectory.Text);
  end;
end;

procedure TfrmParadoxDemo.LoadTablesList(const Directory: string);
var
  SearchRec: TSearchRec;
  FileName: string;
begin
  lbTables.Clear;
  
  if FindFirst(Directory + '*.db', faAnyFile, SearchRec) = 0 then
  begin
    try
      repeat
        FileName := ChangeFileExt(SearchRec.Name, '');
        lbTables.Items.Add(FileName);
      until FindNext(SearchRec) <> 0;
    finally
      FindClose(SearchRec);
    end;
  end;
  
  if lbTables.Items.Count > 0 then
    lbTables.ItemIndex := 0
  else
    ClearGrid;
end;

procedure TfrmParadoxDemo.lbTablesClick(Sender: TObject);
begin
  if lbTables.ItemIndex >= 0 then
  begin
    // 如果有未保存的更改，提示保存
    if Assigned(FParadoxReader) and FModified then
    begin
      if MessageDlg('数据已修改，是否保存更改？', mtConfirmation, [mbYes, mbNo], 0) = mrYes then
        FParadoxReader.SaveChanges;
    end;
    
    // 释放之前的读取器
    FreeAndNil(FParadoxReader);
    FModified := False;
    
    // 加载选中的表
    FCurrentTable := lbTables.Items[lbTables.ItemIndex];
    LoadTableData(FCurrentTable);
  end;
end;

procedure TfrmParadoxDemo.LoadTableData(const TableName: string);
var
  TablePath: string;
  I, J: Integer;
  Record: TParadoxRecord;
  FieldValue: Variant;
begin
  ClearGrid;
  
  TablePath := IncludeTrailingPathDelimiter(edtDirectory.Text) + TableName + '.db';
  
  try
    FParadoxReader := TParadoxReader.Create(TablePath);
    
    // 设置网格列
    sgData.ColCount := FParadoxReader.FieldCount + 1;
    sgData.Cells[0, 0] := '#';
    
    // 设置字段名称
    for I := 0 to FParadoxReader.FieldCount - 1 do
      sgData.Cells[I + 1, 0] := FParadoxReader.GetFieldName(I);
    
    // 设置行数
    sgData.RowCount := FParadoxReader.RecordCount + 1;
    
    // 填充行号
    for I := 0 to FParadoxReader.RecordCount - 1 do
      sgData.Cells[0, I + 1] := IntToStr(I + 1);
    
    // 填充数据
    for I := 0 to FParadoxReader.RecordCount - 1 do
    begin
      Record := FParadoxReader.ReadRecord(I);
      try
        for J := 0 to FParadoxReader.FieldCount - 1 do
        begin
          FieldValue := Record.FieldValues[J];
          
          if VarIsNull(FieldValue) then
            sgData.Cells[J + 1, I + 1] := ''
          else
            sgData.Cells[J + 1, I + 1] := VarToStr(FieldValue);
        end;
      finally
        Record.Free;
      end;
    end;
    
    Caption := Format('Paradox 数据库查看器 - %s (%d 条记录)', [TableName, FParadoxReader.RecordCount]);
    btnSave.Enabled := True;
  except
    on E: Exception do
    begin
      ShowMessage('无法打开表: ' + E.Message);
      FreeAndNil(FParadoxReader);
      ClearGrid;
    end;
  end;
end;

procedure TfrmParadoxDemo.ClearGrid;
begin
  sgData.RowCount := 2;
  sgData.ColCount := 2;
  sgData.Cells[0, 0] := '#';
  sgData.Cells[1, 0] := '字段1';
  sgData.Cells[0, 1] := '1';
  sgData.Cells[1, 1] := '';
  
  Caption := 'Paradox 数据库查看器';
  btnSave.Enabled := False;
end;

function TfrmParadoxDemo.GetCellValue(ACol, ARow: Integer): string;
begin
  if (ACol >= 0) and (ACol < sgData.ColCount) and
     (ARow >= 0) and (ARow < sgData.RowCount) then
    Result := sgData.Cells[ACol, ARow]
  else
    Result := '';
end;

procedure TfrmParadoxDemo.SetCellValue(ACol, ARow: Integer; const Value: string);
begin
  if (ACol >= 0) and (ACol < sgData.ColCount) and
     (ARow >= 0) and (ARow < sgData.RowCount) then
    sgData.Cells[ACol, ARow] := Value;
end;

procedure TfrmParadoxDemo.sgDataSetEditText(Sender: TObject; ACol, ARow: Integer; const Value: string);
var
  Record: TParadoxRecord;
  FieldIndex: Integer;
begin
  if not Assigned(FParadoxReader) then
    Exit;
    
  if (ACol <= 0) or (ARow <= 0) then
    Exit;
    
  FieldIndex := ACol - 1;
  
  try
    Record := FParadoxReader.ReadRecord(ARow - 1);
    try
      Record.FieldValues[FieldIndex] := Value;
      FParadoxReader.WriteRecord(ARow - 1, Record);
      FModified := True;
    finally
      Record.Free;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('无法修改数据: ' + E.Message);
      // 恢复原始值
      sgData.Cells[ACol, ARow] := GetCellValue(ACol, ARow);
    end;
  end;
end;

procedure TfrmParadoxDemo.btnSaveClick(Sender: TObject);
begin
  if Assigned(FParadoxReader) and FModified then
  begin
    try
      FParadoxReader.SaveChanges;
      FModified := False;
      ShowMessage('数据已保存');
    except
      on E: Exception do
        ShowMessage('保存失败: ' + E.Message);
    end;
  end;
end;

procedure TfrmParadoxDemo.btnCloseClick(Sender: TObject);
begin
  Close;
end;

end.
