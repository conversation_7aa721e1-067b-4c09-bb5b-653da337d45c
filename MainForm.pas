unit MainForm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ComCtrls, Vcl.ExtCtr<PERSON>,
  Vcl.<PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, System.ImageList, Vcl.ImgList,
  Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error, FireDAC.UI.Intf,
  FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool, FireDAC.Stan.Async,
  FireDAC.Phys, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs,
  FireDAC.VCLUI.Wait, FireDAC.Comp.Client, System.Actions, Vcl.ActnList, Vcl.DBGrids,
  FireDAC.Comp.DataSet, System.Generics.Collections, DBConnection, System.IniFiles;

type
  TDatabaseInfo = class
  public
    Path: string;
    Name: string;
    DBType: DBConnection.TDBType;
    constructor Create(const APath, AName: string; ADBType: DBConnection.TDBType);
  end;
  TfrmMain = class(TForm)
    pnlMain: TPanel;
    sbMain: TStatusBar;
    mmMain: TMainMenu;
    miFile: TMenuItem;
    miConnect: TMenuItem;
    miDisconnect: TMenuItem;
    N1: TMenuItem;
    miExit: TMenuItem;
    miEdit: TMenuItem;
    miHelp: TMenuItem;
    miAbout: TMenuItem;
    tbMain: TToolBar;
    ilMain: TImageList;
    pnlLeft: TPanel;
    tvDatabases: TTreeView;
    splVertical: TSplitter;
    pcRight: TPageControl;
    tsData: TTabSheet;
    alMain: TActionList;
    actConnect: TAction;
    actDisconnect: TAction;
    actExit: TAction;
    btnConnect: TToolButton;
    btnDisconnect: TToolButton;
    dbgData: TDBGrid;
    dsData: TDataSource;
    pmTreeView: TPopupMenu;
    pmDBGrid: TPopupMenu;
    actExportData: TAction;
    actImportData: TAction;
    actRefreshData: TAction;
    actModifyData: TAction;
    actRefreshDB: TAction;
    actSearchDB: TAction;
    actConvertDB: TAction;
    miExportData: TMenuItem;
    miImportData: TMenuItem;
    miRefreshData: TMenuItem;
    miModifyData: TMenuItem;
    miRefreshDB: TMenuItem;
    miSearchDB: TMenuItem;
    miConvertDB: TMenuItem;
    btnExportData: TToolButton;
    btnImportData: TToolButton;
    btnRefreshData: TToolButton;
    btnModifyData: TToolButton;
    btnRefreshDB: TToolButton;
    btnSearchDB: TToolButton;
    btnConvertDB: TToolButton;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure actConnectExecute(Sender: TObject);
    procedure actDisconnectExecute(Sender: TObject);
    procedure actExitExecute(Sender: TObject);
    procedure tvDatabasesDblClick(Sender: TObject);
    procedure actExportDataExecute(Sender: TObject);
    procedure actImportDataExecute(Sender: TObject);
    procedure actRefreshDataExecute(Sender: TObject);
    procedure actModifyDataExecute(Sender: TObject);
    procedure actRefreshDBExecute(Sender: TObject);
    procedure actSearchDBExecute(Sender: TObject);
    procedure actConvertDBExecute(Sender: TObject);
    procedure tvDatabasesMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
    procedure dbgDataMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
  private
    { Private declarations }
    FConnection: TFDConnection;
    FCurrentQuery: TFDQuery;
    FCurrentTable: string;
    FDatabaseList: TObjectList<TDatabaseInfo>;
    FConfigFileName: string;
    procedure LoadIcons;
    procedure InitializeControls;
    procedure DisconnectFromDatabase;
    procedure LoadDatabaseStructure;
    procedure LoadTablesIntoDirectories(DBManager: DBConnection.TDBConnectionManager; RootNode: TTreeNode; const DBPath: string);
    procedure LoadTableData(const ATableName: string);
    procedure SaveDatabaseList;
    procedure LoadDatabaseList;
    procedure AddDatabaseToList(const APath, AName: string; ADBType: DBConnection.TDBType);
    procedure ConnectToDatabase(const APath: string; ADBType: DBConnection.TDBType);
    function FindMirServerDir(const StartDir: string): string;
    function AutoDetectDatabase(const AGameDir: string): Boolean;
    procedure RefreshDatabaseNode(Node: TTreeNode);
    procedure ExportTableData(const ATableName: string);
    procedure ImportTableData(const ATableName: string);
    procedure ModifyTableData(const ATableName: string);
    procedure SearchDatabase(const SearchText: string);
    procedure ConvertDatabase(const SourcePath, TargetPath: string; SourceType, TargetType: DBConnection.TDBType);
    function ReadIniValue(const FileName, Section, Key, DefaultValue: string): string;
  public
    { Public declarations }
  end;

var
  frmMain: TfrmMain;

implementation

{$R *.dfm}

uses
  ConnectionDialog;

constructor TDatabaseInfo.Create(const APath, AName: string; ADBType: DBConnection.TDBType);
begin
  inherited Create;
  Path := APath;
  Name := AName;
  DBType := ADBType;
end;

procedure TfrmMain.FormCreate(Sender: TObject);
begin
  // 创建新的Action
  if not Assigned(actExportData) then
  begin
    actExportData := TAction.Create(alMain);
    actExportData.Caption := '导出数据(&E)';
    actExportData.OnExecute := actExportDataExecute;
    actExportData.ActionList := alMain;
  end;

  if not Assigned(actImportData) then
  begin
    actImportData := TAction.Create(alMain);
    actImportData.Caption := '导入数据(&I)';
    actImportData.OnExecute := actImportDataExecute;
    actImportData.ActionList := alMain;
  end;

  if not Assigned(actRefreshData) then
  begin
    actRefreshData := TAction.Create(alMain);
    actRefreshData.Caption := '刷新数据(&R)';
    actRefreshData.OnExecute := actRefreshDataExecute;
    actRefreshData.ActionList := alMain;
  end;

  if not Assigned(actModifyData) then
  begin
    actModifyData := TAction.Create(alMain);
    actModifyData.Caption := '修改数据(&M)';
    actModifyData.OnExecute := actModifyDataExecute;
    actModifyData.ActionList := alMain;
  end;

  if not Assigned(actRefreshDB) then
  begin
    actRefreshDB := TAction.Create(alMain);
    actRefreshDB.Caption := '刷新数据库(&F)';
    actRefreshDB.OnExecute := actRefreshDBExecute;
    actRefreshDB.ActionList := alMain;
  end;

  if not Assigned(actSearchDB) then
  begin
    actSearchDB := TAction.Create(alMain);
    actSearchDB.Caption := '搜索数据库(&S)';
    actSearchDB.OnExecute := actSearchDBExecute;
    actSearchDB.ActionList := alMain;
  end;

  if not Assigned(actConvertDB) then
  begin
    actConvertDB := TAction.Create(alMain);
    actConvertDB.Caption := '转换数据库(&C)';
    actConvertDB.OnExecute := actConvertDBExecute;
    actConvertDB.ActionList := alMain;
  end;

  // 创建弹出菜单
  if not Assigned(pmTreeView) then
    pmTreeView := TPopupMenu.Create(Self);

  if not Assigned(pmDBGrid) then
    pmDBGrid := TPopupMenu.Create(Self);

  // 创建工具栏按钮
  if not Assigned(btnExportData) then
  begin
    btnExportData := TToolButton.Create(tbMain);
    btnExportData.Parent := tbMain;
    btnExportData.Action := actExportData;
  end;

  if not Assigned(btnImportData) then
  begin
    btnImportData := TToolButton.Create(tbMain);
    btnImportData.Parent := tbMain;
    btnImportData.Action := actImportData;
  end;

  if not Assigned(btnRefreshData) then
  begin
    btnRefreshData := TToolButton.Create(tbMain);
    btnRefreshData.Parent := tbMain;
    btnRefreshData.Action := actRefreshData;
  end;

  if not Assigned(btnModifyData) then
  begin
    btnModifyData := TToolButton.Create(tbMain);
    btnModifyData.Parent := tbMain;
    btnModifyData.Action := actModifyData;
  end;

  if not Assigned(btnRefreshDB) then
  begin
    btnRefreshDB := TToolButton.Create(tbMain);
    btnRefreshDB.Parent := tbMain;
    btnRefreshDB.Action := actRefreshDB;
  end;

  if not Assigned(btnSearchDB) then
  begin
    btnSearchDB := TToolButton.Create(tbMain);
    btnSearchDB.Parent := tbMain;
    btnSearchDB.Action := actSearchDB;
  end;

  if not Assigned(btnConvertDB) then
  begin
    btnConvertDB := TToolButton.Create(tbMain);
    btnConvertDB.Parent := tbMain;
    btnConvertDB.Action := actConvertDB;
  end;

  InitializeControls;

  // 创建数据库列表
  FDatabaseList := TObjectList<TDatabaseInfo>.Create(True); // True表示自动释放对象

  // 设置配置文件名
  FConfigFileName := ChangeFileExt(Application.ExeName, '.ini');

  // 加载数据库列表
  LoadDatabaseList;

  // 创建连接对象
  FConnection := TFDConnection.Create(Self);
  FConnection.Params.DriverID := 'SQLite';

  // 创建查询对象
  FCurrentQuery := TFDQuery.Create(Self);
  dsData.DataSet := FCurrentQuery;

  // 加载图标
  LoadIcons;
end;

procedure TfrmMain.FormDestroy(Sender: TObject);
begin
  // 保存数据库列表
  SaveDatabaseList;

  if FConnection.Connected then
    FConnection.Close;

  // 释放查询对象
  if Assigned(FCurrentQuery) then
    FCurrentQuery.Free;

  // 释放数据库列表
  FDatabaseList.Free;

  FConnection.Free;
end;

procedure TfrmMain.InitializeControls;
begin
  pcRight.ActivePage := tsData;
  actDisconnect.Enabled := False;

  // 初始化Action
  actExportData.Enabled := False;
  actImportData.Enabled := False;
  actRefreshData.Enabled := False;
  actModifyData.Enabled := False;
  actRefreshDB.Enabled := False;
  actSearchDB.Enabled := False;
  actConvertDB.Enabled := False;

  // 设置Action图标索引
  actExportData.ImageIndex := 1; // 表数据导出图标
  actImportData.ImageIndex := 3; // 表数据导入图标
  actRefreshData.ImageIndex := 4; // 表数据刷新图标
  actModifyData.ImageIndex := 5; // 表数据修改图标
  actRefreshDB.ImageIndex := 2; // 数据库刷新图标
  actSearchDB.ImageIndex := 8; // 数据库搜索图标
  actConvertDB.ImageIndex := 9; // 数据库转换图标

  // 设置Action提示
  actExportData.Hint := '导出表数据';
  actImportData.Hint := '导入表数据';
  actRefreshData.Hint := '刷新表数据';
  actModifyData.Hint := '修改表数据';
  actRefreshDB.Hint := '刷新数据库';
  actSearchDB.Hint := '搜索数据库';
  actConvertDB.Hint := '转换数据库';

  // 初始化TreeView右键菜单
  if not Assigned(pmTreeView) then
    pmTreeView := TPopupMenu.Create(Self);

  // 添加菜单项
  if not Assigned(miExportData) then
  begin
    miExportData := TMenuItem.Create(pmTreeView);
    miExportData.Action := actExportData;
    pmTreeView.Items.Add(miExportData);
  end;

  if not Assigned(miImportData) then
  begin
    miImportData := TMenuItem.Create(pmTreeView);
    miImportData.Action := actImportData;
    pmTreeView.Items.Add(miImportData);
  end;

  if not Assigned(miRefreshData) then
  begin
    miRefreshData := TMenuItem.Create(pmTreeView);
    miRefreshData.Action := actRefreshData;
    pmTreeView.Items.Add(miRefreshData);
  end;

  if not Assigned(miModifyData) then
  begin
    miModifyData := TMenuItem.Create(pmTreeView);
    miModifyData.Action := actModifyData;
    pmTreeView.Items.Add(miModifyData);
  end;

  if not Assigned(miRefreshDB) then
  begin
    miRefreshDB := TMenuItem.Create(pmTreeView);
    miRefreshDB.Action := actRefreshDB;
    pmTreeView.Items.Add(miRefreshDB);
  end;

  if not Assigned(miSearchDB) then
  begin
    miSearchDB := TMenuItem.Create(pmTreeView);
    miSearchDB.Action := actSearchDB;
    pmTreeView.Items.Add(miSearchDB);
  end;

  if not Assigned(miConvertDB) then
  begin
    miConvertDB := TMenuItem.Create(pmTreeView);
    miConvertDB.Action := actConvertDB;
    pmTreeView.Items.Add(miConvertDB);
  end;

  // 设置TreeView事件处理
  tvDatabases.PopupMenu := pmTreeView;
  tvDatabases.OnMouseDown := tvDatabasesMouseDown;

  // 设置DBGrid事件处理
  dbgData.PopupMenu := pmDBGrid;
  dbgData.OnMouseDown := dbgDataMouseDown;
end;

procedure TfrmMain.actConnectExecute(Sender: TObject);
var
  ConnectionDlg: TfrmConnectionDialog;
begin
  ConnectionDlg := TfrmConnectionDialog.Create(Self, FConnection);
  try
    if ConnectionDlg.Execute then
    begin
      // 连接成功，更新UI
      var DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
      try
        // 显示连接方式信息
        sbMain.SimpleText := '已连接到数据库: ' + FConnection.Params.Database + ' | 连接方式: ' + DBManager.GetConnectionInfo;
      finally
        DBManager.Free;
      end;

      // 更新Action状态
      actConnect.Enabled := False;
      actDisconnect.Enabled := True;
      actRefreshDB.Enabled := True;
      actSearchDB.Enabled := True;
      actConvertDB.Enabled := True;

      // 加载数据库结构
      LoadDatabaseStructure;
    end;
  finally
    ConnectionDlg.Free;
  end;
end;

procedure TfrmMain.actDisconnectExecute(Sender: TObject);
begin
  DisconnectFromDatabase;
end;

procedure TfrmMain.DisconnectFromDatabase;
var
  i: Integer;
begin
  if FConnection.Connected then
    FConnection.Close;

  sbMain.SimpleText := '已断开连接';

  // 更新Action状态
  actConnect.Enabled := True;
  actDisconnect.Enabled := False;
  actExportData.Enabled := False;
  actImportData.Enabled := False;
  actRefreshData.Enabled := False;
  actModifyData.Enabled := False;
  actRefreshDB.Enabled := False;
  actSearchDB.Enabled := False;
  actConvertDB.Enabled := False;

  // 清空当前表名
  FCurrentTable := '';

  // 清空当前数据库节点，但保留数据库列表节点
  for i := tvDatabases.Items.Count - 1 downto 0 do
  begin
    if (tvDatabases.Items[i].Level = 0) and (tvDatabases.Items[i].Text <> '数据库列表') then
    begin
      tvDatabases.Items[i].Delete;
    end;
  end;
end;

procedure TfrmMain.LoadDatabaseStructure;
var
  DBManager: DBConnection.TDBConnectionManager;
  RootNode: TTreeNode;
  DBPath, DBName: string;
begin
  // 清空树视图中的当前数据库节点
  for var i := tvDatabases.Items.Count - 1 downto 0 do
  begin
    if (tvDatabases.Items[i].Level = 0) and (tvDatabases.Items[i].Text <> '数据库列表') then
    begin
      tvDatabases.Items[i].Delete;
    end;
  end;

  if not FConnection.Connected then
    Exit;

  // 获取数据库路径
  DBPath := FConnection.Params.Database;
  if DBPath = '' then
    DBPath := FConnection.Params.Values['DefaultDir'];

  // 获取数据库名称（使用目录名）
  if DBPath <> '' then
  begin
    // 去除末尾的路径分隔符
    if (Length(DBPath) > 0) and (DBPath[Length(DBPath)] = PathDelim) then
      DBPath := Copy(DBPath, 1, Length(DBPath) - 1);

    // 获取最后一级目录名
    DBName := ExtractFileName(DBPath);
    if DBName = '' then
      DBName := DBPath;
  end
  else
    DBName := '数据库';

  // 创建根节点（使用数据库名称）
  RootNode := tvDatabases.Items.Add(nil, DBName);
  RootNode.ImageIndex := 0; // 数据库图标索引
  RootNode.SelectedIndex := 0;

  // 展开根节点
  RootNode.Expand(False);

  // 加载数据库对象
  DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
  try
    // 加载表并按目录组织
    LoadTablesIntoDirectories(DBManager, RootNode, DBPath);
  finally
    DBManager.Free;
  end;
end;

procedure TfrmMain.actExitExecute(Sender: TObject);
begin
  Close;
end;

procedure TfrmMain.LoadTableData(const ATableName: string);
var
  DBManager: DBConnection.TDBConnectionManager;
  IsSQLite: Boolean;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  try
    // 保存当前表名
    FCurrentTable := ATableName;

    // 设置页面标题
    tsData.Caption := '数据表: ' + ATableName;

    // 切换到数据页
    pcRight.ActivePage := tsData;

    // 关闭之前的查询
    if Assigned(FCurrentQuery) then
    begin
      FCurrentQuery.Close;

      // 设置字符串支持
      FCurrentQuery.Connection := FConnection;
      FCurrentQuery.FormatOptions.MapRules.Clear;
      FCurrentQuery.FormatOptions.OwnMapRules := True;
      FCurrentQuery.FormatOptions.StrsEmpty2Null := False;
      FCurrentQuery.FormatOptions.StrsTrim := False;

      // 根据数据库类型构造不同的SQL语句
      DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
      try
        IsSQLite := (DBManager.DBType = dbtSQLite);
      finally
        DBManager.Free;
      end;

      if IsSQLite then
        // SQLite使用双引号包围表名
        FCurrentQuery.SQL.Text := Format('SELECT * FROM "%s"', [ATableName])
      else
        // Access使用方括号包围表名
        FCurrentQuery.SQL.Text := Format('SELECT * FROM [%s]', [ATableName]);

      // 打开查询
      FCurrentQuery.Open;

      // 更新状态栏信息
      DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
      try
        sbMain.SimpleText := Format('已加载表 "%s"，共 %d 条记录 | 数据库: %s | 连接方式: %s',
          [ATableName, FCurrentQuery.RecordCount, FConnection.Params.Database, DBManager.GetConnectionInfo]);
      finally
        DBManager.Free;
      end;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('加载表数据失败: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.tvDatabasesDblClick(Sender: TObject);
var
  Node: TTreeNode;
  ObjectName: string;
  DBInfo: TDatabaseInfo;
begin
  Node := tvDatabases.Selected;
  if not Assigned(Node) then
    Exit;

  // 处理数据库节点（第1级节点）
  if (Node.Level = 1) and Assigned(Node.Data) then
  begin
    // 获取数据库信息
    DBInfo := TDatabaseInfo(Node.Data);

    // 连接到数据库
    ConnectToDatabase(DBInfo.Path, DBInfo.DBType);
    Exit;
  end;

  // 以下处理表节点（第2级节点）
  if not FConnection.Connected then
    Exit;

  if (Node.Level = 2) then
  begin
    // 获取表名
    ObjectName := Node.Text;

    // 保存当前表名
    FCurrentTable := ObjectName;

    // 启用表相关的Action
    actExportData.Enabled := True;
    actImportData.Enabled := True;
    actRefreshData.Enabled := True;
    actModifyData.Enabled := True;

    // 在右侧面板显示表数据
    LoadTableData(ObjectName);
  end;
end;

procedure TfrmMain.LoadTablesIntoDirectories(DBManager: DBConnection.TDBConnectionManager; RootNode: TTreeNode; const DBPath: string);
var
  Tables: TStringList;
  i: Integer;
  TableName: string;
  TableNode: TTreeNode;
  DBName: string;
begin
  // 获取表列表
  Tables := DBManager.GetTableNames;
  try
    if (Tables = nil) or (Tables.Count = 0) then
      Exit;

    // 按字母顺序排序表名
    Tables.Sort;

    // 获取数据库名称
    DBName := ExtractFileName(ExcludeTrailingPathDelimiter(DBPath));
    if DBName = '' then
      DBName := DBPath;

    // 将表列表添加到根节点下
    for i := 0 to Tables.Count - 1 do
    begin
      TableName := Tables[i];

      // 添加表节点
      TableNode := tvDatabases.Items.AddChild(RootNode, TableName);
      TableNode.ImageIndex := 1; // 表图标索引
      TableNode.SelectedIndex := 1;
    end;

    // 展开根节点
    RootNode.Expand(False);
  finally
    if Assigned(Tables) then
      Tables.Free;
  end;
end;

procedure TfrmMain.LoadIcons;
var
  Icon: TIcon;
  Bitmap: TBitmap;
  IconPath: string;
begin
  // 清空图标列表
  ilMain.Clear;

  // 获取图标文件夹路径
  IconPath := ExtractFilePath(Application.ExeName) + 'icon\';

  // 数据库图标 (索引 0)
  if FileExists(IconPath + 'database.bmp') then
  begin
    Bitmap := TBitmap.Create;
    try
      Bitmap.LoadFromFile(IconPath + 'database.bmp');
      ilMain.Add(Bitmap, nil);
    finally
      Bitmap.Free;
    end;
  end;

  // 导出数据图标 (索引 1)
  if FileExists(IconPath + 'export.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + 'export.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // 刷新数据库图标 (索引 2)
  if FileExists(IconPath + 'refresh.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + 'refresh.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // 导入数据图标 (索引 3)
  if FileExists(IconPath + 'import.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + 'import.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // 刷新表数据图标 (索引 4)
  if FileExists(IconPath + 'refresh_data.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + 'refresh_data.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // 修改表数据图标 (索引 5)
  if FileExists(IconPath + 'edit_data.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + 'edit_data.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // 连接数据库图标 (索引 6)
  if FileExists(IconPath + 'connect.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + 'connect.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // 断开数据库连接图标 (索引 7)
  if FileExists(IconPath + 'disconnect.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + 'disconnect.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // 搜索数据库图标 (索引 8)
  if FileExists(IconPath + 'search.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + 'search.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // 转换数据库图标 (索引 9)
  if FileExists(IconPath + 'convert.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + 'convert.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;
end;

procedure TfrmMain.SaveDatabaseList;
var
  IniFile: TIniFile;
  i: Integer;
begin
  if not Assigned(FDatabaseList) then
    Exit;

  IniFile := TIniFile.Create(FConfigFileName);
  try
    // 清除旧的数据库列表
    IniFile.EraseSection('DatabaseList');

    // 保存数据库数量
    IniFile.WriteInteger('DatabaseList', 'Count', FDatabaseList.Count);

    // 保存每个数据库的信息
    for i := 0 to FDatabaseList.Count - 1 do
    begin
      IniFile.WriteString('DatabaseList', Format('Path%d', [i]), FDatabaseList[i].Path);
      IniFile.WriteString('DatabaseList', Format('Name%d', [i]), FDatabaseList[i].Name);
      IniFile.WriteInteger('DatabaseList', Format('DBType%d', [i]), Ord(FDatabaseList[i].DBType));
    end;
  finally
    IniFile.Free;
  end;
end;

procedure TfrmMain.LoadDatabaseList;
var
  IniFile: TIniFile;
  Count, i, DBTypeValue: Integer;
  Path, Name: string;
  DBType: DBConnection.TDBType;
begin
  if not Assigned(FDatabaseList) then
    Exit;

  // 清空数据库列表
  FDatabaseList.Clear;

  IniFile := TIniFile.Create(FConfigFileName);
  try
    // 读取数据库数量
    Count := IniFile.ReadInteger('DatabaseList', 'Count', 0);

    // 读取每个数据库的信息
    for i := 0 to Count - 1 do
    begin
      Path := IniFile.ReadString('DatabaseList', Format('Path%d', [i]), '');
      Name := IniFile.ReadString('DatabaseList', Format('Name%d', [i]), '');
      DBTypeValue := IniFile.ReadInteger('DatabaseList', Format('DBType%d', [i]), 0);
      DBType := DBConnection.TDBType(DBTypeValue);

      // 添加到数据库列表
      if (Path <> '') and (Name <> '') then
        AddDatabaseToList(Path, Name, DBType);
    end;
  finally
    IniFile.Free;
  end;
end;

procedure TfrmMain.AddDatabaseToList(const APath, AName: string; ADBType: DBConnection.TDBType);
var
  DBInfo: TDatabaseInfo;
  i: Integer;
begin
  // 检查数据库是否已经存在于列表中
  for i := 0 to FDatabaseList.Count - 1 do
  begin
    if SameText(FDatabaseList[i].Path, APath) then
      Exit; // 已存在，不重复添加
  end;

  // 创建新的数据库信息对象并添加
  DBInfo := TDatabaseInfo.Create(APath, AName, ADBType);
  FDatabaseList.Add(DBInfo);
end;

procedure TfrmMain.ConnectToDatabase(const APath: string; ADBType: DBConnection.TDBType);
var
  DBManager: DBConnection.TDBConnectionManager;
  DBName: string;
  i: Integer;
  Found: Boolean;
  RootNode, DBNode: TTreeNode;
begin
  if not Assigned(FConnection) then
    Exit;

  if FConnection.Connected then
    FConnection.Close;

  case ADBType of
    dbtSQLite:
      begin
        FConnection.Params.Clear;
        FConnection.Params.DriverID := 'SQLite';
        FConnection.Params.Database := APath;
        // 设置字符串支持
        FConnection.FormatOptions.MapRules.Clear;
        FConnection.FormatOptions.OwnMapRules := True;
        FConnection.FormatOptions.StrsEmpty2Null := False;
        FConnection.FormatOptions.StrsTrim := False;
        // 使用gb2312编码正确显示中文字符
        FConnection.Params.Add('CharacterSet=gb2312');
      end;
    dbtAccess:
      begin
        FConnection.Params.Clear;
        FConnection.Params.DriverID := 'MSAcc';
        FConnection.Params.Database := APath;
        // 设置字符串支持
        FConnection.FormatOptions.MapRules.Clear;
        FConnection.FormatOptions.OwnMapRules := True;
        FConnection.FormatOptions.StrsEmpty2Null := False;
        FConnection.FormatOptions.StrsTrim := False;
      end;
    dbtParadox:
      begin
        FConnection.Params.Clear;
        FConnection.Params.DriverID := 'ODBC';
        FConnection.Params.Add('DefaultDir=' + APath);
        // 设置字符串支持
        FConnection.FormatOptions.MapRules.Clear;
        FConnection.FormatOptions.OwnMapRules := True;
        FConnection.FormatOptions.StrsEmpty2Null := False;
        FConnection.FormatOptions.StrsTrim := False;
      end;
  end;

  try
    FConnection.Open;

    // 连接成功，更新UI
    DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
    try
      // 显示连接方式信息
      sbMain.SimpleText := '已连接到数据库: ' + APath + ' | 连接方式: ' + DBManager.GetConnectionInfo;
    finally
      DBManager.Free;
    end;

    actConnect.Enabled := False;
    actDisconnect.Enabled := True;
    actRefreshDB.Enabled := True;
    actSearchDB.Enabled := True;
    actConvertDB.Enabled := True;

    // 加载数据库结构
    LoadDatabaseStructure;

    // 获取数据库名称
    DBName := ExtractFileName(APath);
    if DBName = '' then
      DBName := APath;

    // 添加到数据库列表（如果不存在）
    Found := False;
    for i := 0 to FDatabaseList.Count - 1 do
    begin
      if SameText(FDatabaseList[i].Path, APath) then
      begin
        Found := True;
        Break;
      end;
    end;

    if not Found then
    begin
      AddDatabaseToList(APath, DBName, ADBType);

      // 查找或创建根节点
      RootNode := nil;
      for i := 0 to tvDatabases.Items.Count - 1 do
      begin
        if (tvDatabases.Items[i].Level = 0) and (tvDatabases.Items[i].Text = '数据库列表') then
        begin
          RootNode := tvDatabases.Items[i];
          Break;
        end;
      end;

      if not Assigned(RootNode) then
      begin
        RootNode := tvDatabases.Items.Add(nil, '数据库列表');
        RootNode.ImageIndex := 0;
        RootNode.SelectedIndex := 0;
      end;

      // 添加数据库节点
      DBNode := tvDatabases.Items.AddChild(RootNode, DBName + ' (' + APath + ')');
      DBNode.ImageIndex := 0;
      DBNode.SelectedIndex := 0;
      DBNode.Data := Pointer(FDatabaseList[FDatabaseList.Count - 1]);

      // 展开根节点
      RootNode.Expand(False);

      // 保存数据库列表
      SaveDatabaseList;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('连接数据库失败: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.ExportTableData(const ATableName: string);
var
  SaveDialog: TSaveDialog;
  ExportQuery: TFDQuery;
  ExportFile: TextFile;
  i: Integer;
  FieldNames, FieldValues: string;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  SaveDialog := TSaveDialog.Create(nil);
  try
    SaveDialog.Title := '导出表数据';
    SaveDialog.DefaultExt := 'csv';
    SaveDialog.Filter := 'CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*';
    SaveDialog.FileName := ATableName + '.csv';

    if SaveDialog.Execute then
    begin
      ExportQuery := TFDQuery.Create(nil);
      try
        ExportQuery.Connection := FConnection;
        ExportQuery.SQL.Text := Format('SELECT * FROM "%s"', [ATableName]);
        ExportQuery.Open;

        AssignFile(ExportFile, SaveDialog.FileName);
        Rewrite(ExportFile);
        try
          // 写入字段名
          FieldNames := '';
          for i := 0 to ExportQuery.FieldCount - 1 do
          begin
            if i > 0 then
              FieldNames := FieldNames + ',';
            FieldNames := FieldNames + ExportQuery.Fields[i].FieldName;
          end;
          WriteLn(ExportFile, FieldNames);

          // 写入数据
          while not ExportQuery.Eof do
          begin
            FieldValues := '';
            for i := 0 to ExportQuery.FieldCount - 1 do
            begin
              if i > 0 then
                FieldValues := FieldValues + ',';
              FieldValues := FieldValues + ExportQuery.Fields[i].AsString;
            end;
            WriteLn(ExportFile, FieldValues);
            ExportQuery.Next;
          end;

          ShowMessage('数据已成功导出到文件: ' + SaveDialog.FileName);
        finally
          CloseFile(ExportFile);
        end;
      finally
        ExportQuery.Free;
      end;
    end;
  finally
    SaveDialog.Free;
  end;
end;

procedure TfrmMain.ImportTableData(const ATableName: string);
var
  OpenDialog: TOpenDialog;
  ImportFile: TextFile;
  Line, FieldName: string;
  FieldNames, FieldValues: TStringList;
  ImportQuery: TFDQuery;
  InsertSQL: string;
  i: Integer;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  OpenDialog := TOpenDialog.Create(nil);
  try
    OpenDialog.Title := '导入表数据';
    OpenDialog.DefaultExt := 'csv';
    OpenDialog.Filter := 'CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*';

    if OpenDialog.Execute then
    begin
      FieldNames := TStringList.Create;
      FieldValues := TStringList.Create;
      ImportQuery := TFDQuery.Create(nil);
      try
        ImportQuery.Connection := FConnection;

        // 读取CSV文件
        AssignFile(ImportFile, OpenDialog.FileName);
        Reset(ImportFile);
        try
          // 读取字段名
          if not Eof(ImportFile) then
          begin
            ReadLn(ImportFile, Line);
            FieldNames.CommaText := Line;
          end;

          // 开始事务处理
          FConnection.StartTransaction;
          try
            while not Eof(ImportFile) do
            begin
              ReadLn(ImportFile, Line);
              FieldValues.CommaText := Line;

              // 构建INSERT语句
              InsertSQL := Format('INSERT INTO "%s" (', [ATableName]);
              for i := 0 to FieldNames.Count - 1 do
              begin
                if i > 0 then
                  InsertSQL := InsertSQL + ', ';
                FieldName := FieldNames[i];
                InsertSQL := InsertSQL + '"' + FieldName + '"';
              end;
              InsertSQL := InsertSQL + ') VALUES (';
              for i := 0 to FieldValues.Count - 1 do
              begin
                if i > 0 then
                  InsertSQL := InsertSQL + ', ';
                InsertSQL := InsertSQL + '''' + StringReplace(FieldValues[i], '''', '''''', [rfReplaceAll]) + '''';
              end;
              InsertSQL := InsertSQL + ')';

              // 执行INSERT语句
              ImportQuery.SQL.Text := InsertSQL;
              ImportQuery.ExecSQL;
            end;

            FConnection.Commit;
            ShowMessage('数据导入成功完成');

            // 刷新表格数据
            LoadTableData(ATableName);
          except
            on E: Exception do
            begin
              FConnection.Rollback;
              ShowMessage('导入数据时出错: ' + E.Message);
            end;
          end;
        finally
          CloseFile(ImportFile);
        end;
      finally
        FieldNames.Free;
        FieldValues.Free;
        ImportQuery.Free;
      end;
    end;
  finally
    OpenDialog.Free;
  end;
end;

procedure TfrmMain.ModifyTableData(const ATableName: string);
begin
  // 此功能需要更复杂的实现，包括表结构分析、数据编辑界面等，暂未实现
  ShowMessage('表数据修改功能尚未实现');
end;

procedure TfrmMain.RefreshDatabaseNode(Node: TTreeNode);
var
  DBManager: DBConnection.TDBConnectionManager;

  DBPath: string;
begin
  if not Assigned(Node) then
    Exit;

  if not FConnection.Connected then
    Exit;

  // 获取数据库路径
  DBPath := FConnection.Params.Database;
  if DBPath = '' then
    DBPath := FConnection.Params.Values['DefaultDir'];

  // 清空当前节点的子节点
  Node.DeleteChildren;

  // 加载数据库对象
  DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
  try
    // 加载表并按目录组织
    LoadTablesIntoDirectories(DBManager, Node, DBPath);
  finally
    DBManager.Free;
  end;
end;

function TfrmMain.FindMirServerDir(const StartDir: string): string;
var
  CurrentDir, ParentDir: string;
  IsMirServerDir: Boolean;
begin
  Result := '';

  // 检查当前目录是否就是MirServer
  if ExtractFileName(ExcludeTrailingPathDelimiter(StartDir)) = 'MirServer' then
  begin
    Result := IncludeTrailingPathDelimiter(StartDir);
    Exit;
  end;

  // 检查当前目录下是否有MirServer子目录
  if DirectoryExists(IncludeTrailingPathDelimiter(StartDir) + 'MirServer') then
  begin
    Result := IncludeTrailingPathDelimiter(StartDir) + 'MirServer\';
    Exit;
  end;

  // 检查当前目录是否是MirServer目录的特征
  // 判断依据是存在Config.ini和Mud2\DB目录结构
  IsMirServerDir := FileExists(IncludeTrailingPathDelimiter(StartDir) + 'Config.ini') and
                   DirectoryExists(IncludeTrailingPathDelimiter(StartDir) + 'Mud2') and
                   DirectoryExists(IncludeTrailingPathDelimiter(StartDir) + 'Mud2\DB');

  if IsMirServerDir then
  begin
    Result := IncludeTrailingPathDelimiter(StartDir);
    Exit;
  end;

  // 向上查找
  ParentDir := ExtractFilePath(ExcludeTrailingPathDelimiter(StartDir));
  if (ParentDir <> '') and (ParentDir <> StartDir) then
  begin
    Result := FindMirServerDir(ParentDir);
    if Result <> '' then
      Exit;
  end;

  // 如果以上方法都没找到，尝试向上递归查找所有父目录中是否有MirServer目录
  CurrentDir := StartDir;
  while (CurrentDir <> '') do
  begin
    // 获取父目录路径
    ParentDir := ExtractFilePath(ExcludeTrailingPathDelimiter(CurrentDir));

    // 如果已经到达根目录或者出现循环引用，退出
    if (ParentDir = CurrentDir) then
      Break;

    // 检查父目录下是否有MirServer目录
    if DirectoryExists(IncludeTrailingPathDelimiter(ParentDir) + 'MirServer') then
    begin
      Result := IncludeTrailingPathDelimiter(ParentDir) + 'MirServer\';
      Exit;
    end;

    CurrentDir := ParentDir;
  end;
end;

function TfrmMain.AutoDetectDatabase(const AGameDir: string): Boolean;
var
  MirServerPath, ConfigFile, DBType, DBFile, AccessFileName, ParadoxDir, ParentDir: string;
  UseAccessDB: Integer;
begin
  Result := False;

  // 首先尝试查找MirServer目录的位置
  MirServerPath := FindMirServerDir(AGameDir);

  // 如果没有找到MirServer目录，尝试其他方法
  if MirServerPath = '' then
  begin
    // 检查当前目录是否包含Mud2\DB目录，如果是则可能是MirServer目录
    if DirectoryExists(IncludeTrailingPathDelimiter(AGameDir) + 'Mud2') and
       DirectoryExists(IncludeTrailingPathDelimiter(AGameDir) + 'Mud2\DB') then
    begin
      MirServerPath := IncludeTrailingPathDelimiter(AGameDir);
    end
    else
    begin
      // 检查父目录是否包含Mud2\DB目录
      ParentDir := ExtractFilePath(ExcludeTrailingPathDelimiter(AGameDir));
      if DirectoryExists(IncludeTrailingPathDelimiter(ParentDir) + 'Mud2') and
         DirectoryExists(IncludeTrailingPathDelimiter(ParentDir) + 'Mud2\DB') then
      begin
        MirServerPath := IncludeTrailingPathDelimiter(ParentDir);
      end
      else
      begin
        // 如果都找不到，使用当前目录
        MirServerPath := IncludeTrailingPathDelimiter(AGameDir);
      end;
    end;
  end;

  // 确保路径以反斜杠结尾
  MirServerPath := IncludeTrailingPathDelimiter(MirServerPath);

  // 查找Config.ini文件
  ConfigFile := MirServerPath + 'Config.ini';
  if not FileExists(ConfigFile) then
  begin
    ShowMessage('找不到Config.ini文件，无法自动检测数据库类型，请手动选择数据库文件');
    Exit(False);
  end;

  // 从Config.ini中读取数据库类型
  DBType := ReadIniValue(ConfigFile, 'DB', 'DBType', '');

  // 如果配置文件中指定了SQLite数据库
  if SameText(DBType, 'SQLite') then
  begin
    // 读取SQLite数据库文件路径
    DBFile := ReadIniValue(ConfigFile, 'DB', 'DBFile', '');

    // 如果没有指定DBFile，尝试在Mud2\DB目录下查找常见的SQLite数据库文件
    if DBFile = '' then
    begin
      var DBPath := MirServerPath + 'Mud2\DB\';
      if DirectoryExists(DBPath) then
      begin
        if FileExists(DBPath + 'mir2.db') then
          DBFile := DBPath + 'mir2.db'
        else if FileExists(DBPath + 'game.db') then
          DBFile := DBPath + 'game.db'
        else if FileExists(DBPath + 'HeroDB.db') then
          DBFile := DBPath + 'HeroDB.db'
        else if FileExists(DBPath + 'ApexM2.DB') then
          DBFile := DBPath + 'ApexM2.DB';
      end;
    end
    else
    begin
      // 如果DBFile不是绝对路径，尝试在不同位置查找
      if not FileExists(DBFile) and (Length(DBFile) > 0) then
      begin
        // 检查是否是绝对路径
        if (DBFile[1] = PathDelim) or ((Length(DBFile) > 2) and (DBFile[2] = ':')) then
        begin
          // 如果是绝对路径但文件不存在，报错
          ShowMessage('Config.ini中指定的SQLite数据库文件不存在: ' + DBFile);
          Exit(False);
        end
        else
        begin
          // 尝试在MirServer目录下
          if FileExists(MirServerPath + DBFile) then
            DBFile := MirServerPath + DBFile
          // 尝试在Mud2\DB目录下
          else if FileExists(MirServerPath + 'Mud2\DB\' + DBFile) then
            DBFile := MirServerPath + 'Mud2\DB\' + DBFile
          else
          begin
            ShowMessage('找不到SQLite数据库文件: ' + DBFile);
            Exit(False);
          end;
        end;
      end;
    end;

    if (DBFile <> '') and FileExists(DBFile) then
    begin
      // 连接到SQLite数据库
      ConnectToDatabase(DBFile, dbtSQLite);
      Result := True;
      Exit; // 连接成功，返回结果
    end
    else
    begin
      ShowMessage('无法找到有效的SQLite数据库文件');
      Exit(False);
    end;
  end
  // 如果没有指定数据库类型，检查Access配置
  else if DBType = '' then
  begin
    // 检查是否使用Access数据库
    UseAccessDB := StrToIntDef(ReadIniValue(ConfigFile, 'DB', 'UseAccessDB', '0'), 0);

    if UseAccessDB = 1 then
    begin
      // 读取Access数据库文件路径
      AccessFileName := ReadIniValue(ConfigFile, 'DB', 'AccessFileName', '');

      // 如果没有指定AccessFileName，尝试在Mud2\DB目录下查找常见的Access数据库文件
      if AccessFileName = '' then
      begin
        var DBPath := MirServerPath + 'Mud2\DB\';
        if DirectoryExists(DBPath) then
        begin
          if FileExists(DBPath + 'Mir2.mdb') then
            AccessFileName := DBPath + 'Mir2.mdb'
          else if FileExists(DBPath + 'GameData.mdb') then
            AccessFileName := DBPath + 'GameData.mdb'
          else if FileExists(DBPath + 'HeroDB.mdb') then
            AccessFileName := DBPath + 'HeroDB.mdb';
        end;
      end
      else
      begin
        // 如果AccessFileName不是绝对路径，尝试在不同位置查找
        if not FileExists(AccessFileName) and (Length(AccessFileName) > 0) then
        begin
          // 检查是否是绝对路径
          if (AccessFileName[1] = PathDelim) or ((Length(AccessFileName) > 2) and (AccessFileName[2] = ':')) then
          begin
            // 如果是绝对路径但文件不存在，报错
            ShowMessage('Config.ini中指定的Access数据库文件不存在: ' + AccessFileName);
            Exit(False);
          end
          else
          begin
            // 尝试在MirServer目录下
            if FileExists(MirServerPath + AccessFileName) then
              AccessFileName := MirServerPath + AccessFileName
            // 尝试在Mud2\DB目录下
            else if FileExists(MirServerPath + 'Mud2\DB\' + AccessFileName) then
              AccessFileName := MirServerPath + 'Mud2\DB\' + AccessFileName
            else
            begin
              ShowMessage('找不到Access数据库文件: ' + AccessFileName);
              Exit(False);
            end;
          end;
        end;
      end;

      if (AccessFileName <> '') and FileExists(AccessFileName) then
      begin
        // 连接到Access数据库
        ConnectToDatabase(AccessFileName, dbtAccess);
        Result := True;
        Exit; // 连接成功，返回结果
      end
      else
      begin
        ShowMessage('无法找到有效的Access数据库文件');
        Exit(False);
      end;
    end
    // 如果UseAccessDB=0，则尝试连接Paradox数据库
    else if UseAccessDB = 0 then
    begin
      // 查找Mud2\DB目录下的Paradox数据库
      ParadoxDir := MirServerPath + 'Mud2\DB\';

      if DirectoryExists(ParadoxDir) then
      begin
        // 连接到Paradox数据库
        ConnectToDatabase(ParadoxDir, dbtParadox);
        Result := True;
        Exit; // 连接成功，返回结果
      end
      else
      begin
        ShowMessage('无法找到有效的Paradox数据库目录: ' + ParadoxDir);
        Exit(False);
      end;
    end
  end;

  // 如果以上所有方法都无法找到有效的数据库，提示用户手动选择
  ShowMessage('无法自动检测数据库类型或找不到有效的数据库文件，请检查Config.ini配置或手动选择数据库文件');
  Exit(False);
end;

procedure TfrmMain.actExportDataExecute(Sender: TObject);
begin
  if FCurrentTable <> '' then
    ExportTableData(FCurrentTable)
  else
    ShowMessage('请先选择一个表');
end;

procedure TfrmMain.actImportDataExecute(Sender: TObject);
begin
  if FCurrentTable <> '' then
    ImportTableData(FCurrentTable)
  else
    ShowMessage('请先选择一个表');
end;

procedure TfrmMain.actRefreshDataExecute(Sender: TObject);
begin
  if FCurrentTable <> '' then
    LoadTableData(FCurrentTable)
  else
    ShowMessage('请先选择一个表');
end;

procedure TfrmMain.actModifyDataExecute(Sender: TObject);
begin
  if FCurrentTable <> '' then
    ModifyTableData(FCurrentTable)
  else
    ShowMessage('请先选择一个表');
end;

procedure TfrmMain.actRefreshDBExecute(Sender: TObject);
begin
  if FConnection.Connected then
    LoadDatabaseStructure
  else
    ShowMessage('请先连接到数据库');
end;

procedure TfrmMain.actSearchDBExecute(Sender: TObject);
var
  SearchText: string;
begin
  if not FConnection.Connected then
  begin
    ShowMessage('请先连接到数据库');
    Exit;
  end;

  if InputQuery('搜索数据库', '请输入搜索关键字:', SearchText) then
  begin
    if SearchText <> '' then
      SearchDatabase(SearchText)
    else
      ShowMessage('请输入搜索关键字');
  end;
end;

procedure TfrmMain.actConvertDBExecute(Sender: TObject);
begin
  ShowMessage('数据库转换功能尚未实现');
end;

procedure TfrmMain.tvDatabasesMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
var
  Node: TTreeNode;

begin
  if Button = mbRight then
  begin
    Node := tvDatabases.GetNodeAt(X, Y);
    if Assigned(Node) then
    begin
      tvDatabases.Selected := Node;

      // 根据节点类型显示不同的菜单项
      if Node.Level = 0 then
      begin
        // 数据库根节点菜单项
        miRefreshDB.Visible := True;
        miSearchDB.Visible := True;
        miConvertDB.Visible := True;
        miExportData.Visible := False;
        miImportData.Visible := False;
        miRefreshData.Visible := False;
        miModifyData.Visible := False;
      end
      else if Node.Level = 1 then
      begin
        // 表节点菜单项
        miRefreshDB.Visible := False;
        miSearchDB.Visible := False;
        miConvertDB.Visible := False;
        miExportData.Visible := True;
        miImportData.Visible := True;
        miRefreshData.Visible := True;
        miModifyData.Visible := True;

        // 设置当前表名
        FCurrentTable := Node.Text;
      end;

      // 显示弹出菜单
      pmTreeView.Popup(Mouse.CursorPos.X, Mouse.CursorPos.Y);
    end;
  end;
end;

procedure TfrmMain.dbgDataMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
begin
  if Button = mbRight then
  begin
    // 在鼠标位置显示数据网格弹出菜单
    pmDBGrid.Popup(Mouse.CursorPos.X, Mouse.CursorPos.Y);
  end;
end;

procedure TfrmMain.SearchDatabase(const SearchText: string);
var
  SearchQuery: TFDQuery;
  Tables: TStringList;
  i: Integer;
  TableName: string;
  ResultsForm: TForm;
  ResultsMemo: TMemo;
  SQL: string;
begin
  if not FConnection.Connected then
    Exit;

  // 创建结果窗口
  ResultsForm := TForm.Create(Self);
  try
    ResultsForm.Caption := '搜索结果: ' + SearchText;
    ResultsForm.Width := 600;
    ResultsForm.Height := 400;
    ResultsForm.Position := poScreenCenter;

    ResultsMemo := TMemo.Create(ResultsForm);
    ResultsMemo.Parent := ResultsForm;
    ResultsMemo.Align := alClient;
    ResultsMemo.ScrollBars := ssBoth;
    ResultsMemo.ReadOnly := True;

    // 获取所有表
    Tables := TStringList.Create;
    try
      // 创建查询对象
      SearchQuery := TFDQuery.Create(nil);
      try
        SearchQuery.Connection := FConnection;

        // 获取表列表
        var DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
        try
          Tables := DBManager.GetTableNames;
        finally
          DBManager.Free;
        end;

        // 在每个表中搜索
        for i := 0 to Tables.Count - 1 do
        begin
          TableName := Tables[i];
          ResultsMemo.Lines.Add('正在搜索表: ' + TableName);
          Application.ProcessMessages;

          try
            // 构建查询SQL
            SQL := 'SELECT * FROM "' + TableName + '"';
            SearchQuery.SQL.Text := SQL;
            SearchQuery.Open;

            // 搜索每一行
            while not SearchQuery.Eof do
            begin
              var FoundInRow := False;
              var RowText := '';

              // 检查每个字段
              for var j := 0 to SearchQuery.FieldCount - 1 do
              begin
                var FieldValue := SearchQuery.Fields[j].AsString;
                if Pos(LowerCase(SearchText), LowerCase(FieldValue)) > 0 then
                begin
                  FoundInRow := True;
                  RowText := RowText + SearchQuery.Fields[j].FieldName + '=' + FieldValue + ', ';
                end;
              end;

              if FoundInRow then
              begin
                ResultsMemo.Lines.Add('  找到匹配: ' + RowText);
              end;

              SearchQuery.Next;
            end;

            SearchQuery.Close;
          except
            on E: Exception do
            begin
              ResultsMemo.Lines.Add('  搜索表时出错: ' + E.Message);
            end;
          end;
        end;

        ResultsMemo.Lines.Add('搜索完成');
      finally
        SearchQuery.Free;
      end;
    finally
      Tables.Free;
    end;

    // 显示搜索结果
    ResultsForm.ShowModal;
  finally
    ResultsForm.Free;
  end;
end;

procedure TfrmMain.ConvertDatabase(const SourcePath, TargetPath: string; SourceType, TargetType: DBConnection.TDBType);
begin
  ShowMessage('数据库转换功能尚未实现');
end;

function TfrmMain.ReadIniValue(const FileName, Section, Key, DefaultValue: string): string;
var
  IniFile: TIniFile;
begin
  Result := DefaultValue;

  if not FileExists(FileName) then
    Exit;

  IniFile := TIniFile.Create(FileName);
  try
    Result := IniFile.ReadString(Section, Key, DefaultValue);
  finally
    IniFile.Free;
  end;
end;



end.