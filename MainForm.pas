unit MainForm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ComCtrls, Vcl.ExtCtr<PERSON>,
  Vcl.<PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, System.ImageList, Vcl.ImgList,
  Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error, FireDAC.UI.Intf,
  FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool, FireDAC.Stan.Async,
  FireDAC.Phys, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs,
  FireDAC.VCLUI.Wait, FireDAC.Comp.Client, System.Actions, Vcl.ActnList, Vcl.DBGrids,
  FireDAC.Comp.DataSet, System.Generics.Collections, DBConnection, System.IniFiles;

type
  TDatabaseInfo = class
  public
    Path: string;
    Name: string;
    DBType: DBConnection.TDBType;
    constructor Create(const APath, AName: string; ADBType: DBConnection.TDBType);
  end;
  TfrmMain = class(TForm)
    pnlMain: TPanel;
    sbMain: TStatusBar;
    mmMain: TMainMenu;
    miFile: TMenuItem;
    miConnect: TMenuItem;
    miDisconnect: TMenuItem;
    N1: TMenuItem;
    miExit: TMenuItem;
    miEdit: TMenuItem;
    miHelp: TMenuItem;
    miAbout: TMenuItem;
    tbMain: TToolBar;
    ilMain: TImageList;
    pnlLeft: TPanel;
    tvDatabases: TTreeView;
    splVertical: TSplitter;
    pcRight: TPageControl;
    tsData: TTabSheet;
    alMain: TActionList;
    actConnect: TAction;
    actDisconnect: TAction;
    actExit: TAction;
    btnConnect: TToolButton;
    btnDisconnect: TToolButton;
    dbgData: TDBGrid;
    dsData: TDataSource;
    pmTreeView: TPopupMenu;
    pmDBGrid: TPopupMenu;
    actExportData: TAction;
    actImportData: TAction;
    actRefreshData: TAction;
    actModifyData: TAction;
    actRefreshDB: TAction;
    actSearchDB: TAction;
    actConvertDB: TAction;
    miExportData: TMenuItem;
    miImportData: TMenuItem;
    miRefreshData: TMenuItem;
    miModifyData: TMenuItem;
    miRefreshDB: TMenuItem;
    miSearchDB: TMenuItem;
    miConvertDB: TMenuItem;
    btnExportData: TToolButton;
    btnImportData: TToolButton;
    btnRefreshData: TToolButton;
    btnModifyData: TToolButton;
    btnRefreshDB: TToolButton;
    btnSearchDB: TToolButton;
    btnConvertDB: TToolButton;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure actConnectExecute(Sender: TObject);
    procedure actDisconnectExecute(Sender: TObject);
    procedure actExitExecute(Sender: TObject);
    procedure tvDatabasesDblClick(Sender: TObject);
    procedure actExportDataExecute(Sender: TObject);
    procedure actImportDataExecute(Sender: TObject);
    procedure actRefreshDataExecute(Sender: TObject);
    procedure actModifyDataExecute(Sender: TObject);
    procedure actRefreshDBExecute(Sender: TObject);
    procedure actSearchDBExecute(Sender: TObject);
    procedure actConvertDBExecute(Sender: TObject);
    procedure tvDatabasesMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
    procedure dbgDataMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
  private
    { Private declarations }
    FConnection: TFDConnection;
    FCurrentQuery: TFDQuery;
    FCurrentTable: string;
    FDatabaseList: TObjectList<TDatabaseInfo>;
    FConfigFileName: string;
    procedure LoadIcons;
    procedure InitializeControls;
    procedure DisconnectFromDatabase;
    procedure LoadDatabaseStructure;
    procedure LoadTablesIntoDirectories(DBManager: DBConnection.TDBConnectionManager; RootNode: TTreeNode; const DBPath: string);
    procedure LoadTableData(const ATableName: string);
    procedure SaveDatabaseList;
    procedure LoadDatabaseList;
    procedure AddDatabaseToList(const APath, AName: string; ADBType: DBConnection.TDBType);
    procedure AddDatabaseToHistoryList(const APath, AName: string; ADBType: DBConnection.TDBType);
    procedure ConnectToDatabase(const APath: string; ADBType: DBConnection.TDBType);
    function FindMirServerDir(const StartDir: string): string;
    function AutoDetectDatabase(const AGameDir: string): Boolean;
    procedure RefreshDatabaseNode(Node: TTreeNode);
    procedure ExportTableData(const ATableName: string);
    procedure ImportTableData(const ATableName: string);
    procedure ModifyTableData(const ATableName: string);
    procedure SearchDatabase(const SearchText: string);
    procedure ConvertDatabase(const SourcePath, TargetPath: string; SourceType, TargetType: DBConnection.TDBType);
    function ReadIniValue(const FileName, Section, Key, DefaultValue: string): string;
  public
    { Public declarations }
  end;

var
  frmMain: TfrmMain;

implementation

{$R *.dfm}

uses
  ConnectionDialog;

constructor TDatabaseInfo.Create(const APath, AName: string; ADBType: DBConnection.TDBType);
begin
  inherited Create;
  Path := APath;
  Name := AName;
  DBType := ADBType;
end;

procedure TfrmMain.FormCreate(Sender: TObject);
begin
  // �����µ�Action
  if not Assigned(actExportData) then
  begin
    actExportData := TAction.Create(alMain);
    actExportData.Caption := '��������(&E)';
    actExportData.OnExecute := actExportDataExecute;
    actExportData.ActionList := alMain;
  end;

  if not Assigned(actImportData) then
  begin
    actImportData := TAction.Create(alMain);
    actImportData.Caption := '��������(&I)';
    actImportData.OnExecute := actImportDataExecute;
    actImportData.ActionList := alMain;
  end;

  if not Assigned(actRefreshData) then
  begin
    actRefreshData := TAction.Create(alMain);
    actRefreshData.Caption := 'ˢ������(&R)';
    actRefreshData.OnExecute := actRefreshDataExecute;
    actRefreshData.ActionList := alMain;
  end;

  if not Assigned(actModifyData) then
  begin
    actModifyData := TAction.Create(alMain);
    actModifyData.Caption := '�޸�����(&M)';
    actModifyData.OnExecute := actModifyDataExecute;
    actModifyData.ActionList := alMain;
  end;

  if not Assigned(actRefreshDB) then
  begin
    actRefreshDB := TAction.Create(alMain);
    actRefreshDB.Caption := 'ˢ�����ݿ�(&F)';
    actRefreshDB.OnExecute := actRefreshDBExecute;
    actRefreshDB.ActionList := alMain;
  end;

  if not Assigned(actSearchDB) then
  begin
    actSearchDB := TAction.Create(alMain);
    actSearchDB.Caption := '�������ݿ�(&S)';
    actSearchDB.OnExecute := actSearchDBExecute;
    actSearchDB.ActionList := alMain;
  end;

  if not Assigned(actConvertDB) then
  begin
    actConvertDB := TAction.Create(alMain);
    actConvertDB.Caption := 'ת�����ݿ�(&C)';
    actConvertDB.OnExecute := actConvertDBExecute;
    actConvertDB.ActionList := alMain;
  end;

  // ���������˵�
  if not Assigned(pmTreeView) then
    pmTreeView := TPopupMenu.Create(Self);

  if not Assigned(pmDBGrid) then
    pmDBGrid := TPopupMenu.Create(Self);

  // ������������ť
  if not Assigned(btnExportData) then
  begin
    btnExportData := TToolButton.Create(tbMain);
    btnExportData.Parent := tbMain;
    btnExportData.Action := actExportData;
  end;

  if not Assigned(btnImportData) then
  begin
    btnImportData := TToolButton.Create(tbMain);
    btnImportData.Parent := tbMain;
    btnImportData.Action := actImportData;
  end;

  if not Assigned(btnRefreshData) then
  begin
    btnRefreshData := TToolButton.Create(tbMain);
    btnRefreshData.Parent := tbMain;
    btnRefreshData.Action := actRefreshData;
  end;

  if not Assigned(btnModifyData) then
  begin
    btnModifyData := TToolButton.Create(tbMain);
    btnModifyData.Parent := tbMain;
    btnModifyData.Action := actModifyData;
  end;

  if not Assigned(btnRefreshDB) then
  begin
    btnRefreshDB := TToolButton.Create(tbMain);
    btnRefreshDB.Parent := tbMain;
    btnRefreshDB.Action := actRefreshDB;
  end;

  if not Assigned(btnSearchDB) then
  begin
    btnSearchDB := TToolButton.Create(tbMain);
    btnSearchDB.Parent := tbMain;
    btnSearchDB.Action := actSearchDB;
  end;

  if not Assigned(btnConvertDB) then
  begin
    btnConvertDB := TToolButton.Create(tbMain);
    btnConvertDB.Parent := tbMain;
    btnConvertDB.Action := actConvertDB;
  end;

  InitializeControls;

  // �������ݿ��б�
  FDatabaseList := TObjectList<TDatabaseInfo>.Create(True); // True��ʾ�Զ��ͷŶ���

  // ���������ļ���
  FConfigFileName := ChangeFileExt(Application.ExeName, '.ini');

  // �������ݿ��б�
  LoadDatabaseList;

  // �������Ӷ���
  FConnection := TFDConnection.Create(Self);
  FConnection.Params.DriverID := 'SQLite';

  // ������ѯ����
  FCurrentQuery := TFDQuery.Create(Self);
  dsData.DataSet := FCurrentQuery;

  // ����ͼ��
  LoadIcons;
end;

procedure TfrmMain.FormDestroy(Sender: TObject);
begin
  // �������ݿ��б�
  SaveDatabaseList;

  if FConnection.Connected then
    FConnection.Close;

  // �ͷŲ�ѯ����
  if Assigned(FCurrentQuery) then
    FCurrentQuery.Free;

  // �ͷ����ݿ��б�
  FDatabaseList.Free;

  FConnection.Free;
end;

procedure TfrmMain.InitializeControls;
begin
  pcRight.ActivePage := tsData;
  actDisconnect.Enabled := False;

  // ��ʼ��Action
  actExportData.Enabled := False;
  actImportData.Enabled := False;
  actRefreshData.Enabled := False;
  actModifyData.Enabled := False;
  actRefreshDB.Enabled := False;
  actSearchDB.Enabled := False;
  actConvertDB.Enabled := False;

  // ����Actionͼ������
  actConnect.ImageIndex := 0; // ���ݿ�ȫ��ͼ��
  actDisconnect.ImageIndex := 7; // ���ݿ����Ӵ���ͼ��
  actExportData.ImageIndex := 1; // �����ݵ���ͼ��
  actImportData.ImageIndex := 3; // �����ݵ���ͼ��
  actRefreshData.ImageIndex := 4; // ������ˢ��ͼ��
  actModifyData.ImageIndex := 5; // �������޸�ͼ��
  actRefreshDB.ImageIndex := 2; // ���ݿ�ˢ��ͼ��
  actSearchDB.ImageIndex := 8; // ���ݿ�����ͼ��
  actConvertDB.ImageIndex := 9; // ���ݿ�ת��ͼ��

  // ����Action��ʾ
  actConnect.Hint := '�������ݿ�';
  actDisconnect.Hint := '�Ͽ����ݿ�����';
  actExportData.Hint := '����������';
  actImportData.Hint := '���������';
  actRefreshData.Hint := 'ˢ�±�����';
  actModifyData.Hint := '�޸ı�����';
  actRefreshDB.Hint := 'ˢ�����ݿ�';
  actSearchDB.Hint := '�������ݿ�';
  actConvertDB.Hint := 'ת�����ݿ�';

  // ��ʼ��TreeView�Ҽ��˵�
  if not Assigned(pmTreeView) then
    pmTreeView := TPopupMenu.Create(Self);

  // ���Ӳ˵���
  if not Assigned(miExportData) then
  begin
    miExportData := TMenuItem.Create(pmTreeView);
    miExportData.Action := actExportData;
    pmTreeView.Items.Add(miExportData);
  end;

  if not Assigned(miImportData) then
  begin
    miImportData := TMenuItem.Create(pmTreeView);
    miImportData.Action := actImportData;
    pmTreeView.Items.Add(miImportData);
  end;

  if not Assigned(miRefreshData) then
  begin
    miRefreshData := TMenuItem.Create(pmTreeView);
    miRefreshData.Action := actRefreshData;
    pmTreeView.Items.Add(miRefreshData);
  end;

  if not Assigned(miModifyData) then
  begin
    miModifyData := TMenuItem.Create(pmTreeView);
    miModifyData.Action := actModifyData;
    pmTreeView.Items.Add(miModifyData);
  end;

  if not Assigned(miRefreshDB) then
  begin
    miRefreshDB := TMenuItem.Create(pmTreeView);
    miRefreshDB.Action := actRefreshDB;
    pmTreeView.Items.Add(miRefreshDB);
  end;

  if not Assigned(miSearchDB) then
  begin
    miSearchDB := TMenuItem.Create(pmTreeView);
    miSearchDB.Action := actSearchDB;
    pmTreeView.Items.Add(miSearchDB);
  end;

  if not Assigned(miConvertDB) then
  begin
    miConvertDB := TMenuItem.Create(pmTreeView);
    miConvertDB.Action := actConvertDB;
    pmTreeView.Items.Add(miConvertDB);
  end;

  // ����TreeView�¼�����
  tvDatabases.PopupMenu := pmTreeView;
  tvDatabases.OnMouseDown := tvDatabasesMouseDown;

  // ����DBGrid�¼�����
  dbgData.PopupMenu := pmDBGrid;
  dbgData.OnMouseDown := dbgDataMouseDown;
end;

procedure TfrmMain.actConnectExecute(Sender: TObject);
var
  ConnectionDlg: TfrmConnectionDialog;
begin
  ConnectionDlg := TfrmConnectionDialog.Create(Self, FConnection);
  try
    if ConnectionDlg.Execute then
    begin
      // 连接成功，更新UI
      var DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
      try
        // 显示连接方式信息
        sbMain.SimpleText := '已连接到数据库: ' + FConnection.Params.Database + ' | 连接方式: ' + DBManager.GetConnectionInfo;

        // 获取数据库信息并添加到历史列表
        var DBPath := FConnection.Params.Database;
        if DBPath = '' then
          DBPath := FConnection.Params.Values['DefaultDir'];

        var DBType := DBManager.DBType;
        var DBName := ExtractFileName(DBPath);
        if DBName = '' then
          DBName := DBPath;

        // 添加到历史列表
        AddDatabaseToHistoryList(DBPath, DBName, DBType);
      finally
        DBManager.Free;
      end;

      // 更新Action状态
      actConnect.Enabled := False;
      actDisconnect.Enabled := True;
      actRefreshDB.Enabled := True;
      actSearchDB.Enabled := True;
      actConvertDB.Enabled := True;

      // 加载数据库结构
      LoadDatabaseStructure;
    end;
  finally
    ConnectionDlg.Free;
  end;
end;

procedure TfrmMain.actDisconnectExecute(Sender: TObject);
begin
  DisconnectFromDatabase;
end;

procedure TfrmMain.DisconnectFromDatabase;
var
  i: Integer;
begin
  if FConnection.Connected then
    FConnection.Close;

  sbMain.SimpleText := '�ѶϿ�����';

  // ����Action״̬
  actConnect.Enabled := True;
  actDisconnect.Enabled := False;
  actExportData.Enabled := False;
  actImportData.Enabled := False;
  actRefreshData.Enabled := False;
  actModifyData.Enabled := False;
  actRefreshDB.Enabled := False;
  actSearchDB.Enabled := False;
  actConvertDB.Enabled := False;

  // ��յ�ǰ����
  FCurrentTable := '';

  // ��յ�ǰ���ݿ�ڵ㣬���������ݿ��б��ڵ�
  for i := tvDatabases.Items.Count - 1 downto 0 do
  begin
    if (tvDatabases.Items[i].Level = 0) and (tvDatabases.Items[i].Text <> '���ݿ��б�') then
    begin
      tvDatabases.Items[i].Delete;
    end;
  end;
end;

procedure TfrmMain.LoadDatabaseStructure;
var
  DBManager: DBConnection.TDBConnectionManager;
  RootNode: TTreeNode;
  DBPath, DBName: string;
begin
  // �������ͼ�еĵ�ǰ���ݿ�ڵ�
  for var i := tvDatabases.Items.Count - 1 downto 0 do
  begin
    if (tvDatabases.Items[i].Level = 0) and (tvDatabases.Items[i].Text <> '���ݿ��б�') then
    begin
      tvDatabases.Items[i].Delete;
    end;
  end;

  if not FConnection.Connected then
    Exit;

  // ��ȡ���ݿ�·��
  DBPath := FConnection.Params.Database;
  if DBPath = '' then
    DBPath := FConnection.Params.Values['DefaultDir'];

  // ��ȡ���ݿ����ƣ�ʹ��Ŀ¼����
  if DBPath <> '' then
  begin
    // ȥ��ĩβ��·���ָ���
    if (Length(DBPath) > 0) and (DBPath[Length(DBPath)] = PathDelim) then
      DBPath := Copy(DBPath, 1, Length(DBPath) - 1);

    // ��ȡ���һ��Ŀ¼��
    DBName := ExtractFileName(DBPath);
    if DBName = '' then
      DBName := DBPath;
  end
  else
    DBName := '���ݿ�';

  // �������ڵ㣨ʹ�����ݿ����ƣ�
  RootNode := tvDatabases.Items.Add(nil, DBName);
  RootNode.ImageIndex := 0; // ���ݿ�ȫ��ͼ��
  RootNode.SelectedIndex := 0;

  // չ�����ڵ�
  RootNode.Expand(False);

  // �������ݿ����
  DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
  try
    // ���ر�����Ŀ¼��֯
    LoadTablesIntoDirectories(DBManager, RootNode, DBPath);
  finally
    DBManager.Free;
  end;
end;

procedure TfrmMain.actExitExecute(Sender: TObject);
begin
  Close;
end;

procedure TfrmMain.LoadTableData(const ATableName: string);
var
  DBManager: DBConnection.TDBConnectionManager;
  IsSQLite: Boolean;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  try
    // ���浱ǰ����
    FCurrentTable := ATableName;

    // ����ҳ�����
    tsData.Caption := '���ݱ�: ' + ATableName;

    // �л�������ҳ
    pcRight.ActivePage := tsData;

    // �ر�֮ǰ�Ĳ�ѯ
    if Assigned(FCurrentQuery) then
    begin
      FCurrentQuery.Close;

      // �����ַ���֧��
      FCurrentQuery.Connection := FConnection;
      FCurrentQuery.FormatOptions.MapRules.Clear;
      FCurrentQuery.FormatOptions.OwnMapRules := True;
      FCurrentQuery.FormatOptions.StrsEmpty2Null := False;
      FCurrentQuery.FormatOptions.StrsTrim := False;

      // �������ݿ����͹��첻ͬ��SQL���
      DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
      try
        IsSQLite := (DBManager.DBType = dbtSQLite);
      finally
        DBManager.Free;
      end;

      if IsSQLite then
        // SQLiteʹ��˫���Ű�Χ����
        FCurrentQuery.SQL.Text := Format('SELECT * FROM "%s"', [ATableName])
      else
        // Accessʹ�÷����Ű�Χ����
        FCurrentQuery.SQL.Text := Format('SELECT * FROM [%s]', [ATableName]);

      // �򿪲�ѯ
      FCurrentQuery.Open;

      // ����״̬����Ϣ
      DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
      try
        sbMain.SimpleText := Format('�Ѽ��ر� "%s"���� %d ����¼ | ���ݿ�: %s | ���ӷ�ʽ: %s',
          [ATableName, FCurrentQuery.RecordCount, FConnection.Params.Database, DBManager.GetConnectionInfo]);
      finally
        DBManager.Free;
      end;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('���ر�����ʧ��: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.tvDatabasesDblClick(Sender: TObject);
var
  Node: TTreeNode;
  ObjectName: string;
  DBInfo: TDatabaseInfo;
begin
  Node := tvDatabases.Selected;
  if not Assigned(Node) then
    Exit;

  // �������ݿ��б��е����ݿ�ڵ㣨��1���ڵ㣩
  if (Node.Level = 1) and Assigned(Node.Data) then
  begin
    // ��ȡ���ݿ���Ϣ
    DBInfo := TDatabaseInfo(Node.Data);

    // ���ӵ����ݿ�
    ConnectToDatabase(DBInfo.Path, DBInfo.DBType);
    Exit;
  end;

  // ���´������ڵ㣨��2���ڵ㣩
  if not FConnection.Connected then
    Exit;

  if Node.Level = 1 then
  begin
    // ��ǰ���ݿ��µı��ڵ�
    if not Assigned(Node.Data) then
    begin
      // ��ȡ����
      ObjectName := Node.Text;

      // ���浱ǰ����
      FCurrentTable := ObjectName;

      // ���ñ���ص�Action
      actExportData.Enabled := True;
      actImportData.Enabled := True;
      actRefreshData.Enabled := True;
      actModifyData.Enabled := True;

      // ���Ҳ������ʾ������
      LoadTableData(ObjectName);
    end;
  end
  else if Node.Level = 2 then
  begin
    // ���ݿ��б��µ����ݿ��µı��ڵ�
    // ��ȡ����
    ObjectName := Node.Text;

    // ���浱ǰ����
    FCurrentTable := ObjectName;

    // ���ñ���ص�Action
    actExportData.Enabled := True;
    actImportData.Enabled := True;
    actRefreshData.Enabled := True;
    actModifyData.Enabled := True;

    // ���Ҳ������ʾ������
    LoadTableData(ObjectName);
  end;
end;

procedure TfrmMain.LoadTablesIntoDirectories(DBManager: DBConnection.TDBConnectionManager; RootNode: TTreeNode; const DBPath: string);
var
  Tables: TStringList;
  i: Integer;
  TableName: string;
  TableNode: TTreeNode;
  DBName: string;
begin
  // ��ȡ���б�
  Tables := DBManager.GetTableNames;
  try
    if (Tables = nil) or (Tables.Count = 0) then
      Exit;

    // ����ĸ˳���������
    Tables.Sort;

    // ��ȡ���ݿ�����
    DBName := ExtractFileName(ExcludeTrailingPathDelimiter(DBPath));
    if DBName = '' then
      DBName := DBPath;

    // �����б����ӵ����ڵ���
    for i := 0 to Tables.Count - 1 do
    begin
      TableName := Tables[i];

      // ���ӱ��ڵ�
      TableNode := tvDatabases.Items.AddChild(RootNode, TableName);
      TableNode.ImageIndex := 1; // �����ݵ���ͼ����Ϊ��ͼ��
      TableNode.SelectedIndex := 1;
    end;

    // չ�����ڵ�
    RootNode.Expand(False);
  finally
    if Assigned(Tables) then
      Tables.Free;
  end;
end;

procedure TfrmMain.LoadIcons;
var
  Icon: TIcon;
  Bitmap: TBitmap;
  IconPath: string;
begin
  // ���ͼ���б�
  ilMain.Clear;

  // ��ȡͼ���ļ���·��
  IconPath := ExtractFilePath(Application.ExeName) + 'icon\';

  // ���ݿ�ȫ��ͼ�� (���� 0)
  if FileExists(IconPath + '���ݿ�ȫ��.bmp') then
  begin
    Bitmap := TBitmap.Create;
    try
      Bitmap.LoadFromFile(IconPath + '���ݿ�ȫ��.bmp');
      ilMain.Add(Bitmap, nil);
    finally
      Bitmap.Free;
    end;
  end;

  // �����ݵ���ͼ�� (���� 1)
  if FileExists(IconPath + '�����ݵ���.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '�����ݵ���.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // ���ݿ�ˢ��ͼ�� (���� 2)
  if FileExists(IconPath + '���ݿ�ˢ��.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '���ݿ�ˢ��.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // �����ݵ���ͼ�� (���� 3)
  if FileExists(IconPath + '�����ݵ���.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '�����ݵ���.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // ������ˢ��ͼ�� (���� 4)
  if FileExists(IconPath + '������ˢ��.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '������ˢ��.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // �������޸�ͼ�� (���� 5)
  if FileExists(IconPath + '�������޸�.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '�������޸�.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // �������쳣ͼ�� (���� 6)
  if FileExists(IconPath + '�������쳣.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '�������쳣.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // ���ݿ����Ӵ���ͼ�� (���� 7)
  if FileExists(IconPath + '���ݿ����Ӵ���.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '���ݿ����Ӵ���.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // ���ݿ�����ͼ�� (���� 8)
  if FileExists(IconPath + '���ݿ�����.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '���ݿ�����.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // ���ݿ�ת��ͼ�� (���� 9)
  if FileExists(IconPath + '���ݿ�ת��.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '���ݿ�ת��.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;
end;

procedure TfrmMain.SaveDatabaseList;
var
  IniFile: TIniFile;
  i: Integer;
begin
  if not Assigned(FDatabaseList) then
    Exit;

  IniFile := TIniFile.Create(FConfigFileName);
  try
    // ����ɵ����ݿ��б�
    IniFile.EraseSection('DatabaseList');

    // �������ݿ�����
    IniFile.WriteInteger('DatabaseList', 'Count', FDatabaseList.Count);

    // ����ÿ�����ݿ����Ϣ
    for i := 0 to FDatabaseList.Count - 1 do
    begin
      IniFile.WriteString('DatabaseList', Format('Path%d', [i]), FDatabaseList[i].Path);
      IniFile.WriteString('DatabaseList', Format('Name%d', [i]), FDatabaseList[i].Name);
      IniFile.WriteInteger('DatabaseList', Format('DBType%d', [i]), Ord(FDatabaseList[i].DBType));
    end;
  finally
    IniFile.Free;
  end;
end;

procedure TfrmMain.LoadDatabaseList;
var
  IniFile: TIniFile;
  Count, i, DBTypeValue: Integer;
  Path, Name: string;
  DBType: DBConnection.TDBType;
begin
  if not Assigned(FDatabaseList) then
    Exit;

  // ������ݿ��б�
  FDatabaseList.Clear;

  // �������ͼ
  tvDatabases.Items.Clear;

  // �������ڵ�
  var RootNode := tvDatabases.Items.Add(nil, '���ݿ��б�');
  RootNode.ImageIndex := 0; // ���ݿ�ȫ��ͼ��
  RootNode.SelectedIndex := 0;

  IniFile := TIniFile.Create(FConfigFileName);
  try
    // ��ȡ���ݿ�����
    Count := IniFile.ReadInteger('DatabaseList', 'Count', 0);

    // ��ȡÿ�����ݿ����Ϣ
    for i := 0 to Count - 1 do
    begin
      Path := IniFile.ReadString('DatabaseList', Format('Path%d', [i]), '');
      Name := IniFile.ReadString('DatabaseList', Format('Name%d', [i]), '');
      DBTypeValue := IniFile.ReadInteger('DatabaseList', Format('DBType%d', [i]), 0);
      DBType := DBConnection.TDBType(DBTypeValue);

      // ���ӵ����ݿ��б�
      if (Path <> '') and (Name <> '') then
      begin
        AddDatabaseToList(Path, Name, DBType);

        // ���ӵ�����ͼ
        var DBNode := tvDatabases.Items.AddChild(RootNode, Name + ' (' + Path + ')');
        DBNode.ImageIndex := 0; // ���ݿ�ȫ��ͼ��
        DBNode.SelectedIndex := 0;
        // �洢���ݿ���Ϣ����
        DBNode.Data := Pointer(FDatabaseList[FDatabaseList.Count - 1]);
      end;
    end;
  finally
    IniFile.Free;
  end;

  // չ�����ڵ�
  if RootNode.Count > 0 then
    RootNode.Expand(False);
end;

procedure TfrmMain.AddDatabaseToList(const APath, AName: string; ADBType: DBConnection.TDBType);
var
  DBInfo: TDatabaseInfo;
  i: Integer;
begin
  // 检查数据库是否已经存在于列表中
  for i := 0 to FDatabaseList.Count - 1 do
  begin
    if SameText(FDatabaseList[i].Path, APath) then
      Exit; // 已存在，不重复添加
  end;

  // 创建新的数据库信息对象并添加
  DBInfo := TDatabaseInfo.Create(APath, AName, ADBType);
  FDatabaseList.Add(DBInfo);
end;

procedure TfrmMain.AddDatabaseToHistoryList(const APath, AName: string; ADBType: DBConnection.TDBType);
var
  DBInfo: TDatabaseInfo;
  i: Integer;
  Found: Boolean;
  RootNode, DBNode: TTreeNode;
begin
  // 检查数据库是否已经存在于列表中
  Found := False;
  for i := 0 to FDatabaseList.Count - 1 do
  begin
    if SameText(FDatabaseList[i].Path, APath) then
    begin
      Found := True;
      Break;
    end;
  end;

  if not Found then
  begin
    // 创建新的数据库信息对象并添加
    DBInfo := TDatabaseInfo.Create(APath, AName, ADBType);
    FDatabaseList.Add(DBInfo);

    // 查找或创建根节点
    RootNode := nil;
    for i := 0 to tvDatabases.Items.Count - 1 do
    begin
      if (tvDatabases.Items[i].Level = 0) and (tvDatabases.Items[i].Text = '数据库列表') then
      begin
        RootNode := tvDatabases.Items[i];
        Break;
      end;
    end;

    if not Assigned(RootNode) then
    begin
      RootNode := tvDatabases.Items.Add(nil, '数据库列表');
      RootNode.ImageIndex := 0;
      RootNode.SelectedIndex := 0;
    end;

    // 添加数据库节点
    DBNode := tvDatabases.Items.AddChild(RootNode, AName + ' (' + APath + ')');
    DBNode.ImageIndex := 0;
    DBNode.SelectedIndex := 0;
    DBNode.Data := Pointer(DBInfo);

    // 展开根节点
    RootNode.Expand(False);

    // 保存数据库列表
    SaveDatabaseList;
  end;
end;

procedure TfrmMain.ConnectToDatabase(const APath: string; ADBType: DBConnection.TDBType);
var
  DBManager: DBConnection.TDBConnectionManager;
  DBName: string;
  i: Integer;
  Found: Boolean;
  RootNode, DBNode: TTreeNode;
  CurrentPath: string;
  CurrentDBType: DBConnection.TDBType;
  CurrentDBName: string;
begin
  if not Assigned(FConnection) then
    Exit;

  // 如果当前已连接，先保存当前连接的数据库信息
  if FConnection.Connected then
  begin
    CurrentPath := FConnection.Params.Database;
    if CurrentPath = '' then
      CurrentPath := FConnection.Params.Values['DefaultDir'];

    // 确定当前数据库类型
    if FConnection.Params.DriverID = 'SQLite' then
      CurrentDBType := dbtSQLite
    else if FConnection.Params.DriverID = 'MSAcc' then
      CurrentDBType := dbtAccess
    else if FConnection.Params.DriverID = 'ODBC' then
      CurrentDBType := dbtParadox
    else
      CurrentDBType := dbtSQLite; // 默认

    CurrentDBName := ExtractFileName(CurrentPath);
    if CurrentDBName = '' then
      CurrentDBName := CurrentPath;

    // 添加当前数据库到历史列表（如果不存在）
    Found := False;
    for i := 0 to FDatabaseList.Count - 1 do
    begin
      if SameText(FDatabaseList[i].Path, CurrentPath) then
      begin
        Found := True;
        Break;
      end;
    end;

    if not Found then
    begin
      AddDatabaseToList(CurrentPath, CurrentDBName, CurrentDBType);

      // 查找或创建根节点
      RootNode := nil;
      for i := 0 to tvDatabases.Items.Count - 1 do
      begin
        if (tvDatabases.Items[i].Level = 0) and (tvDatabases.Items[i].Text = '数据库列表') then
        begin
          RootNode := tvDatabases.Items[i];
          Break;
        end;
      end;

      if not Assigned(RootNode) then
      begin
        RootNode := tvDatabases.Items.Add(nil, '数据库列表');
        RootNode.ImageIndex := 0;
        RootNode.SelectedIndex := 0;
      end;

      // 添加数据库节点
      DBNode := tvDatabases.Items.AddChild(RootNode, CurrentDBName + ' (' + CurrentPath + ')');
      DBNode.ImageIndex := 0;
      DBNode.SelectedIndex := 0;
      DBNode.Data := Pointer(FDatabaseList[FDatabaseList.Count - 1]);

      // 展开根节点
      RootNode.Expand(False);

      // 保存数据库列表
      SaveDatabaseList;
    end;

    // 关闭当前连接
    FConnection.Close;
  end;

  case ADBType of
    dbtSQLite:
      begin
        FConnection.Params.Clear;
        FConnection.Params.DriverID := 'SQLite';
        FConnection.Params.Database := APath;
        // �����ַ���֧��
        FConnection.FormatOptions.MapRules.Clear;
        FConnection.FormatOptions.OwnMapRules := True;
        FConnection.FormatOptions.StrsEmpty2Null := False;
        FConnection.FormatOptions.StrsTrim := False;
        // ʹ��gb2312������ȷ��ʾ�����ַ�
        FConnection.Params.Add('CharacterSet=gb2312');
      end;
    dbtAccess:
      begin
        FConnection.Params.Clear;
        FConnection.Params.DriverID := 'MSAcc';
        FConnection.Params.Database := APath;
        // �����ַ���֧��
        FConnection.FormatOptions.MapRules.Clear;
        FConnection.FormatOptions.OwnMapRules := True;
        FConnection.FormatOptions.StrsEmpty2Null := False;
        FConnection.FormatOptions.StrsTrim := False;
      end;
    dbtParadox:
      begin
        FConnection.Params.Clear;
        FConnection.Params.DriverID := 'ODBC';
        FConnection.Params.Add('DefaultDir=' + APath);
        // �����ַ���֧��
        FConnection.FormatOptions.MapRules.Clear;
        FConnection.FormatOptions.OwnMapRules := True;
        FConnection.FormatOptions.StrsEmpty2Null := False;
        FConnection.FormatOptions.StrsTrim := False;
      end;
  end;

  try
    FConnection.Open;

    // ���ӳɹ�������UI
    DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
    try
      // ��ʾ���ӷ�ʽ��Ϣ
      sbMain.SimpleText := '�����ӵ����ݿ�: ' + APath + ' | ���ӷ�ʽ: ' + DBManager.GetConnectionInfo;
    finally
      DBManager.Free;
    end;

    actConnect.Enabled := False;
    actDisconnect.Enabled := True;
    actRefreshDB.Enabled := True;
    actSearchDB.Enabled := True;
    actConvertDB.Enabled := True;

    // 加载数据库结构
    LoadDatabaseStructure;

    // 获取数据库名称
    DBName := ExtractFileName(APath);
    if DBName = '' then
      DBName := APath;

    // 添加新连接的数据库到历史列表（如果不存在）
    Found := False;
    for i := 0 to FDatabaseList.Count - 1 do
    begin
      if SameText(FDatabaseList[i].Path, APath) then
      begin
        Found := True;
        Break;
      end;
    end;

    if not Found then
    begin
      AddDatabaseToList(APath, DBName, ADBType);

      // 查找或创建根节点
      RootNode := nil;
      for i := 0 to tvDatabases.Items.Count - 1 do
      begin
        if (tvDatabases.Items[i].Level = 0) and (tvDatabases.Items[i].Text = '数据库列表') then
        begin
          RootNode := tvDatabases.Items[i];
          Break;
        end;
      end;

      if not Assigned(RootNode) then
      begin
        RootNode := tvDatabases.Items.Add(nil, '数据库列表');
        RootNode.ImageIndex := 0;
        RootNode.SelectedIndex := 0;
      end;

      // 添加数据库节点
      DBNode := tvDatabases.Items.AddChild(RootNode, DBName + ' (' + APath + ')');
      DBNode.ImageIndex := 0;
      DBNode.SelectedIndex := 0;
      DBNode.Data := Pointer(FDatabaseList[FDatabaseList.Count - 1]);

      // 展开根节点
      RootNode.Expand(False);

      // 保存数据库列表
      SaveDatabaseList;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('连接数据库失败: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.ExportTableData(const ATableName: string);
var
  SaveDialog: TSaveDialog;
  ExportQuery: TFDQuery;
  ExportFile: TextFile;
  i: Integer;
  FieldNames, FieldValues: string;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  SaveDialog := TSaveDialog.Create(nil);
  try
    SaveDialog.Title := '导出表数据';
    SaveDialog.DefaultExt := 'csv';
    SaveDialog.Filter := 'CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*';
    SaveDialog.FileName := ATableName + '.csv';

    if SaveDialog.Execute then
    begin
      ExportQuery := TFDQuery.Create(nil);
      try
        ExportQuery.Connection := FConnection;
        ExportQuery.SQL.Text := Format('SELECT * FROM "%s"', [ATableName]);
        ExportQuery.Open;

        AssignFile(ExportFile, SaveDialog.FileName);
        Rewrite(ExportFile);
        try
          // 写入字段名
          FieldNames := '';
          for i := 0 to ExportQuery.FieldCount - 1 do
          begin
            if i > 0 then
              FieldNames := FieldNames + ',';
            FieldNames := FieldNames + ExportQuery.Fields[i].FieldName;
          end;
          WriteLn(ExportFile, FieldNames);

          // 写入数据
          while not ExportQuery.Eof do
          begin
            FieldValues := '';
            for i := 0 to ExportQuery.FieldCount - 1 do
            begin
              if i > 0 then
                FieldValues := FieldValues + ',';
              FieldValues := FieldValues + ExportQuery.Fields[i].AsString;
            end;
            WriteLn(ExportFile, FieldValues);
            ExportQuery.Next;
          end;

          ShowMessage('数据已成功导出到文件: ' + SaveDialog.FileName);
        finally
          CloseFile(ExportFile);
        end;
      finally
        ExportQuery.Free;
      end;
    end;
  finally
    SaveDialog.Free;
  end;
end;

procedure TfrmMain.ImportTableData(const ATableName: string);
var
  OpenDialog: TOpenDialog;
  ImportFile: TextFile;
  Line, FieldName: string;
  FieldNames, FieldValues: TStringList;
  ImportQuery: TFDQuery;
  InsertSQL: string;
  i: Integer;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  OpenDialog := TOpenDialog.Create(nil);
  try
    OpenDialog.Title := '���������';
    OpenDialog.DefaultExt := 'csv';
    OpenDialog.Filter := 'CSV�ļ� (*.csv)|*.csv|�����ļ� (*.*)|*.*';

    if OpenDialog.Execute then
    begin
      FieldNames := TStringList.Create;
      FieldValues := TStringList.Create;
      ImportQuery := TFDQuery.Create(nil);
      try
        ImportQuery.Connection := FConnection;

        // ��ȡCSV�ļ�
        AssignFile(ImportFile, OpenDialog.FileName);
        Reset(ImportFile);
        try
          // ��ȡ�ֶ���
          if not Eof(ImportFile) then
          begin
            ReadLn(ImportFile, Line);
            FieldNames.CommaText := Line;
          end;

          // ��ʼ������
          FConnection.StartTransaction;
          try
            while not Eof(ImportFile) do
            begin
              ReadLn(ImportFile, Line);
              FieldValues.CommaText := Line;

              // ����INSERT���
              InsertSQL := Format('INSERT INTO "%s" (', [ATableName]);
              for i := 0 to FieldNames.Count - 1 do
              begin
                if i > 0 then
                  InsertSQL := InsertSQL + ', ';
                FieldName := FieldNames[i];
                InsertSQL := InsertSQL + '"' + FieldName + '"';
              end;
              InsertSQL := InsertSQL + ') VALUES (';
              for i := 0 to FieldValues.Count - 1 do
              begin
                if i > 0 then
                  InsertSQL := InsertSQL + ', ';
                InsertSQL := InsertSQL + '''' + StringReplace(FieldValues[i], '''', '''''', [rfReplaceAll]) + '''';
              end;
              InsertSQL := InsertSQL + ')';

              // ִ��INSERT���
              ImportQuery.SQL.Text := InsertSQL;
              ImportQuery.ExecSQL;
            end;

            FConnection.Commit;
            ShowMessage('���ݵ���ɹ����');

            // ˢ�±�������
            LoadTableData(ATableName);
          except
            on E: Exception do
            begin
              FConnection.Rollback;
              ShowMessage('��������ʱ����: ' + E.Message);
            end;
          end;
        finally
          CloseFile(ImportFile);
        end;
      finally
        FieldNames.Free;
        FieldValues.Free;
        ImportQuery.Free;
      end;
    end;
  finally
    OpenDialog.Free;
  end;
end;

procedure TfrmMain.ModifyTableData(const ATableName: string);
begin
  // �˹�����Ҫ�����ӵ�ʵ�֣��������ṹ���������ݱ༭����ȣ���δʵ��
  ShowMessage('�������޸Ĺ�����δʵ��');
end;

procedure TfrmMain.RefreshDatabaseNode(Node: TTreeNode);
var
  DBManager: DBConnection.TDBConnectionManager;

  DBPath: string;
begin
  if not Assigned(Node) then
    Exit;

  if not FConnection.Connected then
    Exit;

  // ��ȡ���ݿ�·��
  DBPath := FConnection.Params.Database;
  if DBPath = '' then
    DBPath := FConnection.Params.Values['DefaultDir'];

  // ��յ�ǰ�ڵ���ӽڵ�
  Node.DeleteChildren;

  // �������ݿ����
  DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
  try
    // ���ر�����Ŀ¼��֯
    LoadTablesIntoDirectories(DBManager, Node, DBPath);
  finally
    DBManager.Free;
  end;
end;

function TfrmMain.FindMirServerDir(const StartDir: string): string;
var
  CurrentDir, ParentDir: string;
  IsMirServerDir: Boolean;
begin
  Result := '';

  // 检查当前目录是否包含MirServer（支持前后缀）
  var DirName := ExtractFileName(ExcludeTrailingPathDelimiter(StartDir));
  if Pos('MirServer', DirName) > 0 then
  begin
    // 进一步检查是否真的是MirServer目录（包含Config.ini和Mud2\DB）
    if FileExists(IncludeTrailingPathDelimiter(StartDir) + 'Config.ini') and
       DirectoryExists(IncludeTrailingPathDelimiter(StartDir) + 'Mud2') and
       DirectoryExists(IncludeTrailingPathDelimiter(StartDir) + 'Mud2\DB') then
    begin
      Result := IncludeTrailingPathDelimiter(StartDir);
      Exit;
    end;
  end;

  // 检查当前目录下是否有包含MirServer的子目录
  var SearchRec: TSearchRec;
  if FindFirst(IncludeTrailingPathDelimiter(StartDir) + '*', faDirectory, SearchRec) = 0 then
  begin
    try
      repeat
        if (SearchRec.Attr and faDirectory <> 0) and
           (SearchRec.Name <> '.') and (SearchRec.Name <> '..') and
           (Pos('MirServer', SearchRec.Name) > 0) then
        begin
          var SubDir := IncludeTrailingPathDelimiter(StartDir) + SearchRec.Name;
          // 检查是否真的是MirServer目录
          if FileExists(SubDir + '\Config.ini') and
             DirectoryExists(SubDir + '\Mud2') and
             DirectoryExists(SubDir + '\Mud2\DB') then
          begin
            Result := IncludeTrailingPathDelimiter(SubDir);
            Exit;
          end;
        end;
      until FindNext(SearchRec) <> 0;
    finally
      FindClose(SearchRec);
    end;
  end;

  // 向上级目录递归查找
  CurrentDir := ExcludeTrailingPathDelimiter(StartDir);
  ParentDir := ExtractFileDir(CurrentDir);

  // 如果已到达根目录，则退出
  if (ParentDir = CurrentDir) or (ParentDir = '') then
    Exit;

  // 递归查找上级目录
  Result := FindMirServerDir(ParentDir);
end;

function TfrmMain.AutoDetectDatabase(const AGameDir: string): Boolean;
var
  MirServerPath, ConfigFile, DBType, DBFile, AccessFileName, ParadoxDir, ParentDir: string;
  UseAccessDB: Integer;
begin
  Result := False;

  // ���ȳ��Բ���MirServerĿ¼��λ��
  MirServerPath := FindMirServerDir(AGameDir);

  // ���û���ҵ�MirServerĿ¼��������������
  if MirServerPath = '' then
  begin
    // ��鵱ǰĿ¼�Ƿ����Mud2\DBĿ¼��������������MirServerĿ¼
    if DirectoryExists(IncludeTrailingPathDelimiter(AGameDir) + 'Mud2') and
       DirectoryExists(IncludeTrailingPathDelimiter(AGameDir) + 'Mud2\DB') then
    begin
      MirServerPath := IncludeTrailingPathDelimiter(AGameDir);
    end
    else
    begin
      // ��鸸Ŀ¼�Ƿ����Mud2\DBĿ¼
      ParentDir := ExtractFilePath(ExcludeTrailingPathDelimiter(AGameDir));
      if DirectoryExists(IncludeTrailingPathDelimiter(ParentDir) + 'Mud2') and
         DirectoryExists(IncludeTrailingPathDelimiter(ParentDir) + 'Mud2\DB') then
      begin
        MirServerPath := IncludeTrailingPathDelimiter(ParentDir);
      end
      else
      begin
        // ������Ҳ�����ʹ�õ�ǰĿ¼
        MirServerPath := IncludeTrailingPathDelimiter(AGameDir);
      end;
    end;
  end;

  // ȷ��·���Է�б�ܽ�β
  MirServerPath := IncludeTrailingPathDelimiter(MirServerPath);

  // ����Config.ini�ļ�
  ConfigFile := MirServerPath + 'Config.ini';
  if not FileExists(ConfigFile) then
  begin
    ShowMessage('�Ҳ���Config.ini�ļ����޷��Զ�������ݿ����ͣ����ֶ�ѡ�����ݿ��ļ�');
    Exit(False);
  end;

  // ��Config.ini�ж�ȡ���ݿ�����
  DBType := ReadIniValue(ConfigFile, 'DB', 'DBType', '');

  // ��������ļ���ָ����SQLite���ݿ�
  if SameText(DBType, 'SQLite') then
  begin
    // ��ȡSQLite���ݿ��ļ�·��
    DBFile := ReadIniValue(ConfigFile, 'DB', 'DBFile', '');

    // ���û��ָ��DBFile��������Mud2\DBĿ¼�²��ҳ�����SQLite���ݿ��ļ�
    if DBFile = '' then
    begin
      var DBPath := MirServerPath + 'Mud2\DB\';
      if DirectoryExists(DBPath) then
      begin
        if FileExists(DBPath + 'mir2.db') then
          DBFile := DBPath + 'mir2.db'
        else if FileExists(DBPath + 'game.db') then
          DBFile := DBPath + 'game.db'
        else if FileExists(DBPath + 'HeroDB.db') then
          DBFile := DBPath + 'HeroDB.db'
        else if FileExists(DBPath + 'ApexM2.DB') then
          DBFile := DBPath + 'ApexM2.DB';
      end;
    end
    else
    begin
      // ���DBFile���Ǿ���·���������ڲ�ͬλ�ò���
      if not FileExists(DBFile) and (Length(DBFile) > 0) then
      begin
        // ����Ƿ��Ǿ���·��
        if (DBFile[1] = PathDelim) or ((Length(DBFile) > 2) and (DBFile[2] = ':')) then
        begin
          // ����Ǿ���·�����ļ������ڣ�����
          ShowMessage('Config.ini��ָ����SQLite���ݿ��ļ�������: ' + DBFile);
          Exit(False);
        end
        else
        begin
          // ������MirServerĿ¼��
          if FileExists(MirServerPath + DBFile) then
            DBFile := MirServerPath + DBFile
          // ������Mud2\DBĿ¼��
          else if FileExists(MirServerPath + 'Mud2\DB\' + DBFile) then
            DBFile := MirServerPath + 'Mud2\DB\' + DBFile
          else
          begin
            ShowMessage('�Ҳ���SQLite���ݿ��ļ�: ' + DBFile);
            Exit(False);
          end;
        end;
      end;
    end;

    if (DBFile <> '') and FileExists(DBFile) then
    begin
      // ���ӵ�SQLite���ݿ�
      ConnectToDatabase(DBFile, dbtSQLite);
      Result := True;
      Exit; // ���ӳɹ������ؽ��
    end
    else
    begin
      ShowMessage('�޷��ҵ���Ч��SQLite���ݿ��ļ�');
      Exit(False);
    end;
  end
  // ���û��ָ�����ݿ����ͣ����Access����
  else if DBType = '' then
  begin
    // ����Ƿ�ʹ��Access���ݿ�
    UseAccessDB := StrToIntDef(ReadIniValue(ConfigFile, 'DB', 'UseAccessDB', '0'), 0);

    if UseAccessDB = 1 then
    begin
      // ��ȡAccess���ݿ��ļ�·��
      AccessFileName := ReadIniValue(ConfigFile, 'DB', 'AccessFileName', '');

      // ���û��ָ��AccessFileName��������Mud2\DBĿ¼�²��ҳ�����Access���ݿ��ļ�
      if AccessFileName = '' then
      begin
        var DBPath := MirServerPath + 'Mud2\DB\';
        if DirectoryExists(DBPath) then
        begin
          if FileExists(DBPath + 'Mir2.mdb') then
            AccessFileName := DBPath + 'Mir2.mdb'
          else if FileExists(DBPath + 'GameData.mdb') then
            AccessFileName := DBPath + 'GameData.mdb'
          else if FileExists(DBPath + 'HeroDB.mdb') then
            AccessFileName := DBPath + 'HeroDB.mdb';
        end;
      end
      else
      begin
        // ���AccessFileName���Ǿ���·���������ڲ�ͬλ�ò���
        if not FileExists(AccessFileName) and (Length(AccessFileName) > 0) then
        begin
          // ����Ƿ��Ǿ���·��
          if (AccessFileName[1] = PathDelim) or ((Length(AccessFileName) > 2) and (AccessFileName[2] = ':')) then
          begin
            // ����Ǿ���·�����ļ������ڣ�����
            ShowMessage('Config.ini��ָ����Access���ݿ��ļ�������: ' + AccessFileName);
            Exit(False);
          end
          else
          begin
            // ������MirServerĿ¼��
            if FileExists(MirServerPath + AccessFileName) then
              AccessFileName := MirServerPath + AccessFileName
            // ������Mud2\DBĿ¼��
            else if FileExists(MirServerPath + 'Mud2\DB\' + AccessFileName) then
              AccessFileName := MirServerPath + 'Mud2\DB\' + AccessFileName
            else
            begin
              ShowMessage('�Ҳ���Access���ݿ��ļ�: ' + AccessFileName);
              Exit(False);
            end;
          end;
        end;
      end;

      if (AccessFileName <> '') and FileExists(AccessFileName) then
      begin
        // ���ӵ�Access���ݿ�
        ConnectToDatabase(AccessFileName, dbtAccess);
        Result := True;
        Exit; // ���ӳɹ������ؽ��
      end
      else
      begin
        ShowMessage('�޷��ҵ���Ч��Access���ݿ��ļ�');
        Exit(False);
      end;
    end
    // ���UseAccessDB=0����������Paradox���ݿ�
    else if UseAccessDB = 0 then
    begin
      // ����Mud2\DBĿ¼�µ�Paradox���ݿ�
      ParadoxDir := MirServerPath + 'Mud2\DB\';

      if DirectoryExists(ParadoxDir) then
      begin
        // ���ӵ�Paradox���ݿ�
        ConnectToDatabase(ParadoxDir, dbtParadox);
        Result := True;
        Exit; // ���ӳɹ������ؽ��
      end
      else
      begin
        ShowMessage('�޷��ҵ���Ч��Paradox���ݿ�Ŀ¼: ' + ParadoxDir);
        Exit(False);
      end;
    end
  end;

  // ����������з������޷��ҵ���Ч�����ݿ⣬��ʾ�û��ֶ�ѡ��
  ShowMessage('�޷��Զ�������ݿ����ͻ��Ҳ�����Ч�����ݿ��ļ�������Config.ini���û��ֶ�ѡ�����ݿ��ļ�');
  Exit(False);
end;

procedure TfrmMain.actExportDataExecute(Sender: TObject);
begin
  if FCurrentTable <> '' then
    ExportTableData(FCurrentTable)
  else
    ShowMessage('����ѡ��һ����');
end;

procedure TfrmMain.actImportDataExecute(Sender: TObject);
begin
  if FCurrentTable <> '' then
    ImportTableData(FCurrentTable)
  else
    ShowMessage('����ѡ��һ����');
end;

procedure TfrmMain.actRefreshDataExecute(Sender: TObject);
begin
  if FCurrentTable <> '' then
    LoadTableData(FCurrentTable)
  else
    ShowMessage('����ѡ��һ����');
end;

procedure TfrmMain.actModifyDataExecute(Sender: TObject);
begin
  if FCurrentTable <> '' then
    ModifyTableData(FCurrentTable)
  else
    ShowMessage('����ѡ��һ����');
end;

procedure TfrmMain.actRefreshDBExecute(Sender: TObject);
begin
  if FConnection.Connected then
    LoadDatabaseStructure
  else
    ShowMessage('�������ӵ����ݿ�');
end;

procedure TfrmMain.actSearchDBExecute(Sender: TObject);
var
  SearchText: string;
begin
  if not FConnection.Connected then
  begin
    ShowMessage('�������ӵ����ݿ�');
    Exit;
  end;

  if InputQuery('�������ݿ�', '�����������ؼ���:', SearchText) then
  begin
    if SearchText <> '' then
      SearchDatabase(SearchText)
    else
      ShowMessage('�����������ؼ���');
  end;
end;

procedure TfrmMain.actConvertDBExecute(Sender: TObject);
begin
  ShowMessage('���ݿ�ת��������δʵ��');
end;

procedure TfrmMain.tvDatabasesMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
var
  Node: TTreeNode;

begin
  if Button = mbRight then
  begin
    Node := tvDatabases.GetNodeAt(X, Y);
    if Assigned(Node) then
    begin
      tvDatabases.Selected := Node;

      // ���ݽڵ�������ʾ��ͬ�Ĳ˵���
      if Node.Level = 0 then
      begin
        // ���ݿ���ڵ�˵���
        miRefreshDB.Visible := True;
        miSearchDB.Visible := True;
        miConvertDB.Visible := True;
        miExportData.Visible := False;
        miImportData.Visible := False;
        miRefreshData.Visible := False;
        miModifyData.Visible := False;
      end
      else if Node.Level = 1 then
      begin
        // ���ڵ�˵���
        miRefreshDB.Visible := False;
        miSearchDB.Visible := False;
        miConvertDB.Visible := False;
        miExportData.Visible := True;
        miImportData.Visible := True;
        miRefreshData.Visible := True;
        miModifyData.Visible := True;

        // ���õ�ǰ����
        FCurrentTable := Node.Text;
      end;

      // ��ʾ�����˵�
      pmTreeView.Popup(Mouse.CursorPos.X, Mouse.CursorPos.Y);
    end;
  end;
end;

procedure TfrmMain.dbgDataMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
begin
  if Button = mbRight then
  begin
    // �����λ����ʾ�������񵯳��˵�
    pmDBGrid.Popup(Mouse.CursorPos.X, Mouse.CursorPos.Y);
  end;
end;

procedure TfrmMain.SearchDatabase(const SearchText: string);
var
  SearchQuery: TFDQuery;
  Tables: TStringList;
  i: Integer;
  TableName: string;
  ResultsForm: TForm;
  ResultsMemo: TMemo;
  SQL: string;
begin
  if not FConnection.Connected then
    Exit;

  // �����������
  ResultsForm := TForm.Create(Self);
  try
    ResultsForm.Caption := '�������: ' + SearchText;
    ResultsForm.Width := 600;
    ResultsForm.Height := 400;
    ResultsForm.Position := poScreenCenter;

    ResultsMemo := TMemo.Create(ResultsForm);
    ResultsMemo.Parent := ResultsForm;
    ResultsMemo.Align := alClient;
    ResultsMemo.ScrollBars := ssBoth;
    ResultsMemo.ReadOnly := True;

    // ��ȡ���б�
    Tables := TStringList.Create;
    try
      // ������ѯ����
      SearchQuery := TFDQuery.Create(nil);
      try
        SearchQuery.Connection := FConnection;

        // ��ȡ���б�
        var DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
        try
          Tables := DBManager.GetTableNames;
        finally
          DBManager.Free;
        end;

        // ��ÿ����������
        for i := 0 to Tables.Count - 1 do
        begin
          TableName := Tables[i];
          ResultsMemo.Lines.Add('����������: ' + TableName);
          Application.ProcessMessages;

          try
            // ������ѯSQL
            SQL := 'SELECT * FROM "' + TableName + '"';
            SearchQuery.SQL.Text := SQL;
            SearchQuery.Open;

            // ����ÿһ��
            while not SearchQuery.Eof do
            begin
              var FoundInRow := False;
              var RowText := '';

              // ���ÿ���ֶ�
              for var j := 0 to SearchQuery.FieldCount - 1 do
              begin
                var FieldValue := SearchQuery.Fields[j].AsString;
                if Pos(LowerCase(SearchText), LowerCase(FieldValue)) > 0 then
                begin
                  FoundInRow := True;
                  RowText := RowText + SearchQuery.Fields[j].FieldName + '=' + FieldValue + ', ';
                end;
              end;

              if FoundInRow then
              begin
                ResultsMemo.Lines.Add('  �ҵ�ƥ��: ' + RowText);
              end;

              SearchQuery.Next;
            end;

            SearchQuery.Close;
          except
            on E: Exception do
            begin
              ResultsMemo.Lines.Add('  ������ʱ����: ' + E.Message);
            end;
          end;
        end;

        ResultsMemo.Lines.Add('�������');
      finally
        SearchQuery.Free;
      end;
    finally
      Tables.Free;
    end;

    // ��ʾ�������
    ResultsForm.ShowModal;
  finally
    ResultsForm.Free;
  end;
end;

procedure TfrmMain.ConvertDatabase(const SourcePath, TargetPath: string; SourceType, TargetType: DBConnection.TDBType);
begin
  ShowMessage('���ݿ�ת��������δʵ��');
end;

function TfrmMain.ReadIniValue(const FileName, Section, Key, DefaultValue: string): string;
var
  IniFile: TIniFile;
begin
  Result := DefaultValue;

  if not FileExists(FileName) then
    Exit;

  IniFile := TIniFile.Create(FileName);
  try
    Result := IniFile.ReadString(Section, Key, DefaultValue);
  finally
    IniFile.Free;
  end;
end;



end.