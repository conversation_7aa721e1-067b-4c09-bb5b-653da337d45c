unit MainForm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ComCtrls, Vcl.ExtCtr<PERSON>,
  Vcl.<PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, System.ImageList, Vcl.ImgList,
  Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error, FireDAC.UI.Intf,
  FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool, FireDAC.Stan.Async,
  FireDAC.Phys, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs,
  FireDAC.VCLUI.Wait, FireDAC.Comp.Client, System.Actions, Vcl.ActnList, Vcl.DBGrids,
  FireDAC.Comp.DataSet, System.Generics.Collections, DBConnection, System.IniFiles;

type
  TDatabaseInfo = class
  public
    Path: string;
    Name: string;
    DBType: DBConnection.TDBType;
    constructor Create(const APath, AName: string; ADBType: DBConnection.TDBType);
  end;
  TfrmMain = class(TForm)
    pnlMain: TPanel;
    sbMain: TStatusBar;
    mmMain: TMainMenu;
    miFile: TMenuItem;
    miConnect: TMenuItem;
    miDisconnect: TMenuItem;
    N1: TMenuItem;
    miExit: TMenuItem;
    miEdit: TMenuItem;
    miHelp: TMenuItem;
    miAbout: TMenuItem;
    tbMain: TToolBar;
    ilMain: TImageList;
    pnlLeft: TPanel;
    tvDatabases: TTreeView;
    splVertical: TSplitter;
    pcRight: TPageControl;
    tsData: TTabSheet;
    alMain: TActionList;
    actConnect: TAction;
    actDisconnect: TAction;
    actExit: TAction;
    btnConnect: TToolButton;
    btnDisconnect: TToolButton;
    dbgData: TDBGrid;
    dsData: TDataSource;
    pmTreeView: TPopupMenu;
    pmDBGrid: TPopupMenu;
    actExportData: TAction;
    actImportData: TAction;
    actRefreshData: TAction;
    actModifyData: TAction;
    actRefreshDB: TAction;
    actSearchDB: TAction;
    actConvertDB: TAction;
    miExportData: TMenuItem;
    miImportData: TMenuItem;
    miRefreshData: TMenuItem;
    miModifyData: TMenuItem;
    miRefreshDB: TMenuItem;
    miSearchDB: TMenuItem;
    miConvertDB: TMenuItem;
    btnExportData: TToolButton;
    btnImportData: TToolButton;
    btnRefreshData: TToolButton;
    btnModifyData: TToolButton;
    btnRefreshDB: TToolButton;
    btnSearchDB: TToolButton;
    btnConvertDB: TToolButton;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure actConnectExecute(Sender: TObject);
    procedure actDisconnectExecute(Sender: TObject);
    procedure actExitExecute(Sender: TObject);
    procedure tvDatabasesDblClick(Sender: TObject);
    procedure actExportDataExecute(Sender: TObject);
    procedure actImportDataExecute(Sender: TObject);
    procedure actRefreshDataExecute(Sender: TObject);
    procedure actModifyDataExecute(Sender: TObject);
    procedure actRefreshDBExecute(Sender: TObject);
    procedure actSearchDBExecute(Sender: TObject);
    procedure actConvertDBExecute(Sender: TObject);
    procedure tvDatabasesMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
    procedure dbgDataMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
  private
    { Private declarations }
    FConnection: TFDConnection;
    FCurrentQuery: TFDQuery;
    FCurrentTable: string;
    FDatabaseList: TObjectList<TDatabaseInfo>;
    FConfigFileName: string;
    procedure LoadIcons;
    procedure SaveDatabaseList;
    procedure LoadDatabaseList;
    procedure InitializeControls;
    procedure DisconnectFromDatabase;
    procedure LoadDatabaseStructure;
    procedure LoadTablesIntoDirectories(DBManager: DBConnection.TDBConnectionManager; RootNode: TTreeNode; const DBPath: string);
    procedure LoadTableData(const ATableName: string);
    procedure SaveDatabaseList;
    procedure LoadDatabaseList;
    procedure AddDatabaseToList(const APath, AName: string; ADBType: DBConnection.TDBType);
    procedure ConnectToDatabase(const APath: string; ADBType: DBConnection.TDBType);
    function FindMirServerDir(const StartDir: string): string;
    function AutoDetectDatabase(const AGameDir: string): Boolean;
    procedure RefreshDatabaseNode(Node: TTreeNode);
    procedure ExportTableData(const ATableName: string);
    procedure ImportTableData(const ATableName: string);
    procedure ModifyTableData(const ATableName: string);
    procedure SearchDatabase(const SearchText: string);
    procedure ConvertDatabase(const SourcePath, TargetPath: string; SourceType, TargetType: DBConnection.TDBType);
    function ReadIniValue(const FileName, Section, Key, DefaultValue: string): string;
  public
    { Public declarations }
  end;

var
  frmMain: TfrmMain;

implementation

{$R *.dfm}

uses
  ConnectionDialog;

constructor TDatabaseInfo.Create(const APath, AName: string; ADBType: DBConnection.TDBType);
begin
  inherited Create;
  Path := APath;
  Name := AName;
  DBType := ADBType;
end;

procedure TfrmMain.FormCreate(Sender: TObject);
begin
  // ???????Action
  if not Assigned(actExportData) then
  begin
    actExportData := TAction.Create(alMain);
    actExportData.Caption := '??????????(&E)';
    actExportData.OnExecute := actExportDataExecute;
    actExportData.ActionList := alMain;
  end;

  if not Assigned(actImportData) then
  begin
    actImportData := TAction.Create(alMain);
    actImportData.Caption := '?????????(&I)';
    actImportData.OnExecute := actImportDataExecute;
    actImportData.ActionList := alMain;
  end;

  if not Assigned(actRefreshData) then
  begin
    actRefreshData := TAction.Create(alMain);
    actRefreshData.Caption := '??±?????(&R)';
    actRefreshData.OnExecute := actRefreshDataExecute;
    actRefreshData.ActionList := alMain;
  end;

  if not Assigned(actModifyData) then
  begin
    actModifyData := TAction.Create(alMain);
    actModifyData.Caption := '????????(&M)';
    actModifyData.OnExecute := actModifyDataExecute;
    actModifyData.ActionList := alMain;
  end;

  if not Assigned(actRefreshDB) then
  begin
    actRefreshDB := TAction.Create(alMain);
    actRefreshDB.Caption := '????????(&F)';
    actRefreshDB.OnExecute := actRefreshDBExecute;
    actRefreshDB.ActionList := alMain;
  end;

  if not Assigned(actSearchDB) then
  begin
    actSearchDB := TAction.Create(alMain);
    actSearchDB.Caption := '?????????(&S)';
    actSearchDB.OnExecute := actSearchDBExecute;
    actSearchDB.ActionList := alMain;
  end;

  if not Assigned(actConvertDB) then
  begin
    actConvertDB := TAction.Create(alMain);
    actConvertDB.Caption := '????????(&C)';
    actConvertDB.OnExecute := actConvertDBExecute;
    actConvertDB.ActionList := alMain;
  end;

  // ???????????
  if not Assigned(pmTreeView) then
    pmTreeView := TPopupMenu.Create(Self);

  if not Assigned(pmDBGrid) then
    pmDBGrid := TPopupMenu.Create(Self);

  // ?????????????
  if not Assigned(btnExportData) then
  begin
    btnExportData := TToolButton.Create(tbMain);
    btnExportData.Parent := tbMain;
    btnExportData.Action := actExportData;
  end;

  if not Assigned(btnImportData) then
  begin
    btnImportData := TToolButton.Create(tbMain);
    btnImportData.Parent := tbMain;
    btnImportData.Action := actImportData;
  end;

  if not Assigned(btnRefreshData) then
  begin
    btnRefreshData := TToolButton.Create(tbMain);
    btnRefreshData.Parent := tbMain;
    btnRefreshData.Action := actRefreshData;
  end;

  if not Assigned(btnModifyData) then
  begin
    btnModifyData := TToolButton.Create(tbMain);
    btnModifyData.Parent := tbMain;
    btnModifyData.Action := actModifyData;
  end;

  if not Assigned(btnRefreshDB) then
  begin
    btnRefreshDB := TToolButton.Create(tbMain);
    btnRefreshDB.Parent := tbMain;
    btnRefreshDB.Action := actRefreshDB;
  end;

  if not Assigned(btnSearchDB) then
  begin
    btnSearchDB := TToolButton.Create(tbMain);
    btnSearchDB.Parent := tbMain;
    btnSearchDB.Action := actSearchDB;
  end;

  if not Assigned(btnConvertDB) then
  begin
    btnConvertDB := TToolButton.Create(tbMain);
    btnConvertDB.Parent := tbMain;
    btnConvertDB.Action := actConvertDB;
  end;

  InitializeControls;

  // 创建数据库列表
  FDatabaseList := TObjectList<TDatabaseInfo>.Create(True); // True表示自动释放对象

  // 设置配置文件名
  FConfigFileName := ChangeFileExt(Application.ExeName, '.ini');

  // 加载数据库列表
  LoadDatabaseList;

  // 创建连接对象
  FConnection := TFDConnection.Create(Self);
  FConnection.Params.DriverID := 'SQLite';

  // 创建查询对象
  FCurrentQuery := TFDQuery.Create(Self);
  dsData.DataSet := FCurrentQuery;

  // 加载图标
  LoadIcons;
end;

procedure TfrmMain.FormDestroy(Sender: TObject);
begin
  // 保存数据库列表
  SaveDatabaseList;

  if FConnection.Connected then
    FConnection.Close;

  // 释放查询对象
  if Assigned(FCurrentQuery) then
    FCurrentQuery.Free;

  // 释放数据库列表
  FDatabaseList.Free;

  FConnection.Free;
end;

procedure TfrmMain.InitializeControls;
begin
  pcRight.ActivePage := tsData;
  actDisconnect.Enabled := False;

  // ?????Action
  actExportData.Enabled := False;
  actImportData.Enabled := False;
  actRefreshData.Enabled := False;
  actModifyData.Enabled := False;
  actRefreshDB.Enabled := False;
  actSearchDB.Enabled := False;
  actConvertDB.Enabled := False;

  // ????Action???????
  actExportData.ImageIndex := 1; // ????????????
  actImportData.ImageIndex := 3; // ????????????
  actRefreshData.ImageIndex := 4; // ????????????
  actModifyData.ImageIndex := 5; // ????????????
  actRefreshDB.ImageIndex := 2; // ???????????
  actSearchDB.ImageIndex := 8; // ????????????
  actConvertDB.ImageIndex := 9; // ???????????

  // ????Action???
  actExportData.Hint := '??????????';
  actImportData.Hint := '?????????';
  actRefreshData.Hint := '??±?????';
  actModifyData.Hint := '????????';
  actRefreshDB.Hint := '????????';
  actSearchDB.Hint := '?????????';
  actConvertDB.Hint := '????????';

  // ?????TreeView??????
  if not Assigned(pmTreeView) then
    pmTreeView := TPopupMenu.Create(Self);

  // ????????
  if not Assigned(miExportData) then
  begin
    miExportData := TMenuItem.Create(pmTreeView);
    miExportData.Action := actExportData;
    pmTreeView.Items.Add(miExportData);
  end;

  if not Assigned(miImportData) then
  begin
    miImportData := TMenuItem.Create(pmTreeView);
    miImportData.Action := actImportData;
    pmTreeView.Items.Add(miImportData);
  end;

  if not Assigned(miRefreshData) then
  begin
    miRefreshData := TMenuItem.Create(pmTreeView);
    miRefreshData.Action := actRefreshData;
    pmTreeView.Items.Add(miRefreshData);
  end;

  if not Assigned(miModifyData) then
  begin
    miModifyData := TMenuItem.Create(pmTreeView);
    miModifyData.Action := actModifyData;
    pmTreeView.Items.Add(miModifyData);
  end;

  if not Assigned(miRefreshDB) then
  begin
    miRefreshDB := TMenuItem.Create(pmTreeView);
    miRefreshDB.Action := actRefreshDB;
    pmTreeView.Items.Add(miRefreshDB);
  end;

  if not Assigned(miSearchDB) then
  begin
    miSearchDB := TMenuItem.Create(pmTreeView);
    miSearchDB.Action := actSearchDB;
    pmTreeView.Items.Add(miSearchDB);
  end;

  if not Assigned(miConvertDB) then
  begin
    miConvertDB := TMenuItem.Create(pmTreeView);
    miConvertDB.Action := actConvertDB;
    pmTreeView.Items.Add(miConvertDB);
  end;

  // ????TreeView???????
  tvDatabases.PopupMenu := pmTreeView;
  tvDatabases.OnMouseDown := tvDatabasesMouseDown;

  // ????DBGrid???????
  dbgData.PopupMenu := pmDBGrid;
  dbgData.OnMouseDown := dbgDataMouseDown;
end;

procedure TfrmMain.actConnectExecute(Sender: TObject);
var
  ConnectionDlg: TfrmConnectionDialog;
begin
  ConnectionDlg := TfrmConnectionDialog.Create(Self, FConnection);
  try
    if ConnectionDlg.Execute then
    begin
      // ????????????UI
      var DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
      try
        // ????????????
        sbMain.SimpleText := '????????????: ' + FConnection.Params.Database + ' | ??????: ' + DBManager.GetConnectionInfo;
      finally
        DBManager.Free;
      end;

      // ????Action??
      actConnect.Enabled := False;
      actDisconnect.Enabled := True;
      actRefreshDB.Enabled := True;
      actSearchDB.Enabled := True;
      actConvertDB.Enabled := True;

      // ??????????
      LoadDatabaseStructure;
    end;
  finally
    ConnectionDlg.Free;
  end;
end;

procedure TfrmMain.actDisconnectExecute(Sender: TObject);
begin
  DisconnectFromDatabase;
end;

procedure TfrmMain.DisconnectFromDatabase;
var
  i: Integer;
  RootNode: TTreeNode;
begin
  if FConnection.Connected then
    FConnection.Close;

  sbMain.SimpleText := '已断开连接';

  // 更新Action状态
  actConnect.Enabled := True;
  actDisconnect.Enabled := False;
  actExportData.Enabled := False;
  actImportData.Enabled := False;
  actRefreshData.Enabled := False;
  actModifyData.Enabled := False;
  actRefreshDB.Enabled := False;
  actSearchDB.Enabled := False;
  actConvertDB.Enabled := False;

  // 清空当前表名
  FCurrentTable := '';

  // 清空当前数据库节点，但保留数据库列表节点
  for i := tvDatabases.Items.Count - 1 downto 0 do
  begin
    if (tvDatabases.Items[i].Level = 0) and (tvDatabases.Items[i].Text <> '数据库列表') then
    begin
      tvDatabases.Items[i].Delete;
    end;
  end;
end;

procedure TfrmMain.LoadDatabaseStructure;
var
  DBManager: DBConnection.TDBConnectionManager;
  RootNode: TTreeNode;
  DBPath, DBName: string;
begin
  // 清空树视图中的当前数据库节点
  for var i := tvDatabases.Items.Count - 1 downto 0 do
  begin
    if (tvDatabases.Items[i].Level = 0) and (tvDatabases.Items[i].Text <> '数据库列表') then
    begin
      tvDatabases.Items[i].Delete;
    end;
  end;

  if not FConnection.Connected then
    Exit;

  // 获取数据库路径
  DBPath := FConnection.Params.Database;
  if DBPath = '' then
    DBPath := FConnection.Params.Values['DefaultDir'];

  // 获取数据库名称（使用目录名）
  if DBPath <> '' then
  begin
    // 去除末尾的路径分隔符
    if (Length(DBPath) > 0) and (DBPath[Length(DBPath)] = PathDelim) then
      DBPath := Copy(DBPath, 1, Length(DBPath) - 1);

    // 获取最后一级目录名
    DBName := ExtractFileName(DBPath);
    if DBName = '' then
      DBName := DBPath;
  end
  else
    DBName := '数据库';

  // 创建根节点（使用数据库名称）
  RootNode := tvDatabases.Items.Add(nil, DBName);
  RootNode.ImageIndex := 0; // 数据库图标索引
  RootNode.SelectedIndex := 0;

  // 展开根节点
  RootNode.Expand(False);

  // 加载数据库对象
  DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
  try
    // 加载表并按目录组织
    LoadTablesIntoDirectories(DBManager, RootNode, DBPath);
  finally
    DBManager.Free;
  end;
end;

procedure TfrmMain.actExitExecute(Sender: TObject);
begin
  Close;
end;

procedure TfrmMain.LoadTableData(const ATableName: string);
var
  DBManager: DBConnection.TDBConnectionManager;
  IsSQLite: Boolean;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  try
    // ???浱?????
    FCurrentTable := ATableName;

    // ??????????
    tsData.Caption := '?????: ' + ATableName;

    // ?л????????
    pcRight.ActivePage := tsData;

    // ?????????
    if Assigned(FCurrentQuery) then
    begin
      FCurrentQuery.Close;

      // ????????????
      FCurrentQuery.Connection := FConnection;
      FCurrentQuery.FormatOptions.MapRules.Clear;
      FCurrentQuery.FormatOptions.OwnMapRules := True;
      FCurrentQuery.FormatOptions.StrsEmpty2Null := False;
      FCurrentQuery.FormatOptions.StrsTrim := False;

      // ?????????????????????SQL???
      DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
      try
        IsSQLite := (DBManager.DBType = dbtSQLite);
      finally
        DBManager.Free;
      end;

      if IsSQLite then
        // SQLite?????????Χ????
        FCurrentQuery.SQL.Text := Format('SELECT * FROM "%s"', [ATableName])
      else
        // Access??÷??????Χ????
        FCurrentQuery.SQL.Text := Format('SELECT * FROM [%s]', [ATableName]);

      // ????
      FCurrentQuery.Open;

      // ???????????
      DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
      try
        sbMain.SimpleText := Format('?????? "%s"???? %d ????? | ?????: %s | ??????: %s',
          [ATableName, FCurrentQuery.RecordCount, FConnection.Params.Database, DBManager.GetConnectionInfo]);
      finally
        DBManager.Free;
      end;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('????????????: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.tvDatabasesDblClick(Sender: TObject);
var
  Node: TTreeNode;
  ObjectName: string;
  DBInfo: TDatabaseInfo;
begin
  Node := tvDatabases.Selected;
  if not Assigned(Node) then
    Exit;

  // 处理数据库节点（第1级节点）
  if (Node.Level = 1) and Assigned(Node.Data) then
  begin
    // 获取数据库信息
    DBInfo := TDatabaseInfo(Node.Data);

    // 连接到数据库
    ConnectToDatabase(DBInfo.Path, DBInfo.DBType);
    Exit;
  end;

  // 以下处理表节点（第2级节点）
  if not FConnection.Connected then
    Exit;

  if (Node.Level = 2) then
  begin
    // 获取表名
    ObjectName := Node.Text;

    // 保存当前表名
    FCurrentTable := ObjectName;

    // 启用表相关的Action
    actExportData.Enabled := True;
    actImportData.Enabled := True;
    actRefreshData.Enabled := True;
    actModifyData.Enabled := True;

    // 在右侧面板显示表数据
    LoadTableData(ObjectName);
  end;
end;

procedure TfrmMain.LoadTablesIntoDirectories(DBManager: DBConnection.TDBConnectionManager; RootNode: TTreeNode; const DBPath: string);
var
  Tables: TStringList;
  i: Integer;
  TableName: string;
  TableNode: TTreeNode;
  DBName: string;
begin
  // 获取表列表
  Tables := DBManager.GetTableNames;
  try
    if (Tables = nil) or (Tables.Count = 0) then
      Exit;

    // 按字母顺序排序表名
    Tables.Sort;

    // 获取数据库名称
    DBName := ExtractFileName(ExcludeTrailingPathDelimiter(DBPath));
    if DBName = '' then
      DBName := DBPath;

    // 将表列表添加到根节点下
    for i := 0 to Tables.Count - 1 do
    begin
      TableName := Tables[i];

      // 添加表节点
      TableNode := tvDatabases.Items.AddChild(RootNode, TableName);
      TableNode.ImageIndex := 1; // 表图标索引
      TableNode.SelectedIndex := 1;
    end;

    // 展开根节点
    RootNode.Expand(False);
  finally
    if Assigned(Tables) then
      Tables.Free;
  end;
end;

procedure TfrmMain.LoadIcons;
var
  Icon: TIcon;
  Bitmap: TBitmap;
  IconPath: string;
begin
  // ???????б?
  ilMain.Clear;

  // ???????·??
  IconPath := ExtractFilePath(Application.ExeName) + 'icon\';

  // ????????????
  if FileExists(IconPath + '????????.bmp') then
  begin
    Bitmap := TBitmap.Create;
    try
      Bitmap.LoadFromFile(IconPath + '????????.bmp');
      ilMain.Add(Bitmap, nil);
    finally
      Bitmap.Free;
    end;
  end;

  // ????????
  if FileExists(IconPath + '?????????.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '?????????.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // ???????????????
  if FileExists(IconPath + '????????.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '????????.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // ???????????????
  if FileExists(IconPath + '?????????.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '?????????.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // ???????????????
  if FileExists(IconPath + '?????????.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '?????????.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // ???????????????
  if FileExists(IconPath + '?????????.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '?????????.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // ??????????????
  if FileExists(IconPath + '????????.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '????????.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // ???????????????????
  if FileExists(IconPath + '???????????? (2).ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '???????????? (2).ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // ????????????????
  if FileExists(IconPath + '?????????.ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '?????????.ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;

  // ???????????????
  if FileExists(IconPath + '???????? (2).ico') then
  begin
    Icon := TIcon.Create;
    try
      Icon.LoadFromFile(IconPath + '???????? (2).ico');
      ilMain.AddIcon(Icon);
    finally
      Icon.Free;
    end;
  end;
end;

procedure TfrmMain.SaveDatabaseList;
var
  IniFile: TIniFile;
  i: Integer;
begin
  if not Assigned(FDatabaseList) then
    Exit;

  IniFile := TIniFile.Create(FConfigFileName);
  try
    // ????????????б?
    IniFile.EraseSection('DatabaseList');

    // ?????????????
    IniFile.WriteInteger('DatabaseList', 'Count', FDatabaseList.Count);

    // ????????????????
    for i := 0 to FDatabaseList.Count - 1 do
    begin
      IniFile.WriteString('DatabaseList', Format('Path%d', [i]), FDatabaseList[i].Path);
      IniFile.WriteString('DatabaseList', Format('Name%d', [i]), FDatabaseList[i].Name);
      IniFile.WriteInteger('DatabaseList', Format('DBType%d', [i]), Ord(FDatabaseList[i].DBType));
    end;
  finally
    IniFile.Free;
  end;
end;

procedure TfrmMain.LoadDatabaseList;
var
  IniFile: TIniFile;
  Count, i, DBTypeValue: Integer;
  Path, Name: string;
  DBType: DBConnection.TDBType;
begin
  if not Assigned(FDatabaseList) then
    Exit;

  // ?????????б?
  FDatabaseList.Clear;

  IniFile := TIniFile.Create(FConfigFileName);
  try
    // ????????????
    Count := IniFile.ReadInteger('DatabaseList', 'Count', 0);

    // ???????????????
    for i := 0 to Count - 1 do
    begin
      Path := IniFile.ReadString('DatabaseList', Format('Path%d', [i]), '');
      Name := IniFile.ReadString('DatabaseList', Format('Name%d', [i]), '');
      DBTypeValue := IniFile.ReadInteger('DatabaseList', Format('DBType%d', [i]), 0);
      DBType := DBConnection.TDBType(DBTypeValue);

      // ???????????б?
      if (Path <> '') and (Name <> '') then
        AddDatabaseToList(Path, Name, DBType);
    end;
  finally
    IniFile.Free;
  end;
end;

procedure TfrmMain.AddDatabaseToList(const APath, AName: string; ADBType: DBConnection.TDBType);
var
  DBInfo: TDatabaseInfo;
  i: Integer;
begin
  // ??????????????·?????????
  for i := 0 to FDatabaseList.Count - 1 do
  begin
    if SameText(FDatabaseList[i].Path, APath) then
      Exit; // ???????????????
  end;

  // ???????????????????
  DBInfo := TDatabaseInfo.Create(APath, AName, ADBType);
  FDatabaseList.Add(DBInfo);
end;

procedure TfrmMain.ConnectToDatabase(const APath: string; ADBType: DBConnection.TDBType);
var
  DBManager: DBConnection.TDBConnectionManager;
  DBName: string;
  i: Integer;
  Found: Boolean;
  RootNode, DBNode: TTreeNode;
begin
  if not Assigned(FConnection) then
    Exit;

  if FConnection.Connected then
    FConnection.Close;

  case ADBType of
    dbtSQLite:
      begin
        FConnection.Params.Clear;
        FConnection.Params.DriverID := 'SQLite';
        FConnection.Params.Database := APath;
        // 设置字符串支持
        FConnection.FormatOptions.MapRules.Clear;
        FConnection.FormatOptions.OwnMapRules := True;
        FConnection.FormatOptions.StrsEmpty2Null := False;
        FConnection.FormatOptions.StrsTrim := False;
        // 使用gb2312编码正确显示中文字符
        FConnection.Params.Add('CharacterSet=gb2312');
      end;
    dbtAccess:
      begin
        FConnection.Params.Clear;
        FConnection.Params.DriverID := 'MSAcc';
        FConnection.Params.Database := APath;
        // 设置字符串支持
        FConnection.FormatOptions.MapRules.Clear;
        FConnection.FormatOptions.OwnMapRules := True;
        FConnection.FormatOptions.StrsEmpty2Null := False;
        FConnection.FormatOptions.StrsTrim := False;
      end;
    dbtParadox:
      begin
        FConnection.Params.Clear;
        FConnection.Params.DriverID := 'ODBC';
        FConnection.Params.Add('DefaultDir=' + APath);
        // 设置字符串支持
        FConnection.FormatOptions.MapRules.Clear;
        FConnection.FormatOptions.OwnMapRules := True;
        FConnection.FormatOptions.StrsEmpty2Null := False;
        FConnection.FormatOptions.StrsTrim := False;
      end;
  end;

  try
    FConnection.Open;

    // 连接成功，更新UI
    DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
    try
      // 显示连接方式信息
      sbMain.SimpleText := '已连接到数据库: ' + APath + ' | 连接方式: ' + DBManager.GetConnectionInfo;
    finally
      DBManager.Free;
    end;

    actConnect.Enabled := False;
    actDisconnect.Enabled := True;
    actRefreshDB.Enabled := True;
    actSearchDB.Enabled := True;
    actConvertDB.Enabled := True;

    // 加载数据库结构
    LoadDatabaseStructure;

    // 获取数据库名称
    DBName := ExtractFileName(APath);
    if DBName = '' then
      DBName := APath;

    // 添加到数据库列表（如果不存在）
    Found := False;
    for i := 0 to FDatabaseList.Count - 1 do
    begin
      if SameText(FDatabaseList[i].Path, APath) then
      begin
        Found := True;
        Break;
      end;
    end;

    if not Found then
    begin
      AddDatabaseToList(APath, DBName, ADBType);

      // 查找或创建根节点
      RootNode := nil;
      for i := 0 to tvDatabases.Items.Count - 1 do
      begin
        if (tvDatabases.Items[i].Level = 0) and (tvDatabases.Items[i].Text = '数据库列表') then
        begin
          RootNode := tvDatabases.Items[i];
          Break;
        end;
      end;

      if not Assigned(RootNode) then
      begin
        RootNode := tvDatabases.Items.Add(nil, '数据库列表');
        RootNode.ImageIndex := 0;
        RootNode.SelectedIndex := 0;
      end;

      // 添加数据库节点
      DBNode := tvDatabases.Items.AddChild(RootNode, DBName + ' (' + APath + ')');
      DBNode.ImageIndex := 0;
      DBNode.SelectedIndex := 0;
      DBNode.Data := Pointer(FDatabaseList[FDatabaseList.Count - 1]);

      // 展开根节点
      RootNode.Expand(False);

      // 保存数据库列表
      SaveDatabaseList;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('连接数据库失败: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.ExportTableData(const ATableName: string);
var
  SaveDialog: TSaveDialog;
  ExportQuery: TFDQuery;
  ExportFile: TextFile;
  i: Integer;
  FieldNames, FieldValues: string;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  SaveDialog := TSaveDialog.Create(nil);
  try
    SaveDialog.Title := '??????????';
    SaveDialog.DefaultExt := 'csv';
    SaveDialog.Filter := 'CSV??? (*.csv)|*.csv|??????? (*.*)|*.*';
    SaveDialog.FileName := ATableName + '.csv';

    if SaveDialog.Execute then
    begin
      ExportQuery := TFDQuery.Create(nil);
      try
        ExportQuery.Connection := FConnection;
        ExportQuery.SQL.Text := Format('SELECT * FROM "%s"', [ATableName]);
        ExportQuery.Open;

        AssignFile(ExportFile, SaveDialog.FileName);
        Rewrite(ExportFile);
        try
          // д???????
          FieldNames := '';
          for i := 0 to ExportQuery.FieldCount - 1 do
          begin
            if i > 0 then
              FieldNames := FieldNames + ',';
            FieldNames := FieldNames + ExportQuery.Fields[i].FieldName;
          end;
          WriteLn(ExportFile, FieldNames);

          // д??????
          while not ExportQuery.Eof do
          begin
            FieldValues := '';
            for i := 0 to ExportQuery.FieldCount - 1 do
            begin
              if i > 0 then
                FieldValues := FieldValues + ',';
              FieldValues := FieldValues + ExportQuery.Fields[i].AsString;
            end;
            WriteLn(ExportFile, FieldValues);
            ExportQuery.Next;
          end;

          ShowMessage('????????????????: ' + SaveDialog.FileName);
        finally
          CloseFile(ExportFile);
        end;
      finally
        ExportQuery.Free;
      end;
    end;
  finally
    SaveDialog.Free;
  end;
end;

procedure TfrmMain.ImportTableData(const ATableName: string);
var
  OpenDialog: TOpenDialog;
  ImportFile: TextFile;
  Line, FieldName: string;
  FieldNames, FieldValues: TStringList;
  ImportQuery: TFDQuery;
  SQL, InsertSQL: string;
  i: Integer;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  OpenDialog := TOpenDialog.Create(nil);
  try
    OpenDialog.Title := '?????????';
    OpenDialog.DefaultExt := 'csv';
    OpenDialog.Filter := 'CSV??? (*.csv)|*.csv|??????? (*.*)|*.*';

    if OpenDialog.Execute then
    begin
      FieldNames := TStringList.Create;
      FieldValues := TStringList.Create;
      ImportQuery := TFDQuery.Create(nil);
      try
        ImportQuery.Connection := FConnection;

        // ??CSV???
        AssignFile(ImportFile, OpenDialog.FileName);
        Reset(ImportFile);
        try
          // ????????
          if not Eof(ImportFile) then
          begin
            ReadLn(ImportFile, Line);
            FieldNames.CommaText := Line;
          end;

          // ???????????
          FConnection.StartTransaction;
          try
            while not Eof(ImportFile) do
            begin
              ReadLn(ImportFile, Line);
              FieldValues.CommaText := Line;

              // ????INSERT???
              InsertSQL := Format('INSERT INTO "%s" (', [ATableName]);
              for i := 0 to FieldNames.Count - 1 do
              begin
                if i > 0 then
                  InsertSQL := InsertSQL + ', ';
                FieldName := FieldNames[i];
                InsertSQL := InsertSQL + '"' + FieldName + '"';
              end;
              InsertSQL := InsertSQL + ') VALUES (';
              for i := 0 to FieldValues.Count - 1 do
              begin
                if i > 0 then
                  InsertSQL := InsertSQL + ', ';
                InsertSQL := InsertSQL + '''' + StringReplace(FieldValues[i], '''', '''''', [rfReplaceAll]) + '''';
              end;
              InsertSQL := InsertSQL + ')';

              // ???INSERT???
              ImportQuery.SQL.Text := InsertSQL;
              ImportQuery.ExecSQL;
            end;

            FConnection.Commit;
            ShowMessage('??????????????');

            // ??±????????
            LoadTableData(ATableName);
          except
            on E: Exception do
            begin
              FConnection.Rollback;
              ShowMessage('???????????: ' + E.Message);
            end;
          end;
        finally
          CloseFile(ImportFile);
        end;
      finally
        FieldNames.Free;
        FieldValues.Free;
        ImportQuery.Free;
      end;
    end;
  finally
    OpenDialog.Free;
  end;
end;

procedure TfrmMain.ModifyTableData(const ATableName: string);
begin
  // ???????????????????????????????????????
  ShowMessage('??????????????δ???');
end;

procedure TfrmMain.RefreshDatabaseNode(Node: TTreeNode);
var
  DBManager: DBConnection.TDBConnectionManager;
  RootNode: TTreeNode;
  DBPath: string;
begin
  if not Assigned(Node) then
    Exit;

  if not FConnection.Connected then
    Exit;

  // ????????·??
  DBPath := FConnection.Params.Database;
  if DBPath = '' then
    DBPath := FConnection.Params.Values['DefaultDir'];

  // ?????????????
  Node.DeleteChildren;

  // ????????????
  DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
  try
    // ??????????????
    LoadTablesIntoDirectories(DBManager, Node, DBPath);
  finally
    DBManager.Free;
  end;
end;

function TfrmMain.FindMirServerDir(const StartDir: string): string;
var
  CurrentDir, ParentDir: string;
  IsMirServerDir: Boolean;
begin
  Result := '';

  // ??鵱?????????MirServer
  if ExtractFileName(ExcludeTrailingPathDelimiter(StartDir)) = 'MirServer' then
  begin
    Result := IncludeTrailingPathDelimiter(StartDir);
    Exit;
  end;

  // ??鵱??????????MirServer????
  if DirectoryExists(IncludeTrailingPathDelimiter(StartDir) + 'MirServer') then
  begin
    Result := IncludeTrailingPathDelimiter(StartDir) + 'MirServer\';
    Exit;
  end;

  // ??鵱????????MirServer??????
  // ????????????Config.ini??Mud2\DB?????ж?
  IsMirServerDir := FileExists(IncludeTrailingPathDelimiter(StartDir) + 'Config.ini') and
                   DirectoryExists(IncludeTrailingPathDelimiter(StartDir) + 'Mud2') and
                   DirectoryExists(IncludeTrailingPathDelimiter(StartDir) + 'Mud2\DB');

  if IsMirServerDir then
  begin
    Result := IncludeTrailingPathDelimiter(StartDir);
    Exit;
  end;

  // ??鸸??
  ParentDir := ExtractFilePath(ExcludeTrailingPathDelimiter(StartDir));
  if (ParentDir <> '') and (ParentDir <> StartDir) then
  begin
    Result := FindMirServerDir(ParentDir);
    if Result <> '' then
      Exit;
  end;

  // ?????????????????????????????????MirServer????
  CurrentDir := StartDir;
  while (CurrentDir <> '') do
  begin
    // ??鵱????????
    ParentDir := ExtractFilePath(ExcludeTrailingPathDelimiter(CurrentDir));

    // ???????????????????????
    if (ParentDir = CurrentDir) then
      Break;

    // ??鸸?????????MirServer??
    if DirectoryExists(IncludeTrailingPathDelimiter(ParentDir) + 'MirServer') then
    begin
      Result := IncludeTrailingPathDelimiter(ParentDir) + 'MirServer\';
      Exit;
    end;

    CurrentDir := ParentDir;
  end;
end;

function TfrmMain.AutoDetectDatabase(const AGameDir: string): Boolean;
var
  MirServerPath, ConfigFile, DBType, DBFile, AccessFileName, ParadoxDir, ParentDir: string;
  UseAccessDB: Integer;
begin
  Result := False;

  // ???????MirServer????????????
  MirServerPath := FindMirServerDir(AGameDir);

  // ?????????MirServer???????????
  if MirServerPath = '' then
  begin
    // ??鵱?????????Mud2\DB??????????????????MirServer??
    if DirectoryExists(IncludeTrailingPathDelimiter(AGameDir) + 'Mud2') and
       DirectoryExists(IncludeTrailingPathDelimiter(AGameDir) + 'Mud2\DB') then
    begin
      MirServerPath := IncludeTrailingPathDelimiter(AGameDir);
    end
    else
    begin
      // ??鵱??????????????Mud2\DB??
      ParentDir := ExtractFilePath(ExcludeTrailingPathDelimiter(AGameDir));
      if DirectoryExists(IncludeTrailingPathDelimiter(ParentDir) + 'Mud2') and
         DirectoryExists(IncludeTrailingPathDelimiter(ParentDir) + 'Mud2\DB') then
      begin
        MirServerPath := IncludeTrailingPathDelimiter(ParentDir);
      end
      else
      begin
        // ??????????????????
        MirServerPath := IncludeTrailingPathDelimiter(AGameDir);
      end;
    end;
  end;

  // ???·?????б???β
  MirServerPath := IncludeTrailingPathDelimiter(MirServerPath);

  // ???Config.ini???
  ConfigFile := MirServerPath + 'Config.ini';
  if not FileExists(ConfigFile) then
  begin
    ShowMessage('δ???Config.ini???????????????????????');
    Exit(False);
  end;

  // ???Config.ini?е??????????
  DBType := ReadIniValue(ConfigFile, 'DB', 'DBType', '');

  // ??????????????SQLite?????
  if SameText(DBType, 'SQLite') then
  begin
    // ???SQLite????????·??
    DBFile := ReadIniValue(ConfigFile, 'DB', 'DBFile', '');

    // ?????????DBFile????????Mud2\DB????????????SQLite????????
    if DBFile = '' then
    begin
      var DBPath := MirServerPath + 'Mud2\DB\';
      if DirectoryExists(DBPath) then
      begin
        if FileExists(DBPath + 'mir2.db') then
          DBFile := DBPath + 'mir2.db'
        else if FileExists(DBPath + 'game.db') then
          DBFile := DBPath + 'game.db'
        else if FileExists(DBPath + 'HeroDB.db') then
          DBFile := DBPath + 'HeroDB.db'
        else if FileExists(DBPath + 'ApexM2.DB') then
          DBFile := DBPath + 'ApexM2.DB';
      end;
    endChecking project dependencies...
Compiling NewDBTool.dproj (Debug, Win32)
dcc32 command line for "NewDBTool.dpr"
  c:\program files (x86)\embarcadero\studio\23.0\bin\dcc32.exe -$O- -$W+ -$R+ -$Q+ --no-config -M -Q -TX.exe 
  -AGenerics.Collections=System.Generics.Collections;Generics.Defaults=System.Generics.Defaults;WinTypes=Winapi.Windows;WinProcs=Winapi.Windows;
  DbiTypes=BDE;DbiProcs=BDE;DbiErrs=BDE -DDEBUG;;FRAMEWORK_VCL -I"c:\program files (x86)\embarcadero\studio\23.0\lib\Win32\release";
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Imports;C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Imports\Win32;"c:\program files 
  (x86)\embarcadero\studio\23.0\Imports";C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Dcp;"c:\program files (x86)\embarcadero\studio\23.0\include" 
  -LEC:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Bpl -LNC:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Dcp -NSWinapi;System.Win;Data.Win;
  Datasnap.Win;Web.Win;Soap.Win;Xml.Win;Bde;Vcl;Vcl.Imaging;Vcl.Touch;Vcl.Samples;Vcl.Shell;System;Xml;Data;Datasnap;Web;Soap;WinAPI.ApplicationModel; 
  -O"c:\program files (x86)\embarcadero\studio\23.0\lib\Win32\release";C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Imports;
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Imports\Win32;"c:\program files (x86)\embarcadero\studio\23.0\Imports";
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Dcp;"c:\program files (x86)\embarcadero\studio\23.0\include" -R"c:\program files 
  (x86)\embarcadero\studio\23.0\lib\Win32\release";C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Imports;
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Imports\Win32;"c:\program files (x86)\embarcadero\studio\23.0\Imports";
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Dcp;"c:\program files (x86)\embarcadero\studio\23.0\include" -U"c:\program files 
  (x86)\embarcadero\studio\23.0\lib\Win32\release";C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Imports;
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Imports\Win32;"c:\program files (x86)\embarcadero\studio\23.0\Imports";
  C:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Dcp;"c:\program files (x86)\embarcadero\studio\23.0\include" -K00400000 
  -NBC:\Users\<USER>\Documents\Embarcadero\Studio\23.0\Dcp -NHC:\Users\<USER>\Documents\Embarcadero\Studio\23.0\hpp\Win32  NewDBTool.dpr   
[dcc32 Error] MainForm.pas(103): E2254 Overloaded procedure 'SaveDatabaseList' must be marked with the 'overload' directive
[dcc32 Error] MainForm.pas(104): E2252 Method 'SaveDatabaseList' with identical parameters already exists
[dcc32 Error] MainForm.pas(105): E2252 Method 'LoadDatabaseList' with identical parameters already exists
[dcc32 Error] MainForm.pas(1733): E2004 Identifier redeclared: 'TfrmMain.SaveDatabaseList'
[dcc32 Error] MainForm.pas(1734): E2004 Identifier redeclared: 'IniFile'
[dcc32 Error] MainForm.pas(1735): E2004 Identifier redeclared: 'i'
[dcc32 Error] MainForm.pas(1761): E2004 Identifier redeclared: 'TfrmMain.LoadDatabaseList'
[dcc32 Error] MainForm.pas(1762): E2004 Identifier redeclared: 'IniFile'
[dcc32 Error] MainForm.pas(1763): E2004 Identifier redeclared: 'Count'
[dcc32 Error] MainForm.pas(1764): E2004 Identifier redeclared: 'Path'
[dcc32 Error] MainForm.pas(1765): E2004 Identifier redeclared: 'DBType'
[dcc32 Error] MainForm.pas(103): E2065 Unsatisfied forward or external declaration: 'TfrmMain.SaveDatabaseList'
[dcc32 Error] MainForm.pas(104): E2065 Unsatisfied forward or external declaration: 'TfrmMain.LoadDatabaseList'
[dcc32 Hint] MainForm.pas(103): H2219 Private symbol 'SaveDatabaseList' declared but never used
[dcc32 Hint] MainForm.pas(104): H2219 Private symbol 'LoadDatabaseList' declared but never used
[dcc32 Hint] MainForm.pas(108): H2219 Private symbol 'AutoDetectDatabase' declared but never used
[dcc32 Hint] MainForm.pas(109): H2219 Private symbol 'RefreshDatabaseNode' declared but never used
[dcc32 Hint] MainForm.pas(114): H2219 Private symbol 'ConvertDatabase' declared but never used
[dcc32 Fatal Error] NewDBTool.dpr(5): F2063 Could not compile used unit 'MainForm.pas'
Failed
Elapsed time: 00:00:01.0
    else
    begin
      // ???DBFile?????·????????????·??
      if not FileExists(DBFile) and (Length(DBFile) > 0) then
      begin
        // ??????????????·??
        if (DBFile[1] = PathDelim) or ((Length(DBFile) > 2) and (DBFile[2] = ':')) then
        begin
          // ????????·???????????????
          ShowMessage('Config.ini???????SQLite??????????????: ' + DBFile);
          Exit(False);
        end
        else
        begin
          // ?????????MirServer????
          if FileExists(MirServerPath + DBFile) then
            DBFile := MirServerPath + DBFile
          // ?????????Mud2\DB??
          else if FileExists(MirServerPath + 'Mud2\DB\' + DBFile) then
            DBFile := MirServerPath + 'Mud2\DB\' + DBFile
          else
          begin
            ShowMessage('δ???SQLite????????: ' + DBFile);
            Exit(False);
          end;
        end;
      end;
    end;

    if (DBFile <> '') and FileExists(DBFile) then
    begin
      // ????SQLite?????
      ConnectToDatabase(DBFile, dbtSQLite);
      Result := True;
      Exit; // ???????????????
    end
    else
    begin
      ShowMessage('δ?????Ч??SQLite????????');
      Exit(False);
    end;
  end
  // ???????????Access?????
  else if DBType = '' then
  begin
    // ?????????Access?????
    UseAccessDB := StrToIntDef(ReadIniValue(ConfigFile, 'DB', 'UseAccessDB', '0'), 0);

    if UseAccessDB = 1 then
    begin
      // ???Access????????·??
      AccessFileName := ReadIniValue(ConfigFile, 'DB', 'AccessFileName', '');

      // ?????????AccessFileName????????Mud2\DB????????????Access????????
      if AccessFileName = '' then
      begin
        var DBPath := MirServerPath + 'Mud2\DB\';
        if DirectoryExists(DBPath) then
        begin
          if FileExists(DBPath + 'Mir2.mdb') then
            AccessFileName := DBPath + 'Mir2.mdb'
          else if FileExists(DBPath + 'GameData.mdb') then
            AccessFileName := DBPath + 'GameData.mdb'
          else if FileExists(DBPath + 'HeroDB.mdb') then
            AccessFileName := DBPath + 'HeroDB.mdb';
        end;
      end
      else
      begin
        // ???AccessFileName?????·????????????·??
        if not FileExists(AccessFileName) and (Length(AccessFileName) > 0) then
        begin
          // ??????????????·??
          if (AccessFileName[1] = PathDelim) or ((Length(AccessFileName) > 2) and (AccessFileName[2] = ':')) then
          begin
            // ????????·???????????????
            ShowMessage('Config.ini???????Access??????????????: ' + AccessFileName);
            Exit(False);
          end
          else
          begin
            // ?????????MirServer????
            if FileExists(MirServerPath + AccessFileName) then
              AccessFileName := MirServerPath + AccessFileName
            // ?????????Mud2\DB??
            else if FileExists(MirServerPath + 'Mud2\DB\' + AccessFileName) then
              AccessFileName := MirServerPath + 'Mud2\DB\' + AccessFileName
            else
            begin
              ShowMessage('δ???Access????????: ' + AccessFileName);
              Exit(False);
            end;
          end;
        end;
      end;

      if (AccessFileName <> '') and FileExists(AccessFileName) then
      begin
        // ????Access?????
        ConnectToDatabase(AccessFileName, dbtAccess);
        Result := True;
        Exit; // ???????????????
      end
      else
      begin
        ShowMessage('δ?????Ч??Access????????');
        Exit(False);
      end;
    end
    // ???UseAccessDB=0??????????Paradox?????
    else if UseAccessDB = 0 then
    begin
      // ???Mud2\DB?????Paradox???????
      ParadoxDir := MirServerPath + 'Mud2\DB\';

      if DirectoryExists(ParadoxDir) then
      begin
        // ????Paradox?????
        ConnectToDatabase(ParadoxDir, dbtParadox);
        Result := True;
        Exit; // ???????????????
      end
      else
      begin
        ShowMessage('δ?????Ч??Paradox???????: ' + ParadoxDir);
        Exit(False);
      end;
    end
  end;

  // ???????????????????????????δ?????Ч???????????
  ShowMessage('δ?????Ч????????????????????????????????Config.ini?????????????????????????');
  Exit(False);
end;

procedure TfrmMain.actExportDataExecute(Sender: TObject);
begin
  if FCurrentTable <> '' then
    ExportTableData(FCurrentTable)
  else
    ShowMessage('????????????');
end;

procedure TfrmMain.actImportDataExecute(Sender: TObject);
begin
  if FCurrentTable <> '' then
    ImportTableData(FCurrentTable)
  else
    ShowMessage('????????????');
end;

procedure TfrmMain.actRefreshDataExecute(Sender: TObject);
begin
  if FCurrentTable <> '' then
    LoadTableData(FCurrentTable)
  else
    ShowMessage('????????????');
end;

procedure TfrmMain.actModifyDataExecute(Sender: TObject);
begin
  if FCurrentTable <> '' then
    ModifyTableData(FCurrentTable)
  else
    ShowMessage('????????????');
end;

procedure TfrmMain.actRefreshDBExecute(Sender: TObject);
begin
  if FConnection.Connected then
    LoadDatabaseStructure
  else
    ShowMessage('??????????????');
end;

procedure TfrmMain.actSearchDBExecute(Sender: TObject);
var
  SearchText: string;
begin
  if not FConnection.Connected then
  begin
    ShowMessage('??????????????');
    Exit;
  end;

  if InputQuery('?????????', '??????????????:', SearchText) then
  begin
    if SearchText <> '' then
      SearchDatabase(SearchText)
    else
      ShowMessage('??????????????');
  end;
end;

procedure TfrmMain.actConvertDBExecute(Sender: TObject);
var
  SourcePath, TargetPath: string;
  SourceType, TargetType: DBConnection.TDBType;
begin
  ShowMessage('??????????????δ???');
end;

procedure TfrmMain.tvDatabasesMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
var
  Node: TTreeNode;
  HitTest: THitTests;
begin
  if Button = mbRight then
  begin
    Node := tvDatabases.GetNodeAt(X, Y);
    if Assigned(Node) then
    begin
      tvDatabases.Selected := Node;

      // ????????????????????
      if Node.Level = 0 then
      begin
        // ?????????????
        miRefreshDB.Visible := True;
        miSearchDB.Visible := True;
        miConvertDB.Visible := True;
        miExportData.Visible := False;
        miImportData.Visible := False;
        miRefreshData.Visible := False;
        miModifyData.Visible := False;
      end
      else if Node.Level = 1 then
      begin
        // ???????
        miRefreshDB.Visible := False;
        miSearchDB.Visible := False;
        miConvertDB.Visible := False;
        miExportData.Visible := True;
        miImportData.Visible := True;
        miRefreshData.Visible := True;
        miModifyData.Visible := True;

        // ??????????
        FCurrentTable := Node.Text;
      end;

      // ??????????
      pmTreeView.Popup(Mouse.CursorPos.X, Mouse.CursorPos.Y);
    end;
  end;
end;

procedure TfrmMain.dbgDataMouseDown(Sender: TObject; Button: TMouseButton; Shift: TShiftState; X, Y: Integer);
begin
  if Button = mbRight then
  begin
    // ??????????????????
    pmDBGrid.Popup(Mouse.CursorPos.X, Mouse.CursorPos.Y);
  end;
end;

procedure TfrmMain.SearchDatabase(const SearchText: string);
var
  SearchQuery: TFDQuery;
  Tables: TStringList;
  i: Integer;
  TableName: string;
  ResultsForm: TForm;
  ResultsMemo: TMemo;
  SQL: string;
begin
  if not FConnection.Connected then
    Exit;

  // ???????????
  ResultsForm := TForm.Create(Self);
  try
    ResultsForm.Caption := '???????: ' + SearchText;
    ResultsForm.Width := 600;
    ResultsForm.Height := 400;
    ResultsForm.Position := poScreenCenter;

    ResultsMemo := TMemo.Create(ResultsForm);
    ResultsMemo.Parent := ResultsForm;
    ResultsMemo.Align := alClient;
    ResultsMemo.ScrollBars := ssBoth;
    ResultsMemo.ReadOnly := True;

    // ??????б?
    Tables := TStringList.Create;
    try
      // ???????????
      SearchQuery := TFDQuery.Create(nil);
      try
        SearchQuery.Connection := FConnection;

        // ??????б?
        var DBManager := DBConnection.TDBConnectionManager.Create(FConnection, nil);
        try
          Tables := DBManager.GetTableNames;
        finally
          DBManager.Free;
        end;

        // ?????????????
        for i := 0 to Tables.Count - 1 do
        begin
          TableName := Tables[i];
          ResultsMemo.Lines.Add('??????????: ' + TableName);
          Application.ProcessMessages;

          try
            // ???????SQL
            SQL := 'SELECT * FROM "' + TableName + '"';
            SearchQuery.SQL.Text := SQL;
            SearchQuery.Open;

            // ????????
            while not SearchQuery.Eof do
            begin
              var FoundInRow := False;
              var RowText := '';

              // ?????????
              for var j := 0 to SearchQuery.FieldCount - 1 do
              begin
                var FieldValue := SearchQuery.Fields[j].AsString;
                if Pos(LowerCase(SearchText), LowerCase(FieldValue)) > 0 then
                begin
                  FoundInRow := True;
                  RowText := RowText + SearchQuery.Fields[j].FieldName + '=' + FieldValue + ', ';
                end;
              end;

              if FoundInRow then
              begin
                ResultsMemo.Lines.Add('  ??????: ' + RowText);
              end;

              SearchQuery.Next;
            end;

            SearchQuery.Close;
          except
            on E: Exception do
            begin
              ResultsMemo.Lines.Add('  ???????????: ' + E.Message);
            end;
          end;
        end;

        ResultsMemo.Lines.Add('???????');
      finally
        SearchQuery.Free;
      end;
    finally
      Tables.Free;
    end;

    // ??????????
    ResultsForm.ShowModal;
  finally
    ResultsForm.Free;
  end;
end;

procedure TfrmMain.ConvertDatabase(const SourcePath, TargetPath: string; SourceType, TargetType: DBConnection.TDBType);
begin
  ShowMessage('??????????????δ???');
end;

function TfrmMain.ReadIniValue(const FileName, Section, Key, DefaultValue: string): string;
var
  IniFile: TIniFile;
begin
  Result := DefaultValue;

  if not FileExists(FileName) then
    Exit;

  IniFile := TIniFile.Create(FileName);
  try
    Result := IniFile.ReadString(Section, Key, DefaultValue);
  finally
    IniFile.Free;
  end;
end;

procedure TfrmMain.SaveDatabaseList;
var
  IniFile: TIniFile;
  i: Integer;
begin
  if not Assigned(FDatabaseList) then
    Exit;

  IniFile := TIniFile.Create(FConfigFileName);
  try
    // 清除旧的数据库列表
    IniFile.EraseSection('DatabaseList');

    // 保存数据库数量
    IniFile.WriteInteger('DatabaseList', 'Count', FDatabaseList.Count);

    // 保存每个数据库的信息
    for i := 0 to FDatabaseList.Count - 1 do
    begin
      IniFile.WriteString('DatabaseList', Format('Path%d', [i]), FDatabaseList[i].Path);
      IniFile.WriteString('DatabaseList', Format('Name%d', [i]), FDatabaseList[i].Name);
      IniFile.WriteInteger('DatabaseList', Format('DBType%d', [i]), Ord(FDatabaseList[i].DBType));
    end;
  finally
    IniFile.Free;
  end;
end;

procedure TfrmMain.LoadDatabaseList;
var
  IniFile: TIniFile;
  Count, i, DBTypeValue: Integer;
  Path, Name: string;
  DBType: DBConnection.TDBType;
  RootNode, DBNode: TTreeNode;
begin
  if not Assigned(FDatabaseList) then
    Exit;

  // 清空数据库列表
  FDatabaseList.Clear;

  // 清空树视图
  tvDatabases.Items.Clear;

  // 创建根节点
  RootNode := tvDatabases.Items.Add(nil, '数据库列表');
  RootNode.ImageIndex := 0;
  RootNode.SelectedIndex := 0;

  IniFile := TIniFile.Create(FConfigFileName);
  try
    // 读取数据库数量
    Count := IniFile.ReadInteger('DatabaseList', 'Count', 0);

    // 读取每个数据库的信息
    for i := 0 to Count - 1 do
    begin
      Path := IniFile.ReadString('DatabaseList', Format('Path%d', [i]), '');
      Name := IniFile.ReadString('DatabaseList', Format('Name%d', [i]), '');
      DBTypeValue := IniFile.ReadInteger('DatabaseList', Format('DBType%d', [i]), 0);
      DBType := DBConnection.TDBType(DBTypeValue);

      // 添加到数据库列表
      if (Path <> '') and (Name <> '') then
      begin
        AddDatabaseToList(Path, Name, DBType);

        // 添加到树视图
        DBNode := tvDatabases.Items.AddChild(RootNode, Name + ' (' + Path + ')');
        DBNode.ImageIndex := 0;
        DBNode.SelectedIndex := 0;
        // 存储数据库信息对象
        DBNode.Data := Pointer(FDatabaseList[FDatabaseList.Count - 1]);
      end;
    end;

    // 展开根节点
    if RootNode.Count > 0 then
      RootNode.Expand(False);
  finally
    IniFile.Free;
  end;
end;

end.