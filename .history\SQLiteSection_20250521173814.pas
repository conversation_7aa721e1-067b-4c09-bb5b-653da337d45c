  // 根据配置判断数据库类型
  if DBType = 'SQLite' then
  begin
    // VV引擎，使用SQLite数据库
    DBFile := ReadIniValue(ConfigIniPath, 'Database', 'DBFile', '');

    if DBFile = '' then
    begin
      // 尝试在默认位置查找SQLite数据库
      DBPath := MirServerPath + 'Mud2\DB\';

      if DirectoryExists(DBPath) then
      begin
        if FileExists(DBPath + 'mir2.db') then
          DBFile := DBPath + 'mir2.db'
        else if FileExists(DBPath + 'game.db') then
          DBFile := DBPath + 'game.db';
      end;
    end
    else
    begin
      // 如果DBFile是相对路径，转换为绝对路径
      if not FileExists(DBFile) and (Length(DBFile) > 0) then
      begin
        // 首先检查是否是绝对路径
        if (DBFile[1] = PathDelim) or ((Length(DBFile) > 2) and (DBFile[2] = ':')) then
        begin
          // 已经是绝对路径，但文件不存在
          ShowMessage('Config.ini中指定的SQLite数据库文件不存在: ' + DBFile);
          Exit(False);
        end
        else
        begin
          // 尝试相对于MirServer根目录
          if FileExists(MirServerPath + DBFile) then
            DBFile := MirServerPath + DBFile
          // 尝试相对于Mud2\DB目录
          else if FileExists(MirServerPath + 'Mud2\DB\' + DBFile) then
            DBFile := MirServerPath + 'Mud2\DB\' + DBFile
          // 尝试相对于当前目录
          else if FileExists(ExtractFilePath(ParamStr(0)) + DBFile) then
            DBFile := ExtractFilePath(ParamStr(0)) + DBFile
          else
          begin
            ShowMessage('无法找到Config.ini中指定的SQLite数据库文件: ' + DBFile);
            Exit(False);
          end;
        end;
      end;
    end;

    if (DBFile <> '') and FileExists(DBFile) then
    begin
      // 连接SQLite数据库
      if ConnectToSQLite(DBFile) then
      begin
        FAutoDetected := True;
        Result := True;
      end;
    end
    else
    begin
      ShowMessage('未找到有效的SQLite数据库文件');
    end;
  end
