unit ConnectionDialog;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls,
  Vcl.ComCtrls, Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error,
  FireDAC.UI.Intf, FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool,
  FireDAC.Stan.Async, FireDAC.Phys, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef,
  FireDAC.Stan.ExprFuncs, FireDAC.VCLUI.Wait, FireDAC.Comp.Client, FireDAC.Phys.ODBC,
  FireDAC.Phys.ODBCDef, DBConnection, FireDAC.Phys.MSAcc, FireDAC.Phys.MSAccDef,
  Data.Win.ADODB, System.Win.Registry, System.IniFiles;

type
  TfrmConnectionDialog = class(TForm)
    pnlMain: TPanel;
    pnlButtons: TPanel;
    btnConnect: TButton;
    btnCancel: TButton;
    lblGameDir: TLabel;
    edtGameDir: TEdit;
    btnBrowseDir: TButton;
    procedure FormCreate(Sender: TObject);
    procedure btnConnectClick(Sender: TObject);
    procedure btnCancelClick(Sender: TObject);
    procedure btnBrowseDirClick(Sender: TObject);
  private
    { Private declarations }
    FConnection: TFDConnection;
    FDBType: DBConnection.TDBType;
    FAutoDetected: Boolean;
    FGameDir: string;
    function ConnectToSQLite(const AFileName: string): Boolean;
    function ConnectToAccess(const AFileName: string): Boolean;
    function ConnectToParadox(const ADirectory: string): Boolean;
    function ReadIniValue(const FileName, Section, Key, DefaultValue: string): string;
    function AutoDetectDatabase(const AGameDir: string): Boolean;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; AConnection: TFDConnection); reintroduce;
    function Execute: Boolean;
  end;

var
  frmConnectionDialog: TfrmConnectionDialog;

implementation

{$R *.dfm}

constructor TfrmConnectionDialog.Create(AOwner: TComponent; AConnection: TFDConnection);
begin
  inherited Create(AOwner);
  FConnection := AConnection;
  FAutoDetected := False;
  FGameDir := '';
end;

procedure TfrmConnectionDialog.FormCreate(Sender: TObject);
begin
  FDBType := DBConnection.dbtSQLite;

  // 初始化游戏目录为空
  FGameDir := '';
  edtGameDir.Text := '';
end;

procedure TfrmConnectionDialog.btnBrowseDirClick(Sender: TObject);
var
  OpenDialog: TFileOpenDialog;
begin
  OpenDialog := TFileOpenDialog.Create(Self);
  try
    // 设置为选择文件夹模式
    OpenDialog.Options := OpenDialog.Options + [fdoPickFolders, fdoPathMustExist];
    OpenDialog.Title := '选择游戏目录';
    // 不设置默认目录，让用户自由选择
    OpenDialog.DefaultFolder := '';

    // 添加文件类型过滤器
    OpenDialog.FileTypes.Clear;
    OpenDialog.FileTypes.Add.DisplayName := '所有文件 (*.*)';
    OpenDialog.FileTypes.Add.FileMask := '*.*';
    OpenDialog.FileTypes.Add.DisplayName := '数据库文件 (*.db;*.mdb;*.accdb;*.sqlite)';
    OpenDialog.FileTypes.Add.FileMask := '*.db;*.mdb;*.accdb;*.sqlite';

    if OpenDialog.Execute then
    begin
      FGameDir := OpenDialog.FileName;
      edtGameDir.Text := FGameDir;

      // 尝试自动检测并连接数据库
      if AutoDetectDatabase(FGameDir) then
      begin
        ShowMessage('数据库连接成功！');
        ModalResult := mrOk;
      end
      else
      begin
        ShowMessage('未找到MirServer目录或无法自动检测数据库类型。请确保选择的是MirServer根目录或其子目录，或手动选择数据库文件。');
      end;
    end;
  finally
    OpenDialog.Free;
  end;
end;

function TfrmConnectionDialog.ReadIniValue(const FileName, Section, Key, DefaultValue: string): string;
var
  IniFile: TIniFile;
begin
  Result := DefaultValue;

  if not FileExists(FileName) then
    Exit;

  IniFile := TIniFile.Create(FileName);
  try
    Result := IniFile.ReadString(Section, Key, DefaultValue);
  finally
    IniFile.Free;
  end;
end;

function TfrmConnectionDialog.AutoDetectDatabase(const AGameDir: string): Boolean;
var
  GamePath, MirServerPath, ConfigIniPath, DBPath, DBFile: string;
  UseAccessDB, UseSqliteDB: Integer;
  DBType, AccessFileName: string;
  ParentDir: string;
  IsFile: Boolean;

  // 查找MirServer目录的函数
  function FindMirServerDir(const StartDir: string): string;
  var
    DirName: string;
    CurrentPath: string;
    IsMirServerDir: Boolean;
    SearchRec: TSearchRec;
  begin
    Result := '';

    // 检查当前目录是否包含MirServer（支持前后缀和数字）
    DirName := ExtractFileName(ExcludeTrailingPathDelimiter(StartDir));
    if (Pos('MirServer', UpperCase(DirName)) > 0) or
       (Pos('MIRSERVER', UpperCase(DirName)) > 0) then
    begin
      // 进一步检查是否真的是MirServer目录（包含Config.ini和Mud2\DB）
      if FileExists(IncludeTrailingPathDelimiter(StartDir) + 'Config.ini') and
         DirectoryExists(IncludeTrailingPathDelimiter(StartDir) + 'Mud2') and
         DirectoryExists(IncludeTrailingPathDelimiter(StartDir) + 'Mud2\DB') then
      begin
        Result := IncludeTrailingPathDelimiter(StartDir);
        Exit;
      end;
    end;

    // 检查当前目录下是否有包含MirServer的子目录
    if FindFirst(IncludeTrailingPathDelimiter(StartDir) + '*', faDirectory, SearchRec) = 0 then
    begin
      try
        repeat
          if (SearchRec.Attr and faDirectory <> 0) and
             (SearchRec.Name <> '.') and (SearchRec.Name <> '..') then
          begin
            // 检查目录名是否包含MirServer（不区分大小写，支持前后缀和数字）
            if (Pos('MirServer', UpperCase(SearchRec.Name)) > 0) or
               (Pos('MIRSERVER', UpperCase(SearchRec.Name)) > 0) then
            begin
              var SubDir := IncludeTrailingPathDelimiter(StartDir) + SearchRec.Name;
              // 检查是否真的是MirServer目录
              if FileExists(SubDir + '\Config.ini') and
                 DirectoryExists(SubDir + '\Mud2') and
                 DirectoryExists(SubDir + '\Mud2\DB') then
              begin
                Result := IncludeTrailingPathDelimiter(SubDir);
                Exit;
              end;
            end;
          end;
        until FindNext(SearchRec) <> 0;
      finally
        FindClose(SearchRec);
      end;
    end;

    // 向上级目录递归查找
    CurrentPath := ExcludeTrailingPathDelimiter(StartDir);
    ParentDir := ExtractFileDir(CurrentPath);

    // 如果已到达根目录，则退出
    if (ParentDir = CurrentPath) or (ParentDir = '') then
      Exit;

    // 递归查找上级目录
    Result := FindMirServerDir(ParentDir);
  end;

begin
  Result := False;

  if AGameDir = '' then
    Exit(False);

  // 判断输入的是文件还是目录
  IsFile := FileExists(AGameDir);

  // 获取游戏目录路径
  if IsFile then
    GamePath := IncludeTrailingPathDelimiter(ExtractFilePath(AGameDir))
  else
    GamePath := IncludeTrailingPathDelimiter(AGameDir);

  // 查找MirServer目录的位置
  MirServerPath := FindMirServerDir(GamePath);

  // 如果没有找到MirServer目录，进行备用查找
  if MirServerPath = '' then
  begin
    // 检查当前目录是否包含Mud2\DB目录，如果有则认为是MirServer目录
    if DirectoryExists(GamePath + 'Mud2') and DirectoryExists(GamePath + 'Mud2\DB') then
    begin
      MirServerPath := GamePath;
    end
    else
    begin
      // 检查父目录是否包含Mud2\DB目录
      ParentDir := ExtractFilePath(ExcludeTrailingPathDelimiter(GamePath));
      if DirectoryExists(ParentDir + 'Mud2') and DirectoryExists(ParentDir + 'Mud2\DB') then
      begin
        MirServerPath := ParentDir;
      end
      else
      begin
        // 都找不到就使用当前目录
        MirServerPath := GamePath;
      end;
    end;
  end;

  // 查找Config.ini文件
  ConfigIniPath := MirServerPath + 'Config.ini';

  if not FileExists(ConfigIniPath) then
  begin
    // 尝试在上级目录查找
    ConfigIniPath := IncludeTrailingPathDelimiter(ExtractFilePath(ExcludeTrailingPathDelimiter(MirServerPath))) + 'Config.ini';

    if not FileExists(ConfigIniPath) then
      Exit(False);
  end;

  // 从Config.ini中读取数据库类型
  // 1. 首先检查VV引擎特有的DBType配置
  DBType := ReadIniValue(ConfigIniPath, 'Database', 'DBType', '');

  // 2. 检查GameConf段的配置
  if DBType = '' then
  begin
    DBType := ReadIniValue(ConfigIniPath, 'GameConf', 'DBType', '');
  end;

  // 3. 检查LF/GEE/V8引擎的UseSqliteDB配置
  UseSqliteDB := StrToIntDef(ReadIniValue(ConfigIniPath, 'GameConf', 'UseSqliteDB', '0'), 0);

  // 4. 检查Access数据库配置
  UseAccessDB := StrToIntDef(ReadIniValue(ConfigIniPath, 'GameConf', 'UseAccessDB', '0'), 0);
  if UseAccessDB = 0 then
  begin
    UseAccessDB := StrToIntDef(ReadIniValue(ConfigIniPath, 'Database', 'UseAccessDB', '0'), 0);
  end;

  // 如果配置文件中指定了SQLite数据库
  if SameText(DBType, 'SQLite') then
  begin
    // 读取SQLite数据库文件路径
    DBFile := ReadIniValue(ConfigIniPath, 'DB', 'DBFile', '');

    // 如果没有指定DBFile，尝试在Mud2\DB目录下查找常见的SQLite数据库文件
    if DBFile = '' then
    begin
      DBPath := MirServerPath + 'Mud2\DB\';
      if DirectoryExists(DBPath) then
      begin
        if FileExists(DBPath + 'mir2.db') then
          DBFile := DBPath + 'mir2.db'
        else if FileExists(DBPath + 'game.db') then
          DBFile := DBPath + 'game.db'
        else if FileExists(DBPath + 'HeroDB.db') then
          DBFile := DBPath + 'HeroDB.db'
        else if FileExists(DBPath + 'ApexM2.DB') then
          DBFile := DBPath + 'ApexM2.DB';
      end;
    end
    else
    begin
      // 如果DBFile不是绝对路径，尝试在不同位置查找
      if not FileExists(DBFile) and (Length(DBFile) > 0) then
      begin
        // 检查是否是绝对路径
        if (DBFile[1] = PathDelim) or ((Length(DBFile) > 2) and (DBFile[2] = ':')) then
        begin
          // 如果是绝对路径但文件不存在，报错
          ShowMessage('Config.ini中指定的SQLite数据库文件不存在: ' + DBFile);
          Exit(False);
        end
        else
        begin
          // 尝试在MirServer目录下
          if FileExists(MirServerPath + DBFile) then
            DBFile := MirServerPath + DBFile
          // 尝试在Mud2\DB目录下
          else if FileExists(MirServerPath + 'Mud2\DB\' + DBFile) then
            DBFile := MirServerPath + 'Mud2\DB\' + DBFile
          else
          begin
            ShowMessage('找不到SQLite数据库文件: ' + DBFile);
            Exit(False);
          end;
        end;
      end;
    end;

    if (DBFile <> '') and FileExists(DBFile) then
    begin
      // 连接到SQLite数据库
      if ConnectToSQLite(DBFile) then
      begin
        FAutoDetected := True;
        Result := True;
        Exit; // 连接成功，返回结果
      end;
    end
    else
    begin
      ShowMessage('无法找到有效的SQLite数据库文件');
      Exit(False);
    end;
  end
  // 如果没有指定数据库类型，检查Access配置
  else if DBType = '' then
  begin
    // 检查是否使用Access数据库
    UseAccessDB := StrToIntDef(ReadIniValue(ConfigIniPath, 'DB', 'UseAccessDB', '0'), 0);

    if UseAccessDB = 1 then
    begin
      // 读取Access数据库文件路径
      AccessFileName := ReadIniValue(ConfigIniPath, 'DB', 'AccessFileName', '');

      // 如果没有指定AccessFileName，尝试在Mud2\DB目录下查找常见的Access数据库文件
      if AccessFileName = '' then
      begin
        DBPath := MirServerPath + 'Mud2\DB\';
        if DirectoryExists(DBPath) then
        begin
          if FileExists(DBPath + 'Mir2.mdb') then
            AccessFileName := DBPath + 'Mir2.mdb'
          else if FileExists(DBPath + 'GameData.mdb') then
            AccessFileName := DBPath + 'GameData.mdb'
          else if FileExists(DBPath + 'HeroDB.mdb') then
            AccessFileName := DBPath + 'HeroDB.mdb';
        end;
      end
      else
      begin
        // 如果AccessFileName不是绝对路径，尝试在不同位置查找
        if not FileExists(AccessFileName) and (Length(AccessFileName) > 0) then
        begin
          // 检查是否是绝对路径
          if (AccessFileName[1] = PathDelim) or ((Length(AccessFileName) > 2) and (AccessFileName[2] = ':')) then
          begin
            // 如果是绝对路径但文件不存在，报错
            ShowMessage('Config.ini中指定的Access数据库文件不存在: ' + AccessFileName);
            Exit(False);
          end
          else
          begin
            // 尝试在MirServer目录下
            if FileExists(MirServerPath + AccessFileName) then
              AccessFileName := MirServerPath + AccessFileName
            // 尝试在Mud2\DB目录下
            else if FileExists(MirServerPath + 'Mud2\DB\' + AccessFileName) then
              AccessFileName := MirServerPath + 'Mud2\DB\' + AccessFileName
            else
            begin
              ShowMessage('找不到Access数据库文件: ' + AccessFileName);
              Exit(False);
            end;
          end;
        end;
      end;

      if (AccessFileName <> '') and FileExists(AccessFileName) then
      begin
        // 连接到Access数据库
        if ConnectToAccess(AccessFileName) then
        begin
          FAutoDetected := True;
          Result := True;
          Exit; // 连接成功，返回结果
        end;
      end
      else
      begin
        ShowMessage('无法找到有效的Access数据库文件');
        Exit(False);
      end;
    end
    // 如果UseAccessDB=0，则尝试连接Paradox数据库
    else if UseAccessDB = 0 then
    begin
      // 查找Mud2\DB目录下的Paradox数据库
      DBPath := MirServerPath + 'Mud2\DB\';

      if DirectoryExists(DBPath) then
      begin
        // 连接到Paradox数据库
        if ConnectToParadox(DBPath) then
        begin
          FAutoDetected := True;
          Result := True;
          Exit; // 连接成功，返回结果
        end;
      end
      else
      begin
        ShowMessage('无法找到有效的Paradox数据库目录: ' + DBPath);
        Exit(False);
      end;
    end
  end;

  // 如果以上所有方法都无法找到有效的数据库，提示用户手动选择
  ShowMessage('无法自动检测数据库类型或找不到有效的数据库文件，请检查Config.ini配置或手动选择数据库文件');
  Exit(False);
end;

procedure TfrmConnectionDialog.btnConnectClick(Sender: TObject);
begin
  if Trim(edtGameDir.Text) = '' then
  begin
    ShowMessage('请选择目录');
    Exit;
  end;

  FGameDir := edtGameDir.Text;

  // 尝试自动检测数据库
  if AutoDetectDatabase(FGameDir) then
  begin
    ModalResult := mrOk;
  end
  else
  begin
    ShowMessage('无法自动检测数据库类型，请检查目录是否正确');
  end;
end;

procedure TfrmConnectionDialog.btnCancelClick(Sender: TObject);
begin
  ModalResult := mrCancel;
end;

function TfrmConnectionDialog.Execute: Boolean;
begin
  Result := ShowModal = mrOk;
end;

function TfrmConnectionDialog.ConnectToSQLite(const AFileName: string): Boolean;
begin
  Result := False;

  try
    if FConnection.Connected then
      FConnection.Close;

    // 设置连接参数
    FConnection.Params.Clear;
    FConnection.Params.DriverID := 'SQLite';

    // 设置格式选项
    FConnection.FormatOptions.MapRules.Clear;
    FConnection.FormatOptions.OwnMapRules := True;
    FConnection.FormatOptions.StrsEmpty2Null := False;
    FConnection.FormatOptions.StrsTrim := False;
    // 设置gb2312字符集以正确处理中文
    FConnection.Params.Add('CharacterSet=gb2312');

    FConnection.Params.Database := AFileName;
    FConnection.Open;

    Result := True;
  except
    on E: Exception do
    begin
      ShowMessage('连接SQLite数据库失败: ' + E.Message);
    end;
  end;
end;

function TfrmConnectionDialog.ConnectToAccess(const AFileName: string): Boolean;
var
  FileExt: string;
  ADOConnection: TADOConnection;
  ADOQuery: TADOQuery;
  ConnectionString: string;
begin
  Result := False;

  try
    if FConnection.Connected then
      FConnection.Close;

    // 获取文件扩展名
    FileExt := LowerCase(ExtractFileExt(AFileName));

    // 方法1：尝试MSAcc驱动
    try
      FConnection.Params.Clear;
      FConnection.Params.DriverID := 'MSAcc';
      FConnection.Params.Database := AFileName;

      // 设置格式选项
      FConnection.FormatOptions.MapRules.Clear;
      FConnection.FormatOptions.OwnMapRules := True;
      FConnection.FormatOptions.StrsEmpty2Null := False;
      FConnection.FormatOptions.StrsTrim := False;

      FConnection.Open;
      Result := True;
      Exit;
    except
      // 如果MSAcc驱动失败，尝试方法2：使用ADO
    end;

    // 方法2：使用ADO连接
    try
      ADOConnection := TADOConnection.Create(nil);
      try
        // 构建ADO连接字符串
        if FileExt = '.mdb' then
          ConnectionString := 'Provider=Microsoft.Jet.OLEDB.4.0;Data Source=' + AFileName + ';Persist Security Info=False;'
        else if FileExt = '.accdb' then
          ConnectionString := 'Provider=Microsoft.ACE.OLEDB.12.0;Data Source=' + AFileName + ';Persist Security Info=False;';

        ADOConnection.ConnectionString := ConnectionString;
        ADOConnection.LoginPrompt := False;
        ADOConnection.Open;

        // 测试连接是否正常
        ADOQuery := TADOQuery.Create(nil);
        try
          ADOQuery.Connection := ADOConnection;

          // 尝试查询系统表
          ADOQuery.SQL.Text := 'SELECT Name FROM MSysObjects WHERE Type=1 AND Flags=0';
          ADOQuery.Open;
          ADOQuery.Close;

          // 如果成功，配置FireDAC连接
          FConnection.Params.Clear;
          FConnection.Params.DriverID := 'MSAcc';
          FConnection.Params.Database := AFileName;

          // 设置格式选项
          FConnection.FormatOptions.MapRules.Clear;
          FConnection.FormatOptions.OwnMapRules := True;
          FConnection.FormatOptions.StrsEmpty2Null := False;
          FConnection.FormatOptions.StrsTrim := False;

          FConnection.Open;
          Result := True;
        finally
          ADOQuery.Free;
        end;
      finally
        ADOConnection.Free;
      end;
    except
      on E: Exception do
      begin
        // 方法3：尝试使用ODBC连接
        try
          FConnection.Params.Clear;
          FConnection.Params.DriverID := 'ODBC';

          // 根据文件扩展名设置不同的驱动
          if FileExt = '.mdb' then
            FConnection.Params.Add('DriverName=Microsoft Access Driver (*.mdb)')
          else if FileExt = '.accdb' then
            FConnection.Params.Add('DriverName=Microsoft Access Driver (*.mdb, *.accdb)');

          // 设置数据库文件路径
          FConnection.Params.Add('Database=' + AFileName);

          // 设置格式选项
          FConnection.FormatOptions.MapRules.Clear;
          FConnection.FormatOptions.OwnMapRules := True;
          FConnection.FormatOptions.StrsEmpty2Null := False;
          FConnection.FormatOptions.StrsTrim := False;

          // 设置元数据相关参数
          FConnection.Params.Add('MetaDefSchema=');
          FConnection.Params.Add('MetaDefCatalog=');

          FConnection.Open;
          Result := True;
        except
          on E2: Exception do
          begin
            ShowMessage('连接Access数据库失败: ' + E2.Message);
          end;
        end;
      end;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('连接Access数据库失败: ' + E.Message);
    end;
  end;
end;

function TfrmConnectionDialog.ConnectToParadox(const ADirectory: string): Boolean;
var
  SearchRec: TSearchRec;
  Directory: string;
  ADOConnection: TADOConnection;
  ConnectionString: string;
  DSNString: string;
  Registry: TRegistry;
  ODBCDriverInstalled: Boolean;
  ErrorMsg: string;
  FoundDBFiles: TStringList;
begin
  Result := False;
  FoundDBFiles := TStringList.Create;
  try
    if FConnection.Connected then
      FConnection.Close;

    // 规范化目录
    Directory := ADirectory;
    if not DirectoryExists(Directory) then
    begin
      ShowMessage('指定的Paradox数据库目录不存在');
      Exit;
    end;

    // 确保目录以路径分隔符结尾
    if (Directory <> '') and (Directory[Length(Directory)] <> PathDelim) then
      Directory := Directory + PathDelim;

    // 检查目录中是否有Paradox数据库文件
    if FindFirst(Directory + '*.db', faAnyFile, SearchRec) = 0 then
    begin
      try
        repeat
          FoundDBFiles.Add(SearchRec.Name);
        until FindNext(SearchRec) <> 0;
      finally
        FindClose(SearchRec);
      end;
    end;

    if FoundDBFiles.Count = 0 then
    begin
      ShowMessage('指定目录中没有找到Paradox数据库文件 (*.db)');
      Exit;
    end;

    // 检查系统是否安装了Paradox ODBC驱动程序
    ODBCDriverInstalled := False;
    Registry := TRegistry.Create;
    try
      Registry.RootKey := HKEY_LOCAL_MACHINE;
      if Registry.OpenKeyReadOnly('SOFTWARE\ODBC\ODBCINST.INI\ODBC Drivers') then
      begin
        ODBCDriverInstalled := Registry.ValueExists('Microsoft Paradox Driver (*.db)') or
                              Registry.ValueExists('Paradox Driver');
      end;
    finally
      Registry.Free;
    end;

    // 方法1：使用ADO连接测试
    try
      ADOConnection := TADOConnection.Create(nil);
      try
        // 构建ADO连接字符串 - 使用标准方法
        ConnectionString := 'Provider=Microsoft.Jet.OLEDB.4.0;Data Source=' + Directory + ';Extended Properties=Paradox 5.x;';

        ADOConnection.ConnectionString := ConnectionString;
        ADOConnection.LoginPrompt := False;
        ADOConnection.Open;

        // 如果ADO连接成功，配置FireDAC的ODBC连接
        FConnection.Params.Clear;
        FConnection.Params.DriverID := 'ODBC';

        // 构建无数据源名的DSN
        DSNString := 'DRIVER={Microsoft Paradox Driver (*.db)};DefaultDir=' + Directory + ';';
        FConnection.Params.Add('ConnectionString=' + DSNString);

        // 设置格式选项
        FConnection.FormatOptions.MapRules.Clear;
        FConnection.FormatOptions.OwnMapRules := True;
        FConnection.FormatOptions.StrsEmpty2Null := False;
        FConnection.FormatOptions.StrsTrim := False;

        // 设置字符集
        FConnection.Params.Add('CharacterSet=gb2312');

        FConnection.Open;
        Result := True;
        Exit;
      finally
        ADOConnection.Free;
      end;
    except
      on E: Exception do
      begin
        ErrorMsg := E.Message;
        // 如果ADO连接失败，尝试方法2
      end;
    end;

    // 方法2：使用更复杂的ADO连接字符串
    try
      ADOConnection := TADOConnection.Create(nil);
      try
        // 使用更详细的连接字符串
        ConnectionString := 'Provider=MSDASQL.1;Extended Properties="Driver={Microsoft Paradox Driver (*.db)};DriverID=538;Fil=Paradox 5.X;DefaultDir=' + Directory + ';Dbq=' + Directory + ';CollatingSequence=ASCII;"';

        ADOConnection.ConnectionString := ConnectionString;
        ADOConnection.LoginPrompt := False;
        ADOConnection.Open;

        // 如果ADO连接成功，配置FireDAC的ODBC连接
        FConnection.Params.Clear;
        FConnection.Params.DriverID := 'ODBC';
        FConnection.Params.Add('ConnectionString=' + ConnectionString);

        // 设置格式选项
        FConnection.FormatOptions.MapRules.Clear;
        FConnection.FormatOptions.OwnMapRules := True;
        FConnection.FormatOptions.StrsEmpty2Null := False;
        FConnection.FormatOptions.StrsTrim := False;

        // 设置字符集
        FConnection.Params.Add('CharacterSet=gb2312');

        FConnection.Open;
        Result := True;
        Exit;
      finally
        ADOConnection.Free;
      end;
    except
      on E: Exception do
      begin
        ErrorMsg := ErrorMsg + #13#10 + E.Message;
        // 如果方法2也失败，尝试方法3
      end;
    end;

    // 方法3：直接使用FireDAC的ODBC连接，尝试多种配置方式
    try
      // 尝试方法1
      FConnection.Params.Clear;
      FConnection.Params.DriverID := 'ODBC';
      FConnection.Params.Add('Database=' + Directory);

      // 根据是否安装ODBC驱动程序选择不同的驱动名称
      if ODBCDriverInstalled then
        FConnection.Params.Add('DriverName=Microsoft Paradox Driver (*.db)')
      else
        FConnection.Params.Add('DriverName=Paradox');

      // 设置格式选项
      FConnection.FormatOptions.MapRules.Clear;
      FConnection.FormatOptions.OwnMapRules := True;
      FConnection.FormatOptions.StrsEmpty2Null := False;
      FConnection.FormatOptions.StrsTrim := False;

      // 设置字符集
      FConnection.Params.Add('CharacterSet=gb2312');

      FConnection.Open;
      Result := True;
      Exit;
    except
      on E: Exception do
      begin
        ErrorMsg := ErrorMsg + #13#10 + E.Message;
        // 如果方法1失败，尝试方法2
        try
          FConnection.Params.Clear;
          FConnection.Params.DriverID := 'ODBC';
          FConnection.Params.Add('ConnectionString=Driver={Microsoft Paradox Driver (*.db)};DefaultDir=' + Directory + ';');

          // 设置格式选项
          FConnection.FormatOptions.MapRules.Clear;
          FConnection.FormatOptions.OwnMapRules := True;
          FConnection.FormatOptions.StrsEmpty2Null := False;
          FConnection.FormatOptions.StrsTrim := False;

          // 设置字符集
          FConnection.Params.Add('CharacterSet=gb2312');

          FConnection.Open;
          Result := True;
          Exit;
        except
          on E2: Exception do
          begin
            ErrorMsg := ErrorMsg + #13#10 + E2.Message;
            // 如果方法2失败，尝试方法3
            try
              FConnection.Params.Clear;
              FConnection.Params.DriverID := 'ODBC';
              FConnection.Params.Add('ConnectionString=Driver={Microsoft Paradox Driver (*.db)};DriverID=538;Fil=Paradox 5.X;DefaultDir=' + Directory + ';Dbq=' + Directory + ';');

              // 设置格式选项
              FConnection.FormatOptions.MapRules.Clear;
              FConnection.FormatOptions.OwnMapRules := True;
              FConnection.FormatOptions.StrsEmpty2Null := False;
              FConnection.FormatOptions.StrsTrim := False;

              // 设置字符集
              FConnection.Params.Add('CharacterSet=gb2312');

              FConnection.Open;
              Result := True;
              Exit;
            except
              on E3: Exception do
              begin
                ErrorMsg := ErrorMsg + #13#10 + E3.Message;
                // 如果方法3失败，尝试方法4
                try
                  FConnection.Params.Clear;
                  FConnection.Params.DriverID := 'ODBC';
                  FConnection.Params.Add('ConnectionString=DSN=MS Paradox Driver;DBQ=' + Directory + ';DefaultDir=' + Directory + ';DriverID=538;FIL=Paradox 5.X;');

                  // 设置格式选项
                  FConnection.FormatOptions.MapRules.Clear;
                  FConnection.FormatOptions.OwnMapRules := True;
                  FConnection.FormatOptions.StrsEmpty2Null := False;
                  FConnection.FormatOptions.StrsTrim := False;

                  // 设置字符集
                  FConnection.Params.Add('CharacterSet=gb2312');

                  FConnection.Open;
                  Result := True;
                  Exit;
                except
                  on E4: Exception do
                  begin
                    // 所有方法都失败，显示详细错误信息
                    ShowMessage('连接Paradox数据库失败: ' + #13#10 +
                               '找到的数据库文件: ' + FoundDBFiles.CommaText + #13#10 +
                               '错误信息: ' + E4.Message + #13#10 +
                               '请确保已安装Paradox ODBC驱动程序' + #13#10 +
                               '可以通过Windows控制面板的"ODBC数据源管理器"来检查Paradox驱动程序');
                  end;
                end;
              end;
            end;
          end;
        end;
      end;
    end;
  finally
    FoundDBFiles.Free;
  end;
end;

end.
