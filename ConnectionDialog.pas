unit ConnectionDialog;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls,
  Vcl.ComCtrls, Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error,
  FireDAC.UI.Intf, FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool,
  FireDAC.Stan.Async, FireDAC.Phys, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef,
  FireDAC.Stan.ExprFuncs, FireDAC.VCLUI.Wait, FireDAC.Comp.Client, FireDAC.Phys.ODBC,
  FireDAC.Phys.ODBCDef, DBConnection, FireDAC.Phys.MSAcc, FireDAC.Phys.MSAccDef,
  Data.Win.ADODB, System.Win.Registry, System.IniFiles;

type
  TfrmConnectionDialog = class(TForm)
    pnlMain: TPanel;
    pnlButtons: TPanel;
    btnConnect: TButton;
    btnCancel: TButton;
    lblGameDir: TLabel;
    edtGameDir: TEdit;
    btnBrowseDir: TButton;
    procedure FormCreate(Sender: TObject);
    procedure btnConnectClick(Sender: TObject);
    procedure btnCancelClick(Sender: TObject);
    procedure btnBrowseDirClick(Sender: TObject);
  private
    { Private declarations }
    FConnection: TFDConnection;
    FDBType: DBConnection.TDBType;
    FAutoDetected: Boolean;
    FGameDir: string;
    function ConnectToSQLite(const AFileName: string): Boolean;
    function ConnectToAccess(const AFileName: string): Boolean;
    function ConnectToParadox(const ADirectory: string): Boolean;
    function ReadIniValue(const FileName, Section, Key, DefaultValue: string): string;
    function AutoDetectDatabase(const AGameDir: string): Boolean;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; AConnection: TFDConnection); reintroduce;
    function Execute: Boolean;
  end;

var
  frmConnectionDialog: TfrmConnectionDialog;

implementation

{$R *.dfm}

constructor TfrmConnectionDialog.Create(AOwner: TComponent; AConnection: TFDConnection);
begin
  inherited Create(AOwner);
  FConnection := AConnection;
  FAutoDetected := False;
  FGameDir := '';
end;

procedure TfrmConnectionDialog.FormCreate(Sender: TObject);
begin
  FDBType := DBConnection.dbtSQLite;

  // 不设置默认目录，让用户手动选择
  FGameDir := '';
  edtGameDir.Text := '';
end;

procedure TfrmConnectionDialog.btnBrowseDirClick(Sender: TObject);
var
  OpenDialog: TFileOpenDialog;
begin
  OpenDialog := TFileOpenDialog.Create(Self);
  try
    // 允许选择文件或文件夹
    OpenDialog.Options := OpenDialog.Options + [fdoPickFolders, fdoPathMustExist];
    OpenDialog.Title := '选择文件或文件夹';
    // 不设置默认目录，让用户从系统默认位置开始选择
    OpenDialog.DefaultFolder := '';

    // 添加文件过滤器
    OpenDialog.FileTypes.Clear;
    OpenDialog.FileTypes.Add.DisplayName := '所有文件 (*.*)';
    OpenDialog.FileTypes.Add.FileMask := '*.*';
    OpenDialog.FileTypes.Add.DisplayName := '数据库文件 (*.db;*.mdb;*.accdb;*.sqlite)';
    OpenDialog.FileTypes.Add.FileMask := '*.db;*.mdb;*.accdb;*.sqlite';

    if OpenDialog.Execute then
    begin
      FGameDir := OpenDialog.FileName;
      edtGameDir.Text := FGameDir;

      // 选择文件或文件夹后自动检测数据库
      if AutoDetectDatabase(FGameDir) then
      begin
        ShowMessage('已成功检测并连接到数据库！');
        ModalResult := mrOk;
      end
      else
      begin
        ShowMessage('无法识别MirServer根目录或未找到有效的数据库配置，请选择MirServer目录或其子目录，或者包含有效数据库文件的目录。');
      end;
    end;
  finally
    OpenDialog.Free;
  end;
end;

function TfrmConnectionDialog.ReadIniValue(const FileName, Section, Key, DefaultValue: string): string;
var
  IniFile: TIniFile;
begin
  Result := DefaultValue;

  if not FileExists(FileName) then
    Exit;

  IniFile := TIniFile.Create(FileName);
  try
    Result := IniFile.ReadString(Section, Key, DefaultValue);
  finally
    IniFile.Free;
  end;
end;

function TfrmConnectionDialog.AutoDetectDatabase(const AGameDir: string): Boolean;
var
  GamePath, MirServerPath, ConfigIniPath, DBPath, DBFile: string;
  UseAccessDB, UseSqliteDB: Integer;
  DBType, AccessFileName: string;
  ParentDir: string;
  IsFile: Boolean;

  // 递归查找MirServer目录的函数
  function FindMirServerDir(const StartDir: string): string;
  var
    DirName: string;
    CurrentPath: string;
    IsMirServerDir: Boolean;
  begin
    Result := '';

    // 检查当前目录名是否为MirServer
    DirName := ExtractFileName(ExcludeTrailingPathDelimiter(StartDir));
    if SameText(DirName, 'MirServer') then
    begin
      Result := StartDir;
      Exit;
    end;

    // 检查当前目录下是否有MirServer子目录
    if DirectoryExists(StartDir + 'MirServer') then
    begin
      Result := IncludeTrailingPathDelimiter(StartDir + 'MirServer');
      Exit;
    end;

    // 检查当前目录是否是MirServer的子目录
    // 通过检查是否存在Config.ini和Mud2\DB目录来判断
    IsMirServerDir := FileExists(StartDir + 'Config.ini') and
                     DirectoryExists(StartDir + 'Mud2') and
                     DirectoryExists(StartDir + 'Mud2\DB');

    if IsMirServerDir then
    begin
      Result := StartDir;
      Exit;
    end;

    // 检查父目录
    ParentDir := ExtractFilePath(ExcludeTrailingPathDelimiter(StartDir));
    if (ParentDir <> '') and (ParentDir <> StartDir) then
    begin
      Result := FindMirServerDir(ParentDir);
      if Result <> '' then
        Exit;
    end;

    // 如果还没找到，尝试向上查找，直到找到包含MirServer的目录
    CurrentPath := StartDir;
    while (CurrentPath <> '') do
    begin
      // 检查当前目录的父目录
      ParentDir := ExtractFilePath(ExcludeTrailingPathDelimiter(CurrentPath));

      // 如果已经到达根目录，则退出循环
      if (ParentDir = CurrentPath) then
        Break;

      // 检查父目录下是否有MirServer目录
      if DirectoryExists(ParentDir + 'MirServer') then
      begin
        Result := IncludeTrailingPathDelimiter(ParentDir + 'MirServer');
        Exit;
      end;

      CurrentPath := ParentDir;
    end;
  end;

begin
  Result := False;

  if AGameDir = '' then
    Exit(False);

  // 判断选择的是文件还是目录
  IsFile := FileExists(AGameDir);

  // 确保路径以反斜杠结尾
  if IsFile then
    GamePath := IncludeTrailingPathDelimiter(ExtractFilePath(AGameDir))
  else
    GamePath := IncludeTrailingPathDelimiter(AGameDir);

  // 尝试查找MirServer目录（任意深度）
  MirServerPath := FindMirServerDir(GamePath);

  // 如果没有找到MirServer目录，使用当前目录
  if MirServerPath = '' then
  begin
    // 检查当前目录是否包含Mud2\DB目录，如果包含则可能是MirServer目录
    if DirectoryExists(GamePath + 'Mud2') and DirectoryExists(GamePath + 'Mud2\DB') then
    begin
      MirServerPath := GamePath;
    end
    else
    begin
      // 检查当前目录的父目录是否包含Mud2\DB目录
      ParentDir := ExtractFilePath(ExcludeTrailingPathDelimiter(GamePath));
      if DirectoryExists(ParentDir + 'Mud2') and DirectoryExists(ParentDir + 'Mud2\DB') then
      begin
        MirServerPath := ParentDir;
      end
      else
      begin
        // 如果都没找到，使用当前目录
        MirServerPath := GamePath;
      end;
    end;
  end;

  // 构建Config.ini路径
  ConfigIniPath := MirServerPath + 'Config.ini';

  if not FileExists(ConfigIniPath) then
  begin
    // 尝试在上级目录查找
    ConfigIniPath := IncludeTrailingPathDelimiter(ExtractFilePath(ExcludeTrailingPathDelimiter(MirServerPath))) + 'Config.ini';

    if not FileExists(ConfigIniPath) then
      Exit(False);
  end;

  // 读取Config.ini中的配置
  // 1. 首先检查VV引擎特有的DBType字段
  DBType := ReadIniValue(ConfigIniPath, 'Database', 'DBType', '');

  // 2. 检查GameConf节中的配置
  if DBType = '' then
  begin
    DBType := ReadIniValue(ConfigIniPath, 'GameConf', 'DBType', '');
  end;

  // 3. 检查LF/GEE/V8引擎的UseSqliteDB字段
  UseSqliteDB := StrToIntDef(ReadIniValue(ConfigIniPath, 'GameConf', 'UseSqliteDB', '0'), 0);

  // 4. 检查Access数据库配置
  UseAccessDB := StrToIntDef(ReadIniValue(ConfigIniPath, 'GameConf', 'UseAccessDB', '0'), 0);
  if UseAccessDB = 0 then
  begin
    UseAccessDB := StrToIntDef(ReadIniValue(ConfigIniPath, 'Database', 'UseAccessDB', '0'), 0);
  end;

  // 优先检查DBType字段（VV版本特有）
  if (DBType = 'SQLite') or (DBType = 'SQLITE') then
  begin
    // VV版本，使用SQLite数据库
    DBFile := ReadIniValue(ConfigIniPath, 'GameConf', 'DBFile', '');
    if DBFile = '' then
    begin
      DBFile := ReadIniValue(ConfigIniPath, 'Database', 'DBFile', '');
    end;

    // 检查LF/GEE/V8引擎的SQLite配置
    if DBFile = '' then
    begin
      DBFile := ReadIniValue(ConfigIniPath, 'GameConf', 'SqliteDBFile', '');
      if DBFile = '' then
      begin
        DBFile := ReadIniValue(ConfigIniPath, 'GameConf', 'SqliteDBName', '');
      end;
    end;

    if DBFile = '' then
    begin
      // 尝试在默认位置查找SQLite数据库
      DBPath := MirServerPath + 'Mud2\DB\';

      if DirectoryExists(DBPath) then
      begin
        if FileExists(DBPath + 'mir2.db') then
          DBFile := DBPath + 'mir2.db'
        else if FileExists(DBPath + 'game.db') then
          DBFile := DBPath + 'game.db'
        else if FileExists(DBPath + 'HeroDB.db') then
          DBFile := DBPath + 'HeroDB.db'
        else if FileExists(DBPath + 'ApexM2.DB') then
          DBFile := DBPath + 'ApexM2.DB';
      end;
    end
    else
    begin
      // 如果DBFile是相对路径，转换为绝对路径
      if not FileExists(DBFile) and (Length(DBFile) > 0) then
      begin
        // 首先检查是否是绝对路径
        if (DBFile[1] = PathDelim) or ((Length(DBFile) > 2) and (DBFile[2] = ':')) then
        begin
          // 已经是绝对路径，但文件不存在
          ShowMessage('Config.ini中指定的SQLite数据库文件不存在: ' + DBFile);
          Exit(False);
        end
        else
        begin
          // 尝试相对于MirServer根目录
          if FileExists(MirServerPath + DBFile) then
            DBFile := MirServerPath + DBFile
          // 尝试相对于Mud2\DB目录
          else if FileExists(MirServerPath + 'Mud2\DB\' + DBFile) then
            DBFile := MirServerPath + 'Mud2\DB\' + DBFile
          // 尝试相对于当前目录
          else if FileExists(ExtractFilePath(ParamStr(0)) + DBFile) then
            DBFile := ExtractFilePath(ParamStr(0)) + DBFile
          else
          begin
            ShowMessage('无法找到Config.ini中指定的SQLite数据库文件: ' + DBFile);
            Exit(False);
          end;
        end;
      end;
    end;

    if (DBFile <> '') and FileExists(DBFile) then
    begin
      // 连接SQLite数据库
      if ConnectToSQLite(DBFile) then
      begin
        FAutoDetected := True;
        Result := True;
        Exit; // 成功连接后立即退出
      end;
    end
    else
    begin
      ShowMessage('未找到有效的SQLite数据库文件');
      Exit(False);
    end;
  end
  // 检查LF/GEE/V8引擎的UseSqliteDB字段
  else if UseSqliteDB = 1 then
  begin
    // LF/GEE/V8引擎，使用SQLite数据库
    DBFile := ReadIniValue(ConfigIniPath, 'GameConf', 'SqliteDBFile', '');
    if DBFile = '' then
    begin
      DBFile := ReadIniValue(ConfigIniPath, 'GameConf', 'SqliteDBName', '');
    end;

    if DBFile = '' then
    begin
      // 尝试在默认位置查找SQLite数据库
      DBPath := MirServerPath + 'Mud2\DB\';

      if DirectoryExists(DBPath) then
      begin
        if FileExists(DBPath + 'mir2.db') then
          DBFile := DBPath + 'mir2.db'
        else if FileExists(DBPath + 'game.db') then
          DBFile := DBPath + 'game.db'
        else if FileExists(DBPath + 'HeroDB.db') then
          DBFile := DBPath + 'HeroDB.db'
        else if FileExists(DBPath + 'ApexM2.DB') then
          DBFile := DBPath + 'ApexM2.DB';
      end;
    end
    else
    begin
      // 如果DBFile是相对路径，转换为绝对路径
      if not FileExists(DBFile) and (Length(DBFile) > 0) then
      begin
        // 首先检查是否是绝对路径
        if (DBFile[1] = PathDelim) or ((Length(DBFile) > 2) and (DBFile[2] = ':')) then
        begin
          // 已经是绝对路径，但文件不存在
          ShowMessage('Config.ini中指定的SQLite数据库文件不存在: ' + DBFile);
          Exit(False);
        end
        else
        begin
          // 尝试相对于MirServer根目录
          if FileExists(MirServerPath + DBFile) then
            DBFile := MirServerPath + DBFile
          // 尝试相对于Mud2\DB目录
          else if FileExists(MirServerPath + 'Mud2\DB\' + DBFile) then
            DBFile := MirServerPath + 'Mud2\DB\' + DBFile
          // 尝试相对于当前目录
          else if FileExists(ExtractFilePath(ParamStr(0)) + DBFile) then
            DBFile := ExtractFilePath(ParamStr(0)) + DBFile
          else
          begin
            ShowMessage('无法找到Config.ini中指定的SQLite数据库文件: ' + DBFile);
            Exit(False);
          end;
        end;
      end;
    end;

    if (DBFile <> '') and FileExists(DBFile) then
    begin
      // 连接SQLite数据库
      if ConnectToSQLite(DBFile) then
      begin
        FAutoDetected := True;
        Result := True;
        Exit; // 成功连接后立即退出
      end;
    end
    else
    begin
      ShowMessage('未找到有效的SQLite数据库文件');
      Exit(False);
    end;
  end
  // 然后检查是否是Access数据库
  else if UseAccessDB = 1 then
  begin
    // 使用Access数据库
    AccessFileName := ReadIniValue(ConfigIniPath, 'GameConf', 'AccessFileName', '');
    if AccessFileName = '' then
    begin
      AccessFileName := ReadIniValue(ConfigIniPath, 'Database', 'AccessFileName', '');
    end;

    if AccessFileName = '' then
    begin
      // 尝试在默认位置查找Access数据库
      DBPath := MirServerPath + 'Mud2\DB\';

      if DirectoryExists(DBPath) then
      begin
        if FileExists(DBPath + 'Mir2.mdb') then
          AccessFileName := DBPath + 'Mir2.mdb'
        else if FileExists(DBPath + 'GameData.mdb') then
          AccessFileName := DBPath + 'GameData.mdb'
        else if FileExists(DBPath + 'HeroDB.mdb') then
          AccessFileName := DBPath + 'HeroDB.mdb';
      end;
    end
    else
    begin
      // 如果AccessFileName是相对路径，转换为绝对路径
      if not FileExists(AccessFileName) and (Length(AccessFileName) > 0) then
      begin
        // 首先检查是否是绝对路径
        if (AccessFileName[1] = PathDelim) or ((Length(AccessFileName) > 2) and (AccessFileName[2] = ':')) then
        begin
          // 已经是绝对路径，但文件不存在
          ShowMessage('Config.ini中指定的Access数据库文件不存在: ' + AccessFileName);
          Exit(False);
        end
        else
        begin
          // 尝试相对于MirServer根目录
          if FileExists(MirServerPath + AccessFileName) then
            AccessFileName := MirServerPath + AccessFileName
          // 尝试相对于Mud2\DB目录
          else if FileExists(MirServerPath + 'Mud2\DB\' + AccessFileName) then
            AccessFileName := MirServerPath + 'Mud2\DB\' + AccessFileName
          // 尝试相对于当前目录
          else if FileExists(ExtractFilePath(ParamStr(0)) + AccessFileName) then
            AccessFileName := ExtractFilePath(ParamStr(0)) + AccessFileName
          else
          begin
            ShowMessage('无法找到Config.ini中指定的Access数据库文件: ' + AccessFileName);
            Exit(False);
          end;
        end;
      end;
    end;

    if (AccessFileName <> '') and FileExists(AccessFileName) then
    begin
      // 连接Access数据库
      if ConnectToAccess(AccessFileName) then
      begin
        FAutoDetected := True;
        Result := True;
        Exit; // 成功连接后立即退出
      end;
    end
    else
    begin
      ShowMessage('未找到有效的Access数据库文件');
      Exit(False);
    end;
  end
  // 如果没有明确指定数据库类型，则提示未找到有效的数据库配置
  else
  begin
    ShowMessage('未找到有效的数据库配置，请确认选择的目录包含正确的Config.ini文件，并且已配置数据库类型。');
    Exit(False);
  end;
end;

procedure TfrmConnectionDialog.btnConnectClick(Sender: TObject);
begin
  if Trim(edtGameDir.Text) = '' then
  begin
    ShowMessage('请选择游戏根目录');
    Exit;
  end;

  FGameDir := edtGameDir.Text;

  // 尝试自动检测数据库
  if AutoDetectDatabase(FGameDir) then
  begin
    ModalResult := mrOk;
  end
  else
  begin
    ShowMessage('在选择目录中未找到有效的数据库配置，请选择其他目录。');
  end;
end;

procedure TfrmConnectionDialog.btnCancelClick(Sender: TObject);
begin
  ModalResult := mrCancel;
end;

function TfrmConnectionDialog.ConnectToSQLite(const AFileName: string): Boolean;
begin
  Result := False;

  try
    if FConnection.Connected then
      FConnection.Close;

    // 设置连接参数
    FConnection.Params.Clear;
    FConnection.Params.DriverID := 'SQLite';

    // 设置字符串支持
    FConnection.FormatOptions.MapRules.Clear;
    FConnection.FormatOptions.OwnMapRules := True;
    FConnection.FormatOptions.StrsEmpty2Null := False;
    FConnection.FormatOptions.StrsTrim := False;
    // 使用gb2312编码正确显示中文字符
    FConnection.Params.Add('CharacterSet=gb2312');

    FConnection.Params.Database := AFileName;
    FConnection.Open;

    Result := True;
  except
    on E: Exception do
    begin
      ShowMessage('连接SQLite数据库失败: ' + E.Message);
    end;
  end;
end;

function TfrmConnectionDialog.ConnectToAccess(const AFileName: string): Boolean;
var
  FileExt: string;
  ADOConnection: TADOConnection;
  ADOQuery: TADOQuery;
  ConnectionString: string;
begin
  Result := False;

  try
    if FConnection.Connected then
      FConnection.Close;

    // 获取文件扩展名
    FileExt := LowerCase(ExtractFileExt(AFileName));

    // 尝试方法1：使用MSAcc驱动
    try
      FConnection.Params.Clear;
      FConnection.Params.DriverID := 'MSAcc';
      FConnection.Params.Database := AFileName;

      // 设置字符串支持
      FConnection.FormatOptions.MapRules.Clear;
      FConnection.FormatOptions.OwnMapRules := True;
      FConnection.FormatOptions.StrsEmpty2Null := False;
      FConnection.FormatOptions.StrsTrim := False;

      FConnection.Open;
      Result := True;
      Exit;
    except
      // 如果MSAcc连接失败，尝试方法2：使用ADO
    end;

    // 方法2：使用ADO连接
    try
      ADOConnection := TADOConnection.Create(nil);
      try
        // 设置ADO连接字符串
        if FileExt = '.mdb' then
          ConnectionString := 'Provider=Microsoft.Jet.OLEDB.4.0;Data Source=' + AFileName + ';Persist Security Info=False;'
        else if FileExt = '.accdb' then
          ConnectionString := 'Provider=Microsoft.ACE.OLEDB.12.0;Data Source=' + AFileName + ';Persist Security Info=False;';

        ADOConnection.ConnectionString := ConnectionString;
        ADOConnection.LoginPrompt := False;
        ADOConnection.Open;

        // 测试连接是否成功
        ADOQuery := TADOQuery.Create(nil);
        try
          ADOQuery.Connection := ADOConnection;

          // 尝试获取表列表
          ADOQuery.SQL.Text := 'SELECT Name FROM MSysObjects WHERE Type=1 AND Flags=0';
          ADOQuery.Open;
          ADOQuery.Close;

          // 连接成功，配置FireDAC连接
          FConnection.Params.Clear;
          FConnection.Params.DriverID := 'MSAcc';
          FConnection.Params.Database := AFileName;

          // 设置字符串支持
          FConnection.FormatOptions.MapRules.Clear;
          FConnection.FormatOptions.OwnMapRules := True;
          FConnection.FormatOptions.StrsEmpty2Null := False;
          FConnection.FormatOptions.StrsTrim := False;

          FConnection.Open;
          Result := True;
        finally
          ADOQuery.Free;
        end;
      finally
        ADOConnection.Free;
      end;
    except
      on E: Exception do
      begin
        // 方法3：尝试使用ODBC连接
        try
          FConnection.Params.Clear;
          FConnection.Params.DriverID := 'ODBC';

          // 根据文件扩展名选择不同的驱动程序
          if FileExt = '.mdb' then
            FConnection.Params.Add('DriverName=Microsoft Access Driver (*.mdb)')
          else if FileExt = '.accdb' then
            FConnection.Params.Add('DriverName=Microsoft Access Driver (*.mdb, *.accdb)');

          // 设置数据库文件路径
          FConnection.Params.Add('Database=' + AFileName);

          // 设置字符串支持
          FConnection.FormatOptions.MapRules.Clear;
          FConnection.FormatOptions.OwnMapRules := True;
          FConnection.FormatOptions.StrsEmpty2Null := False;
          FConnection.FormatOptions.StrsTrim := False;

          // 设置其他需要的参数
          FConnection.Params.Add('MetaDefSchema=');
          FConnection.Params.Add('MetaDefCatalog=');

          FConnection.Open;
          Result := True;
        except
          on E2: Exception do
          begin
            ShowMessage('连接Access数据库失败: ' + E2.Message);
          end;
        end;
      end;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('连接Access数据库失败: ' + E.Message);
    end;
  end;
end;

function TfrmConnectionDialog.ConnectToParadox(const ADirectory: string): Boolean;
var
  SearchRec: TSearchRec;
  Directory: string;
  ADOConnection: TADOConnection;
  ConnectionString: string;
  DSNString: string;
  Registry: TRegistry;
  ODBCDriverInstalled: Boolean;
  ErrorMsg: string;
  FoundDBFiles: TStringList;
begin
  Result := False;
  FoundDBFiles := TStringList.Create;
  try
    if FConnection.Connected then
      FConnection.Close;

    // 确认目录存在
    Directory := ADirectory;
    if not DirectoryExists(Directory) then
    begin
      ShowMessage('指定的Paradox数据库目录不存在');
      Exit;
    end;

    // 确认目录以反斜杠结尾
    if (Directory <> '') and (Directory[Length(Directory)] <> PathDelim) then
      Directory := Directory + PathDelim;

    // 检查目录中是否有Paradox数据文件
    if FindFirst(Directory + '*.db', faAnyFile, SearchRec) = 0 then
    begin
      try
        repeat
          FoundDBFiles.Add(SearchRec.Name);
        until FindNext(SearchRec) <> 0;
      finally
        FindClose(SearchRec);
      end;
    end;

    if FoundDBFiles.Count = 0 then
    begin
      ShowMessage('指定的目录中没有找到Paradox数据文件 (*.db)');
      Exit;
    end;

    // 检查是否安装了Paradox ODBC驱动程序
    ODBCDriverInstalled := False;
    Registry := TRegistry.Create;
    try
      Registry.RootKey := HKEY_LOCAL_MACHINE;
      if Registry.OpenKeyReadOnly('SOFTWARE\ODBC\ODBCINST.INI\ODBC Drivers') then
      begin
        ODBCDriverInstalled := Registry.ValueExists('Microsoft Paradox Driver (*.db)') or
                              Registry.ValueExists('Paradox Driver');
      end;
    finally
      Registry.Free;
    end;

    // 方法1：使用ADO直接连接
    try
      ADOConnection := TADOConnection.Create(nil);
      try
        // 设置ADO连接字符串 - 尝试多种提供程序
        ConnectionString := 'Provider=Microsoft.Jet.OLEDB.4.0;Data Source=' + Directory + ';Extended Properties=Paradox 5.x;';

        ADOConnection.ConnectionString := ConnectionString;
        ADOConnection.LoginPrompt := False;
        ADOConnection.Open;

        // 如果ADO连接成功，使用FireDAC的ODBC连接
        FConnection.Params.Clear;
        FConnection.Params.DriverID := 'ODBC';

        // 创建一个临时DSN
        DSNString := 'DRIVER={Microsoft Paradox Driver (*.db)};DefaultDir=' + Directory + ';';
        FConnection.Params.Add('ConnectionString=' + DSNString);

        // 设置字符串支持
        FConnection.FormatOptions.MapRules.Clear;
        FConnection.FormatOptions.OwnMapRules := True;
        FConnection.FormatOptions.StrsEmpty2Null := False;
        FConnection.FormatOptions.StrsTrim := False;

        // 设置字符集
        FConnection.Params.Add('CharacterSet=gb2312');

        FConnection.Open;
        Result := True;
        Exit;
      finally
        ADOConnection.Free;
      end;
    except
      on E: Exception do
      begin
        ErrorMsg := E.Message;
        // 如果ADO连接失败，尝试方法2
      end;
    end;

    // 方法2：使用不同的ADO提供程序
    try
      ADOConnection := TADOConnection.Create(nil);
      try
        // 尝试使用不同的提供程序
        ConnectionString := 'Provider=MSDASQL.1;Extended Properties="Driver={Microsoft Paradox Driver (*.db)};DriverID=538;Fil=Paradox 5.X;DefaultDir=' + Directory + ';Dbq=' + Directory + ';CollatingSequence=ASCII;"';

        ADOConnection.ConnectionString := ConnectionString;
        ADOConnection.LoginPrompt := False;
        ADOConnection.Open;

        // 如果ADO连接成功，使用FireDAC的ODBC连接
        FConnection.Params.Clear;
        FConnection.Params.DriverID := 'ODBC';
        FConnection.Params.Add('ConnectionString=' + ConnectionString);

        // 设置字符串支持
        FConnection.FormatOptions.MapRules.Clear;
        FConnection.FormatOptions.OwnMapRules := True;
        FConnection.FormatOptions.StrsEmpty2Null := False;
        FConnection.FormatOptions.StrsTrim := False;

        // 设置字符集
        FConnection.Params.Add('CharacterSet=gb2312');

        FConnection.Open;
        Result := True;
        Exit;
      finally
        ADOConnection.Free;
      end;
    except
      on E: Exception do
      begin
        ErrorMsg := ErrorMsg + #13#10 + E.Message;
        // 如果方法2失败，尝试方法3
      end;
    end;

    // 方法3：直接使用FireDAC的ODBC连接，尝试多种连接字符串格式
    try
      // 尝试格式1
      FConnection.Params.Clear;
      FConnection.Params.DriverID := 'ODBC';
      FConnection.Params.Add('Database=' + Directory);

      // 如果检测到ODBC驱动程序已安装，使用具体的驱动名称
      if ODBCDriverInstalled then
        FConnection.Params.Add('DriverName=Microsoft Paradox Driver (*.db)')
      else
        FConnection.Params.Add('DriverName=Paradox');

      // 设置字符串支持
      FConnection.FormatOptions.MapRules.Clear;
      FConnection.FormatOptions.OwnMapRules := True;
      FConnection.FormatOptions.StrsEmpty2Null := False;
      FConnection.FormatOptions.StrsTrim := False;

      // 设置字符集
      FConnection.Params.Add('CharacterSet=gb2312');

      FConnection.Open;
      Result := True;
      Exit;
    except
      on E: Exception do
      begin
        ErrorMsg := ErrorMsg + #13#10 + E.Message;
        // 如果格式1失败，尝试格式2
        try
          FConnection.Params.Clear;
          FConnection.Params.DriverID := 'ODBC';
          FConnection.Params.Add('ConnectionString=Driver={Microsoft Paradox Driver (*.db)};DefaultDir=' + Directory + ';');

          // 设置字符串支持
          FConnection.FormatOptions.MapRules.Clear;
          FConnection.FormatOptions.OwnMapRules := True;
          FConnection.FormatOptions.StrsEmpty2Null := False;
          FConnection.FormatOptions.StrsTrim := False;

          // 设置字符集
          FConnection.Params.Add('CharacterSet=gb2312');

          FConnection.Open;
          Result := True;
          Exit;
        except
          on E2: Exception do
          begin
            ErrorMsg := ErrorMsg + #13#10 + E2.Message;
            // 如果格式2失败，尝试格式3
            try
              FConnection.Params.Clear;
              FConnection.Params.DriverID := 'ODBC';
              FConnection.Params.Add('ConnectionString=Driver={Microsoft Paradox Driver (*.db)};DriverID=538;Fil=Paradox 5.X;DefaultDir=' + Directory + ';Dbq=' + Directory + ';');

              // 设置字符串支持
              FConnection.FormatOptions.MapRules.Clear;
              FConnection.FormatOptions.OwnMapRules := True;
              FConnection.FormatOptions.StrsEmpty2Null := False;
              FConnection.FormatOptions.StrsTrim := False;

              // 设置字符集
              FConnection.Params.Add('CharacterSet=gb2312');

              FConnection.Open;
              Result := True;
              Exit;
            except
              on E3: Exception do
              begin
                ErrorMsg := ErrorMsg + #13#10 + E3.Message;
                // 如果格式3失败，尝试格式4
                try
                  FConnection.Params.Clear;
                  FConnection.Params.DriverID := 'ODBC';
                  FConnection.Params.Add('ConnectionString=DSN=MS Paradox Driver;DBQ=' + Directory + ';DefaultDir=' + Directory + ';DriverID=538;FIL=Paradox 5.X;');

                  // 设置字符串支持
                  FConnection.FormatOptions.MapRules.Clear;
                  FConnection.FormatOptions.OwnMapRules := True;
                  FConnection.FormatOptions.StrsEmpty2Null := False;
                  FConnection.FormatOptions.StrsTrim := False;

                  // 设置字符集
                  FConnection.Params.Add('CharacterSet=gb2312');

                  FConnection.Open;
                  Result := True;
                  Exit;
                except
                  on E4: Exception do
                  begin
                    // 所有方法都失败，显示详细错误信息
                    ShowMessage('连接Paradox数据库失败: ' + #13#10 +
                               '找到的数据库文件: ' + FoundDBFiles.CommaText + #13#10 +
                               '错误信息: ' + E4.Message + #13#10 +
                               '请确认已安装Paradox ODBC驱动程序' + #13#10 +
                               '您可以尝试在Windows控制面板中的"ODBC数据源管理器"中配置Paradox驱动程序');
                  end;
                end;
              end;
            end;
          end;
        end;
      end;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('连接Paradox数据库失败: ' + E.Message);
    end;
  end;

  FoundDBFiles.Free;
end;

function TfrmConnectionDialog.Execute: Boolean;
begin
  Result := (ShowModal = mrOk);
end;

end.