function TfrmConnectionDialog.AutoDetectDatabase(const AGameDir: string): Boolean;
var
  GamePath, MirServerPath, ConfigIniPath, DBPath, DBFile: string;
  UseAccessDB: Integer;
  DBType, AccessFileName: string;
  ParentDir, CurrentDir: string;
  IsFile: Boolean;
begin
  Result := False;

  if AGameDir = '' then
    Exit(False);

  // 判断选择的是文件还是目录
  IsFile := FileExists(AGameDir);
  
  // 确保路径以反斜杠结尾
  if IsFile then
    GamePath := IncludeTrailingPathDelimiter(ExtractFilePath(AGameDir))
  else
    GamePath := IncludeTrailingPathDelimiter(AGameDir);

  // 查找MirServer目录
  // 1. 检查当前目录是否就是MirServer
  if ExtractFileName(ExcludeTrailingPathDelimiter(GamePath)) = 'MirServer' then
    MirServerPath := GamePath
  // 2. 检查当前目录下是否有MirServer子目录
  else if DirectoryExists(GamePath + 'MirServer') then
    MirServerPath := GamePath + 'MirServer\'
  // 3. 检查当前目录是否在MirServer目录下
  else
  begin
    // 向上查找MirServer目录
    CurrentDir := GamePath;
    while (CurrentDir <> '') do
    begin
      ParentDir := IncludeTrailingPathDelimiter(ExtractFilePath(ExcludeTrailingPathDelimiter(CurrentDir)));
      
      // 检查父目录是否为MirServer
      if ExtractFileName(ExcludeTrailingPathDelimiter(ParentDir)) = 'MirServer' then
      begin
        MirServerPath := ParentDir;
        Break;
      end;
      
      // 检查父目录下是否有MirServer
      if DirectoryExists(ParentDir + 'MirServer') then
      begin
        MirServerPath := ParentDir + 'MirServer\';
        Break;
      end;
      
      // 如果已经到达根目录，则退出循环
      if (ParentDir = CurrentDir) then
        Break;
        
      CurrentDir := ParentDir;
    end;
    
    // 如果没有找到MirServer目录，则使用当前目录
    if CurrentDir = '' then
      MirServerPath := GamePath;
  end;

  // 构建Config.ini路径
  ConfigIniPath := MirServerPath + 'Config.ini';

  if not FileExists(ConfigIniPath) then
  begin
    // 尝试在上级目录查找
    ConfigIniPath := IncludeTrailingPathDelimiter(ExtractFilePath(ExcludeTrailingPathDelimiter(MirServerPath))) + 'Config.ini';

    if not FileExists(ConfigIniPath) then
      Exit(False);
  end;

  // 读取Config.ini中的配置
  UseAccessDB := StrToIntDef(ReadIniValue(ConfigIniPath, 'Database', 'UseAccessDB', '0'), 0);
  DBType := ReadIniValue(ConfigIniPath, 'Database', 'DBType', '');

  // 根据配置判断数据库类型
  if DBType = 'SQLite' then
  begin
    // VV引擎，使用SQLite数据库
    DBFile := ReadIniValue(ConfigIniPath, 'Database', 'DBFile', '');

    if DBFile = '' then
    begin
      // 尝试在默认位置查找SQLite数据库
      DBPath := MirServerPath + 'Mud2\DB\';

      if DirectoryExists(DBPath) then
      begin
        if FileExists(DBPath + 'mir2.db') then
          DBFile := DBPath + 'mir2.db'
        else if FileExists(DBPath + 'game.db') then
          DBFile := DBPath + 'game.db';
      end;
    end
    else
    begin
      // 如果DBFile是相对路径，转换为绝对路径
      if not FileExists(DBFile) and (Length(DBFile) > 0) and (DBFile[1] <> PathDelim) then
      begin
        if FileExists(MirServerPath + DBFile) then
          DBFile := MirServerPath + DBFile
        else if FileExists(MirServerPath + 'Mud2\DB\' + DBFile) then
          DBFile := MirServerPath + 'Mud2\DB\' + DBFile;
      end;
    end;

    if (DBFile <> '') and FileExists(DBFile) then
    begin
      // 连接SQLite数据库
      if ConnectToSQLite(DBFile) then
      begin
        FAutoDetected := True;
        Result := True;
      end;
    end;
  end
  else if UseAccessDB = 1 then
  begin
    // 使用Access数据库
    AccessFileName := ReadIniValue(ConfigIniPath, 'Database', 'AccessFileName', '');

    if AccessFileName = '' then
    begin
      // 尝试在默认位置查找Access数据库
      DBPath := MirServerPath + 'Mud2\DB\';

      if DirectoryExists(DBPath) then
      begin
        if FileExists(DBPath + 'Mir2.mdb') then
          AccessFileName := DBPath + 'Mir2.mdb'
        else if FileExists(DBPath + 'GameData.mdb') then
          AccessFileName := DBPath + 'GameData.mdb';
      end;
    end
    else
    begin
      // 如果AccessFileName是相对路径，转换为绝对路径
      if not FileExists(AccessFileName) and (Length(AccessFileName) > 0) and (AccessFileName[1] <> PathDelim) then
      begin
        if FileExists(MirServerPath + AccessFileName) then
          AccessFileName := MirServerPath + AccessFileName
        else if FileExists(MirServerPath + 'Mud2\DB\' + AccessFileName) then
          AccessFileName := MirServerPath + 'Mud2\DB\' + AccessFileName;
      end;
    end;

    if (AccessFileName <> '') and FileExists(AccessFileName) then
    begin
      // 连接Access数据库
      if ConnectToAccess(AccessFileName) then
      begin
        FAutoDetected := True;
        Result := True;
      end;
    end;
  end
  else
  begin
    // 使用Paradox数据库
    DBPath := MirServerPath + 'Mud2\DB\';

    if DirectoryExists(DBPath) then
    begin
      // 连接Paradox数据库
      if ConnectToParadox(DBPath) then
      begin
        FAutoDetected := True;
        Result := True;
      end;
    end;
  end;
end;
