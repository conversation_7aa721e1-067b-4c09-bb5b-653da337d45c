function TDBConnectionManager.GetAccessIndexNames(const ATableName: string): TStringList;
var
  Query: TFDQuery;
  Indices: TStringList;
  Catalog: OleVariant;
  Table: OleVariant;
  IndexList: OleVariant;
  i: Integer;
  ADOConnection: TADOConnection;
  ConnectionString: string;
  FileExt: string;
begin
  Indices := TStringList.Create;
  Result := Indices; // 初始化返回值

  try
    // 检查连接是否有效
    if not Assigned(FConnection) then
      Exit;

    if not FConnection.Connected then
      Exit;

    // 方法1：使用ADO Catalog对象获取索引名
    try
      // 获取数据库文件路径
      FileExt := LowerCase(ExtractFileExt(FConnection.Params.Values['Database']));

      // 创建ADO连接
      ADOConnection := TADOConnection.Create(nil);
      try
        // 设置ADO连接字符串
        if FileExt = '.mdb' then
          ConnectionString := 'Provider=Microsoft.Jet.OLEDB.4.0;Data Source=' + FConnection.Params.Values['Database'] + ';Persist Security Info=False;'
        else // .accdb
          ConnectionString := 'Provider=Microsoft.ACE.OLEDB.12.0;Data Source=' + FConnection.Params.Values['Database'] + ';Persist Security Info=False;';

        ADOConnection.ConnectionString := ConnectionString;
        ADOConnection.LoginPrompt := False;
        ADOConnection.Open;

        // 获取Catalog对象
        Catalog := CreateOleObject('ADOX.Catalog');
        Catalog.ActiveConnection := ADOConnection.ConnectionObject;

        // 获取表对象
        Table := Catalog.Tables[ATableName];

        // 获取索引列表
        IndexList := Table.Indexes;

        // 遍历索引列表
        for i := 0 to IndexList.Count - 1 do
        begin
          Indices.Add(IndexList.Item[i].Name);
        end;

        // 如果成功获取索引名，直接返回
        if Indices.Count > 0 then
        begin
          Result := Indices;
          Exit;
        end;
      finally
        if Assigned(ADOConnection) then
        begin
          if ADOConnection.Connected then
            ADOConnection.Close;
          ADOConnection.Free;
        end;

        // 释放COM对象
        Catalog := Unassigned;
        Table := Unassigned;
        IndexList := Unassigned;
      end;
    except
      // 忽略错误，尝试下一种方法
    end;

    // 方法2：使用直接SQL查询
    Query := TFDQuery.Create(nil);
    try
      Query.Connection := FConnection;

      // 尝试方法2.1：使用INFORMATION_SCHEMA
      try
        Query.SQL.Text := Format('SELECT INDEX_NAME FROM INFORMATION_SCHEMA.INDEXES WHERE TABLE_NAME=''%s''', [ATableName]);
        Query.Open;

        while not Query.Eof do
        begin
          if Query.FieldByName('INDEX_NAME').AsString <> '' then
            Indices.Add(Query.FieldByName('INDEX_NAME').AsString);
          Query.Next;
        end;

        // 如果成功获取索引名，直接返回
        if Indices.Count > 0 then
        begin
          Result := Indices;
          Exit;
        end;
      except
        // 忽略错误，尝试下一种方法
      end;

      // 尝试方法2.2：使用系统表查询
      try
        Query.Close;
        Query.SQL.Text := Format('SELECT Name FROM MSysObjects WHERE Type=4 AND Flags=0 AND Name LIKE ''%s%%''', [ATableName]);
        Query.Open;

        while not Query.Eof do
        begin
          if Query.FieldByName('Name').AsString <> '' then
            Indices.Add(Query.FieldByName('Name').AsString);
          Query.Next;
        end;
      except
        // 忽略错误
      end;
    finally
      Query.Free;
    end;
  except
    // 忽略所有错误，返回当前收集到的索引名
  end;

  Result := Indices;
end;
