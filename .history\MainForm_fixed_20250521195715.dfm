object frmMain: TfrmMain
  Left = 0
  Top = 0
  Caption = 'SQLite数据库工具'
  ClientHeight = 600
  ClientWidth = 900
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Menu = mmMain
  Position = poScreenCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  TextHeight = 15
  object pnlMain: TPanel
    Left = 0
    Top = 26
    Width = 900
    Height = 555
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    object splVertical: TSplitter
      Left = 250
      Top = 0
      Height = 555
      ExplicitLeft = 200
      ExplicitTop = 232
      ExplicitHeight = 100
    end
    object pnlLeft: TPanel
      Left = 0
      Top = 0
      Width = 250
      Height = 555
      Align = alLeft
      BevelOuter = bvNone
      TabOrder = 0
      object tvDatabases: TTreeView
        Left = 0
        Top = 0
        Width = 250
        Height = 555
        Align = alClient
        Indent = 19
        ReadOnly = True
        TabOrder = 0
        OnDblClick = tvDatabasesDblClick
      end
    end
    object pcRight: TPageControl
      Left = 253
      Top = 0
      Width = 647
      Height = 555
      ActivePage = tsQuery
      Align = alClient
      TabOrder = 1
      object tsQuery: TTabSheet
        Caption = 'SQL查询'
      end
      object tsData: TTabSheet
        Caption = '数据'
        object dbgData: TDBGrid
          Left = 0
          Top = 0
          Width = 639
          Height = 525
          Align = alClient
          DataSource = dsData
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgConfirmDelete, dgCancelOnExit, dgTitleClick, dgTitleHotTrack]
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -12
          TitleFont.Name = 'Segoe UI'
          TitleFont.Style = []
        end
      end
      object tsStructure: TTabSheet
        Caption = '结构'
      end
    end
  end
  object sbMain: TStatusBar
    Left = 0
    Top = 581
    Width = 900
    Height = 19
    Panels = <>
    SimplePanel = True
    SimpleText = '未连接'
  end
  object tbMain: TToolBar
    Left = 0
    Top = 0
    Width = 900
    Height = 26
    Caption = 'tbMain'
    Images = ilMain
    TabOrder = 2
    object btnConnect: TToolButton
      Left = 0
      Top = 0
      Action = actConnect
    end
    object btnDisconnect: TToolButton
      Left = 23
      Top = 0
      Action = actDisconnect
    end
    object btnSep1: TToolButton
      Left = 46
      Top = 0
      Width = 8
      Caption = 'btnSep1'
      ImageIndex = 2
      Style = tbsSeparator
    end
    object btnNewQuery: TToolButton
      Left = 54
      Top = 0
      Action = actNewQuery
    end
    object btnOpenQuery: TToolButton
      Left = 77
      Top = 0
      Action = actOpenQuery
    end
    object btnSaveQuery: TToolButton
      Left = 100
      Top = 0
      Action = actSaveQuery
    end
    object btnSep2: TToolButton
      Left = 123
      Top = 0
      Width = 8
      Caption = 'btnSep2'
      ImageIndex = 6
      Style = tbsSeparator
    end
    object btnExecuteQuery: TToolButton
      Left = 131
      Top = 0
      Action = actExecuteQuery
    end
  end
  object mmMain: TMainMenu
    Left = 48
    Top = 72
    object miFile: TMenuItem
      Caption = '文件(&F)'
      object miConnect: TMenuItem
        Action = actConnect
      end
      object miDisconnect: TMenuItem
        Action = actDisconnect
      end
      object N1: TMenuItem
        Caption = '-'
      end
      object miExit: TMenuItem
        Action = actExit
      end
    end
    object miEdit: TMenuItem
      Caption = '编辑(&E)'
    end
    object miView: TMenuItem
      Caption = '视图(&V)'
    end
    object miQuery: TMenuItem
      Caption = '查询(&Q)'
      object miNewQuery: TMenuItem
        Action = actNewQuery
      end
      object miOpenQuery: TMenuItem
        Action = actOpenQuery
      end
      object miSaveQuery: TMenuItem
        Action = actSaveQuery
      end
      object N2: TMenuItem
        Caption = '-'
      end
      object miExecuteQuery: TMenuItem
        Action = actExecuteQuery
      end
    end
    object miTools: TMenuItem
      Caption = '工具(&T)'
    end
    object miHelp: TMenuItem
      Caption = '帮助(&H)'
      object miAbout: TMenuItem
        Caption = '关于(&A)...'
      end
    end
  end
  object ilMain: TImageList
    Left = 48
    Top = 128
  end
  object alMain: TActionList
    Images = ilMain
    Left = 48
    Top = 184
    object actConnect: TAction
      Caption = '连接(&C)'
      OnExecute = actConnectExecute
    end
    object actDisconnect: TAction
      Caption = '断开连接(&D)'
      OnExecute = actDisconnectExecute
    end
    object actExit: TAction
      Caption = '退出(&X)'
      OnExecute = actExitExecute
    end
    object actExecuteQuery: TAction
      Caption = '执行查询(&E)'
      OnExecute = actExecuteQueryExecute
    end
    object actNewQuery: TAction
      Caption = '新建查询(&N)'
      OnExecute = actNewQueryExecute
    end
    object actSaveQuery: TAction
      Caption = '保存查询(&S)'
      OnExecute = actSaveQueryExecute
    end
    object actOpenQuery: TAction
      Caption = '打开(&O)'
      OnExecute = actOpenQueryExecute
    end
  end
  object dsData: TDataSource
    Left = 120
    Top = 184
  end
end
