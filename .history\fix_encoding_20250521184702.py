#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import sys
import codecs
import chardet

def detect_encoding(file_path):
    """检测文件编码"""
    with open(file_path, 'rb') as f:
        raw_data = f.read()
        result = chardet.detect(raw_data)
        return result['encoding'], raw_data

def convert_file(file_path, target_encoding='gb2312'):
    """转换文件编码"""
    try:
        # 检测当前编码
        source_encoding, raw_data = detect_encoding(file_path)
        
        if source_encoding is None:
            print(f"无法检测 {file_path} 的编码")
            return False
        
        # 尝试解码
        try:
            content = raw_data.decode(source_encoding)
        except UnicodeDecodeError:
            # 如果检测到的编码解码失败，尝试其他编码
            for enc in ['utf-8', 'gb2312', 'gbk', 'gb18030', 'latin1']:
                try:
                    content = raw_data.decode(enc)
                    source_encoding = enc
                    print(f"使用备选编码 {enc} 成功解码 {file_path}")
                    break
                except UnicodeDecodeError:
                    continue
            else:
                print(f"无法解码 {file_path}，尝试了多种编码")
                return False
        
        # 如果已经是目标编码，不需要转换
        if source_encoding.lower() == target_encoding.lower():
            print(f"{file_path} 已经是 {target_encoding} 编码")
            return True
        
        # 转换并写入文件
        with codecs.open(file_path, 'w', encoding=target_encoding) as f:
            f.write(content)
        
        print(f"已将 {file_path} 从 {source_encoding} 转换为 {target_encoding}")
        return True
    
    except Exception as e:
        print(f"转换 {file_path} 时出错: {str(e)}")
        return False

def process_directory(directory, extensions=['.pas', '.dfm'], target_encoding='gb2312'):
    """处理目录中的所有指定扩展名的文件"""
    success_count = 0
    fail_count = 0
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if any(file.lower().endswith(ext) for ext in extensions):
                file_path = os.path.join(root, file)
                if convert_file(file_path, target_encoding):
                    success_count += 1
                else:
                    fail_count += 1
    
    print(f"\n处理完成: {success_count} 个文件成功转换, {fail_count} 个文件失败")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python fix_encoding.py <目录路径> [目标编码]")
        sys.exit(1)
    
    directory = sys.argv[1]
    target_encoding = sys.argv[2] if len(sys.argv) > 2 else 'gb2312'
    
    if not os.path.isdir(directory):
        print(f"错误: {directory} 不是有效的目录")
        sys.exit(1)
    
    process_directory(directory, target_encoding=target_encoding)
