function TfrmConnectionDialog.ConnectToSQLite(const AFileName: string): Boolean;
begin
  Result := False;

  try
    if FConnection.Connected then
      FConnection.Close;

    // 设置连接参数
    FConnection.Params.Clear;
    FConnection.Params.DriverID := 'SQLite';

    // 设置字符串支持
    FConnection.FormatOptions.MapRules.Clear;
    FConnection.FormatOptions.OwnMapRules := True;
    FConnection.FormatOptions.StrsEmpty2Null := False;
    FConnection.FormatOptions.StrsTrim := False;
    // 使用gb2312编码正确显示中文字符
    FConnection.Params.Add('CharacterSet=gb2312');

    FConnection.Params.Database := AFileName;
    FConnection.Open;

    Result := True;
  except
    on E: Exception do
    begin
      ShowMessage('连接SQLite数据库失败: ' + E.Message);
    end;
  end;
end;

function TfrmConnectionDialog.ConnectToAccess(const AFileName: string): Boolean;
var
  FileExt: string;
  ADOConnection: TADOConnection;
  ADOQuery: TADOQuery;
  ConnectionString: string;
begin
  Result := False;

  try
    if FConnection.Connected then
      FConnection.Close;

    // 获取文件扩展名
    FileExt := LowerCase(ExtractFileExt(AFileName));

    // 尝试方法1：使用MSAcc驱动
    try
      FConnection.Params.Clear;
      FConnection.Params.DriverID := 'MSAcc';
      FConnection.Params.Database := AFileName;

      // 设置字符串支持
      FConnection.FormatOptions.MapRules.Clear;
      FConnection.FormatOptions.OwnMapRules := True;
      FConnection.FormatOptions.StrsEmpty2Null := False;
      FConnection.FormatOptions.StrsTrim := False;

      FConnection.Open;
      Result := True;
      Exit;
    except
      // 如果MSAcc连接失败，尝试方法2：使用ADO
    end;

    // 方法2：使用ADO连接
    try
      ADOConnection := TADOConnection.Create(nil);
      try
        // 设置ADO连接字符串
        if FileExt = '.mdb' then
          ConnectionString := 'Provider=Microsoft.Jet.OLEDB.4.0;Data Source=' + AFileName + ';Persist Security Info=False;'
        else if FileExt = '.accdb' then
          ConnectionString := 'Provider=Microsoft.ACE.OLEDB.12.0;Data Source=' + AFileName + ';Persist Security Info=False;';

        ADOConnection.ConnectionString := ConnectionString;
        ADOConnection.LoginPrompt := False;
        ADOConnection.Open;

        // 测试连接是否成功
        ADOQuery := TADOQuery.Create(nil);
        try
          ADOQuery.Connection := ADOConnection;

          // 尝试获取表列表
          ADOQuery.SQL.Text := 'SELECT Name FROM MSysObjects WHERE Type=1 AND Flags=0';
          ADOQuery.Open;
          ADOQuery.Close;

          // 连接成功，配置FireDAC连接
          FConnection.Params.Clear;
          FConnection.Params.DriverID := 'MSAcc';
          FConnection.Params.Database := AFileName;

          // 设置字符串支持
          FConnection.FormatOptions.MapRules.Clear;
          FConnection.FormatOptions.OwnMapRules := True;
          FConnection.FormatOptions.StrsEmpty2Null := False;
          FConnection.FormatOptions.StrsTrim := False;

          FConnection.Open;
          Result := True;
        finally
          ADOQuery.Free;
        end;
      finally
        ADOConnection.Free;
      end;
    except
      on E: Exception do
      begin
        // 方法3：尝试使用ODBC连接
        try
          FConnection.Params.Clear;
          FConnection.Params.DriverID := 'ODBC';

          // 根据文件扩展名选择不同的驱动程序
          if FileExt = '.mdb' then
            FConnection.Params.Add('DriverName=Microsoft Access Driver (*.mdb)')
          else if FileExt = '.accdb' then
            FConnection.Params.Add('DriverName=Microsoft Access Driver (*.mdb, *.accdb)');

          // 设置数据库文件路径
          FConnection.Params.Add('Database=' + AFileName);

          // 设置字符串支持
          FConnection.FormatOptions.MapRules.Clear;
          FConnection.FormatOptions.OwnMapRules := True;
          FConnection.FormatOptions.StrsEmpty2Null := False;
          FConnection.FormatOptions.StrsTrim := False;

          // 设置其他需要的参数
          FConnection.Params.Add('MetaDefSchema=');
          FConnection.Params.Add('MetaDefCatalog=');

          FConnection.Open;
          Result := True;
        except
          on E2: Exception do
          begin
            ShowMessage('连接Access数据库失败: ' + E2.Message);
          end;
        end;
      end;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('连接Access数据库失败: ' + E.Message);
    end;
  end;
end;
