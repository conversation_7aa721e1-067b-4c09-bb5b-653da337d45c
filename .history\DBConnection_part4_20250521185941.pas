function TDBConnectionManager.GetParadoxTableNames: TStringList;
var
  Query: TFDQuery;
  Tables: TStringList;
  SearchRec: TSearchRec;
  DBPath: string;
begin
  Tables := TStringList.Create;
  Result := Tables; // 初始化返回值

  try
    // 检查连接是否有效
    if not Assigned(FConnection) then
      Exit;

    if not FConnection.Connected then
      Exit;

    // 方法1：使用ODBC查询
    try
      Query := TFDQuery.Create(nil);
      try
        Query.Connection := FConnection;
        Query.SQL.Text := 'SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE=''TABLE''';
        Query.Open;

        while not Query.Eof do
        begin
          if Query.FieldByName('TABLE_NAME').AsString <> '' then
            Tables.Add(Query.FieldByName('TABLE_NAME').AsString);
          Query.Next;
        end;

        // 如果成功获取表名，直接返回
        if Tables.Count > 0 then
        begin
          Result := Tables;
          Exit;
        end;
      finally
        Query.Free;
      end;
    except
      // 忽略错误，尝试下一种方法
    end;

    // 方法2：直接扫描目录中的.DB文件
    try
      // 获取数据库目录
      DBPath := FConnection.Params.Values['Database'];
      if DBPath = '' then
        DBPath := FConnection.Params.Values['DefaultDir'];

      // 确保路径以反斜杠结尾
      if (DBPath <> '') and (DBPath[Length(DBPath)] <> PathDelim) then
        DBPath := DBPath + PathDelim;

      // 扫描目录中的.DB文件
      if FindFirst(DBPath + '*.DB', faAnyFile, SearchRec) = 0 then
      begin
        try
          repeat
            // 添加不带扩展名的文件名
            Tables.Add(ChangeFileExt(SearchRec.Name, ''));
          until FindNext(SearchRec) <> 0;
        finally
          FindClose(SearchRec);
        end;
      end;
    except
      // 忽略错误
    end;
  except
    // 忽略所有错误，返回当前收集到的表名
  end;

  Result := Tables;
end;

function TDBConnectionManager.GetParadoxViewNames: TStringList;
var
  Views: TStringList;
begin
  Views := TStringList.Create;
  Result := Views; // 初始化返回值

  try
    // Paradox不支持视图，返回空列表
  except
    // 忽略所有错误
  end;

  Result := Views;
end;

function TDBConnectionManager.GetParadoxIndexNames(const ATableName: string): TStringList;
var
  Indices: TStringList;
  SearchRec: TSearchRec;
  DBPath: string;
  IndexPattern: string;
begin
  Indices := TStringList.Create;
  Result := Indices; // 初始化返回值

  try
    // 检查连接是否有效
    if not Assigned(FConnection) then
      Exit;

    if not FConnection.Connected then
      Exit;

    // 获取数据库目录
    DBPath := FConnection.Params.Values['Database'];
    if DBPath = '' then
      DBPath := FConnection.Params.Values['DefaultDir'];

    // 确保路径以反斜杠结尾
    if (DBPath <> '') and (DBPath[Length(DBPath)] <> PathDelim) then
      DBPath := DBPath + PathDelim;

    // Paradox索引文件通常以表名开头，后跟索引名，扩展名为.PX
    IndexPattern := DBPath + ATableName + '*.PX';

    // 扫描目录中的索引文件
    if FindFirst(IndexPattern, faAnyFile, SearchRec) = 0 then
    begin
      try
        repeat
          // 添加索引名（去掉表名前缀和扩展名）
          Indices.Add(ChangeFileExt(Copy(SearchRec.Name, Length(ATableName) + 1, Length(SearchRec.Name)), ''));
        until FindNext(SearchRec) <> 0;
      finally
        FindClose(SearchRec);
      end;
    end;
  except
    // 忽略所有错误，返回当前收集到的索引名
  end;

  Result := Indices;
end;
