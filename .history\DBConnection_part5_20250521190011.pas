procedure TDBConnectionManager.LoadTables;
var
  Tables: TStringList;
  i: Integer;
  TablesNode: TTreeNode;
  TableNode: TTreeNode;
begin
  // 检查连接是否有效
  if not Assigned(FConnection) then
    Exit;

  if not FConnection.Connected then
    Exit;

  // 检查树视图是否有效
  if not Assigned(FTreeView) then
    Exit;

  // 查找表节点
  TablesNode := nil;
  for i := 0 to FTreeView.Items.Count - 1 do
  begin
    if (FTreeView.Items[i].Level = 1) and (FTreeView.Items[i].Text = '表') then
    begin
      TablesNode := FTreeView.Items[i];
      Break;
    end;
  end;

  if not Assigned(TablesNode) then
    Exit;

  // 清空表节点下的所有子节点
  while TablesNode.Count > 0 do
    TablesNode.Item[0].Delete;

  // 根据数据库类型获取表名
  Tables := nil;
  try
    if IsSQLite then
    begin
      // 使用SQLite查询获取表名
      try
        with ExecuteQuery('SELECT name FROM sqlite_master WHERE type=''table'' ORDER BY name') do
        begin
          Tables := TStringList.Create;
          while not Eof do
          begin
            Tables.Add(FieldByName('name').AsString);
            Next;
          end;
          Free;
        end;
      except
        // 忽略错误
      end;
    end
    else if IsAccess then
    begin
      // 使用Access方法获取表名
      Tables := GetAccessTableNames;
    end
    else if IsParadox then
    begin
      // 使用Paradox方法获取表名
      Tables := GetParadoxTableNames;
    end;

    // 添加表节点
    if Assigned(Tables) and (Tables.Count > 0) then
    begin
      for i := 0 to Tables.Count - 1 do
      begin
        TableNode := FTreeView.Items.AddChild(TablesNode, Tables[i]);
        TableNode.ImageIndex := 1; // 表图标索引
        TableNode.SelectedIndex := 1;
      end;

      // 展开表节点
      TablesNode.Expand(False);
    end;
  finally
    if Assigned(Tables) then
      Tables.Free;
  end;
end;

procedure TDBConnectionManager.LoadViews;
var
  Views: TStringList;
  i: Integer;
  ViewsNode: TTreeNode;
  ViewNode: TTreeNode;
begin
  // 检查连接是否有效
  if not Assigned(FConnection) then
    Exit;

  if not FConnection.Connected then
    Exit;

  // 检查树视图是否有效
  if not Assigned(FTreeView) then
    Exit;

  // 查找视图节点
  ViewsNode := nil;
  for i := 0 to FTreeView.Items.Count - 1 do
  begin
    if (FTreeView.Items[i].Level = 1) and (FTreeView.Items[i].Text = '视图') then
    begin
      ViewsNode := FTreeView.Items[i];
      Break;
    end;
  end;

  if not Assigned(ViewsNode) then
    Exit;

  // 清空视图节点下的所有子节点
  while ViewsNode.Count > 0 do
    ViewsNode.Item[0].Delete;

  // 根据数据库类型获取视图名
  Views := nil;
  try
    if IsSQLite then
    begin
      // 使用SQLite查询获取视图名
      try
        with ExecuteQuery('SELECT name FROM sqlite_master WHERE type=''view'' ORDER BY name') do
        begin
          Views := TStringList.Create;
          while not Eof do
          begin
            Views.Add(FieldByName('name').AsString);
            Next;
          end;
          Free;
        end;
      except
        // 忽略错误
      end;
    end
    else if IsAccess then
    begin
      // 使用Access方法获取视图名
      Views := GetAccessViewNames;
    end
    else if IsParadox then
    begin
      // 使用Paradox方法获取视图名
      Views := GetParadoxViewNames;
    end;

    // 添加视图节点
    if Assigned(Views) and (Views.Count > 0) then
    begin
      for i := 0 to Views.Count - 1 do
      begin
        ViewNode := FTreeView.Items.AddChild(ViewsNode, Views[i]);
        ViewNode.ImageIndex := 2; // 视图图标索引
        ViewNode.SelectedIndex := 2;
      end;

      // 展开视图节点
      ViewsNode.Expand(False);
    end;
  finally
    if Assigned(Views) then
      Views.Free;
  end;
end;
