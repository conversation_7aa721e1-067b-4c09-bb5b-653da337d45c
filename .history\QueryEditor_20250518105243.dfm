object frmQueryEditor: TfrmQueryEditor
  Left = 0
  Top = 0
  Caption = '查询编辑器'
  ClientHeight = 500
  ClientWidth = 700
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poScreenCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  TextHeight = 15
  object pnlMain: TPanel
    Left = 0
    Top = 26
    Width = 700
    Height = 455
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    object splHorizontal: TSplitter
      Left = 0
      Top = 200
      Width = 700
      Height = 3
      Cursor = crVSplit
      Align = alTop
      ExplicitTop = 150
      ExplicitWidth = 200
    end
    object pnlTop: TPanel
      Left = 0
      Top = 0
      Width = 700
      Height = 200
      Align = alTop
      BevelOuter = bvNone
      TabOrder = 0
      object memSQL: TMemo
        Left = 0
        Top = 0
        Width = 700
        Height = 200
        Align = alClient
        Font.Charset = DEFAULT_CHARSET
        Font.Color = clWindowText
        Font.Height = -13
        Font.Name = 'Consolas'
        Font.Style = []
        ParentFont = False
        ScrollBars = ssBoth
        TabOrder = 0
        WantTabs = True
      end
    end
    object pnlBottom: TPanel
      Left = 0
      Top = 203
      Width = 700
      Height = 252
      Align = alClient
      BevelOuter = bvNone
      TabOrder = 1
      object pcResults: TPageControl
        Left = 0
        Top = 0
        Width = 700
        Height = 252
        ActivePage = tsData
        Align = alClient
        TabOrder = 0
        object tsData: TTabSheet
          Caption = '数据'
          object dbgResults: TDBGrid
            Left = 0
            Top = 0
            Width = 692
            Height = 222
            Align = alClient
            DataSource = dsResults
            Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgConfirmDelete, dgCancelOnExit, dgTitleClick, dgTitleHotTrack]
            TabOrder = 0
            TitleFont.Charset = DEFAULT_CHARSET
            TitleFont.Color = clWindowText
            TitleFont.Height = -12
            TitleFont.Name = 'Segoe UI'
            TitleFont.Style = []
          end
        end
        object tsMessages: TTabSheet
          Caption = '消息'
          ImageIndex = 1
          object memMessages: TMemo
            Left = 0
            Top = 0
            Width = 692
            Height = 222
            Align = alClient
            ReadOnly = True
            ScrollBars = ssBoth
            TabOrder = 0
          end
        end
      end
    end
  end
  object tbMain: TToolBar
    Left = 0
    Top = 0
    Width = 700
    Height = 26
    Caption = 'tbMain'
    TabOrder = 1
    object btnExecute: TToolButton
      Left = 0
      Top = 0
      Action = actExecute
    end
    object btnExecuteScript: TToolButton
      Left = 23
      Top = 0
      Action = actExecuteScript
    end
    object btnSep1: TToolButton
      Left = 46
      Top = 0
      Width = 8
      Caption = 'btnSep1'
      ImageIndex = 2
      Style = tbsSeparator
    end
    object btnOpen: TToolButton
      Left = 54
      Top = 0
      Action = actOpen
    end
    object btnSave: TToolButton
      Left = 77
      Top = 0
      Action = actSave
    end
    object btnSep2: TToolButton
      Left = 100
      Top = 0
      Width = 8
      Caption = 'btnSep2'
      ImageIndex = 5
      Style = tbsSeparator
    end
    object btnExplain: TToolButton
      Left = 108
      Top = 0
      Action = actExplain
    end
  end
  object alMain: TActionList
    Left = 40
    Top = 80
    object actExecute: TAction
      Caption = '执行'
      OnExecute = actExecuteExecute
    end
    object actExecuteScript: TAction
      Caption = '执行脚本'
      OnExecute = actExecuteScriptExecute
    end
    object actOpen: TAction
      Caption = '打开'
      OnExecute = actOpenExecute
    end
    object actSave: TAction
      Caption = '保存'
      OnExecute = actSaveExecute
    end
    object actExplain: TAction
      Caption = '解释查询'
      OnExecute = actExplainExecute
    end
  end
  object dsResults: TDataSource
    Left = 40
    Top = 144
  end
  object sbMain: TStatusBar
    Left = 0
    Top = 481
    Width = 700
    Height = 19
    Panels = <>
    SimplePanel = True
  end
end
