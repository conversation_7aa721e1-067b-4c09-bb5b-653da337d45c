unit ConnectionDialog;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls,
  Vcl.ComCtrls, Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error,
  FireDAC.UI.Intf, FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool,
  FireDAC.Stan.Async, FireDAC.Phys, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef,
  FireDAC.Stan.ExprFuncs, FireDAC.VCLUI.Wait, FireDAC.Comp.Client, FireDAC.Phys.ODBC,
  FireDAC.Phys.ODBCDef, DBConnection, FireDAC.Phys.MSAcc, FireDAC.Phys.MSAccDef,
  Data.Win.ADODB, System.Win.Registry, System.IniFiles;

type
  TfrmConnectionDialog = class(TForm)
    pnlMain: TPanel;
    pnlButtons: TPanel;
    btnConnect: TButton;
    btnCancel: TButton;
    lblGameDir: TLabel;
    edtGameDir: TEdit;
    btnBrowseDir: TButton;
    procedure FormCreate(Sender: TObject);
    procedure btnConnectClick(Sender: TObject);
    procedure btnCancelClick(Sender: TObject);
    procedure btnBrowseDirClick(Sender: TObject);
  private
    { Private declarations }
    FConnection: TFDConnection;
    FDBType: DBConnection.TDBType;
    FAutoDetected: Boolean;
    FGameDir: string;
    function ConnectToSQLite(const AFileName: string): Boolean;
    function ConnectToAccess(const AFileName: string): Boolean;
    function ConnectToParadox(const ADirectory: string): Boolean;
    function ReadIniValue(const FileName, Section, Key, DefaultValue: string): string;
    function AutoDetectDatabase(const AGameDir: string): Boolean;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; AConnection: TFDConnection); reintroduce;
    function Execute: Boolean;
  end;

var
  frmConnectionDialog: TfrmConnectionDialog;

implementation

{$R *.dfm}

constructor TfrmConnectionDialog.Create(AOwner: TComponent; AConnection: TFDConnection);
begin
  inherited Create(AOwner);
  FConnection := AConnection;
  FAutoDetected := False;
  FGameDir := '';
end;

procedure TfrmConnectionDialog.FormCreate(Sender: TObject);
begin
  FDBType := DBConnection.dbtSQLite;

  // 尝试获取当前目录作为默认游戏目录
  FGameDir := ExtractFilePath(ParamStr(0));
  edtGameDir.Text := FGameDir;
end;

procedure TfrmConnectionDialog.btnBrowseDirClick(Sender: TObject);
var
  OpenDialog: TFileOpenDialog;
begin
  OpenDialog := TFileOpenDialog.Create(Self);
  try
    // 允许选择文件或文件夹
    OpenDialog.Options := OpenDialog.Options + [fdoPickFolders, fdoPathMustExist];
    OpenDialog.Title := '选择文件或文件夹';
    OpenDialog.DefaultFolder := ExtractFilePath(ParamStr(0));

    // 添加文件过滤器
    OpenDialog.FileTypes.Clear;
    OpenDialog.FileTypes.Add.DisplayName := '所有文件 (*.*)';
    OpenDialog.FileTypes.Add.FileMask := '*.*';
    OpenDialog.FileTypes.Add.DisplayName := '数据库文件 (*.db;*.mdb;*.accdb;*.sqlite)';
    OpenDialog.FileTypes.Add.FileMask := '*.db;*.mdb;*.accdb;*.sqlite';

    if OpenDialog.Execute then
    begin
      FGameDir := OpenDialog.FileName;
      edtGameDir.Text := FGameDir;

      // 选择文件或文件夹后自动检测数据库
      if AutoDetectDatabase(FGameDir) then
      begin
        ShowMessage('已成功检测并连接到数据库！');
        ModalResult := mrOk;
      end
      else
      begin
        ShowMessage('无法识别MirServer根目录或未找到有效的数据库配置，请选择其他文件或文件夹。');
      end;
    end;
  finally
    OpenDialog.Free;
  end;
end;
