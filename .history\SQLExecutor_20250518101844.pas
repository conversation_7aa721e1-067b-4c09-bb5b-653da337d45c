unit SQLExecutor;

interface

uses
  System.SysUtils, System.Classes, Data.DB, System.DateUtils,
  FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error, FireDAC.UI.Intf,
  FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool, FireDAC.Stan.Async,
  FireDAC.Phys, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs,
  FireDAC.VCLUI.Wait, FireDAC.Comp.Client, FireDAC.Comp.DataSet, FireDAC.Comp.Script,
  Vcl.Dialogs, Vcl.StdCtrls, Vcl.Grids, Vcl.DBGrids, Vcl.ExtCtrls, Vcl.ComCtrls;

type
  TSQLExecutor = class
  private
    FConnection: TFDConnection;
    FMemo: TMemo;
    FPageControl: TPageControl;
    FStatusBar: TStatusBar;
    FDataSource: TDataSource;
    FDBGrid: TDBGrid;
    FResultsMemo: TMemo;

    procedure ShowResults(AQuery: TFDQuery);
    procedure ShowError(const AErrorMsg: string);
    procedure ShowMessage(const AMessage: string);
  public
    constructor Create(AConnection: TFDConnection; AMemo: TMemo;
      APageControl: TPageControl; AStatusBar: TStatusBar;
      ADataSource: TDataSource; ADBGrid: TDBGrid; AResultsMemo: TMemo);
    destructor Destroy; override;

    procedure ExecuteSQL;
    procedure ExecuteScript;
    procedure ExplainQuery;
  end;

implementation

constructor TSQLExecutor.Create(AConnection: TFDConnection; AMemo: TMemo;
  APageControl: TPageControl; AStatusBar: TStatusBar;
  ADataSource: TDataSource; ADBGrid: TDBGrid; AResultsMemo: TMemo);
begin
  FConnection := AConnection;
  FMemo := AMemo;
  FPageControl := APageControl;
  FStatusBar := AStatusBar;
  FDataSource := ADataSource;
  FDBGrid := ADBGrid;
  FResultsMemo := AResultsMemo;
end;

destructor TSQLExecutor.Destroy;
begin
  inherited;
end;

procedure TSQLExecutor.ExecuteSQL;
var
  Query: TFDQuery;
  SQL: string;
  StartTime, EndTime: TDateTime;
  ExecutionTime: Int64;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
  begin
    ShowError('δ���ӵ����ݿ�');
    Exit;
  end;

  SQL := FMemo.SelText;
  if SQL = '' then
    SQL := FMemo.Text;

  if Trim(SQL) = '' then
  begin
    ShowError('û��SQL����ִ��');
    Exit;
  end;

  Query := TFDQuery.Create(nil);
  try
    Query.Connection := FConnection;
    Query.SQL.Text := SQL;

    StartTime := Now;

    try
      Query.Open;
      EndTime := Now;
      ExecutionTime := MilliSecondsBetween(EndTime, StartTime);

      ShowResults(Query);
      ShowMessage(Format('��ѯִ�гɹ�����ʱ: %d ���룬���� %d �С�',
        [ExecutionTime, Query.RecordCount]));
    except
      on E: Exception do
      begin
        ShowError('ִ��SQLʧ��: ' + E.Message);
      end;
    end;
  finally
    if not (Query.Active and (FDataSource.DataSet = Query)) then
      Query.Free;
  end;
end;

procedure TSQLExecutor.ExecuteScript;
var
  Script: TFDScript;
  SQL: string;
  StartTime, EndTime: TDateTime;
  ExecutionTime: Int64;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
  begin
    ShowError('δ���ӵ����ݿ�');
    Exit;
  end;

  SQL := FMemo.SelText;
  if SQL = '' then
    SQL := FMemo.Text;

  if Trim(SQL) = '' then
  begin
    ShowError('û��SQL�ű���ִ��');
    Exit;
  end;

  Script := TFDScript.Create(nil);
  try
    Script.Connection := FConnection;
    Script.SQLScripts.Clear;
    Script.SQLScripts.Add;
    Script.SQLScripts[0].SQL.Text := SQL;

    StartTime := Now;

    try
      Script.ValidateAll;
      Script.ExecuteAll;

      EndTime := Now;
      ExecutionTime := MilliSecondsBetween(EndTime, StartTime);

      ShowMessage(Format('�ű�ִ�гɹ�����ʱ: %d ���롣', [ExecutionTime]));
    except
      on E: Exception do
      begin
        ShowError('ִ�нű�ʧ��: ' + E.Message);
      end;
    end;
  finally
    Script.Free;
  end;
end;

procedure TSQLExecutor.ExplainQuery;
var
  Query: TFDQuery;
  SQL, ExplainSQL: string;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
  begin
    ShowError('δ���ӵ����ݿ�');
    Exit;
  end;

  SQL := FMemo.SelText;
  if SQL = '' then
    SQL := FMemo.Text;

  if Trim(SQL) = '' then
  begin
    ShowError('û��SQL���ɽ���');
    Exit;
  end;

  ExplainSQL := 'EXPLAIN QUERY PLAN ' + SQL;

  Query := TFDQuery.Create(nil);
  try
    Query.Connection := FConnection;
    Query.SQL.Text := ExplainSQL;

    try
      Query.Open;
      ShowResults(Query);
      ShowMessage('��ѯ�ƻ����ɳɹ���');
    except
      on E: Exception do
      begin
        ShowError('���ɲ�ѯ�ƻ�ʧ��: ' + E.Message);
      end;
    end;
  finally
    if not (Query.Active and (FDataSource.DataSet = Query)) then
      Query.Free;
  end;
end;

procedure TSQLExecutor.ShowResults(AQuery: TFDQuery);
begin
  if Assigned(FDataSource.DataSet) and (FDataSource.DataSet <> AQuery) then
  begin
    FDataSource.DataSet.Free;
  end;

  FDataSource.DataSet := AQuery;
  FPageControl.ActivePageIndex := 0; // �л������ݽ��ҳ
end;

procedure TSQLExecutor.ShowError(const AErrorMsg: string);
begin
  FResultsMemo.Lines.Add('[����] ' + AErrorMsg);
  FPageControl.ActivePageIndex := 1; // �л�����Ϣҳ
  FStatusBar.SimpleText := '����: ' + AErrorMsg;
end;

procedure TSQLExecutor.ShowMessage(const AMessage: string);
begin
  FResultsMemo.Lines.Add('[��Ϣ] ' + AMessage);
  FStatusBar.SimpleText := AMessage;
end;

end.
