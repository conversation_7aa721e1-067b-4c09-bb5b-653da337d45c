unit ParadoxReader;

interface

uses
  System.SysUtils, System.Classes, Data.DB, System.Generics.Collections,
  System.Variants, System.Math;

type
  // Paradox ???????
  TParadoxFieldType = (
    pftAlpha,      // ?????
    pftDate,       // ????
    pftShort,      // ??????
    pftLong,       // ??????
    pftCurrency,   // ????
    pftNumber,     // ????
    pftLogical,    // ????
    pftMemoBLOB,   // ???/BLOB
    pftBLOB,       // BLOB
    pftFmtMemoBLOB,// ????????
    pftOLE,        // OLE????
    pftGraphic,    // ???
    pftTime,       // ???
    pftTimestamp,  // ????
    pftAutoInc,    // ????
    pftBytes,      // ???????
    pftBCD         // BCD????
  );

  // Paradox ???????
  TParadoxFieldDef = class
  private
    FName: string;
    FFieldType: TParadoxFieldType;
    FSize: Integer;
    FOffset: Integer;
  public
    constructor Create(const AName: string; AFieldType: TParadoxFieldType; ASize, AOffset: Integer);
    property Name: string read FName;
    property FieldType: TParadoxFieldType read FFieldType;
    property Size: Integer read FSize;
    property Offset: Integer read FOffset;
  end;

  // Paradox ???
  TParadoxTableRecord = class
  private
    FData: TBytes;
    FFieldDefs: TObjectList<TParadoxFieldDef>;
    function GetFieldValue(FieldIndex: Integer): Variant;
    procedure SetFieldValue(FieldIndex: Integer; const Value: Variant);
  public
    constructor Create(AData: TBytes; AFieldDefs: TObjectList<TParadoxFieldDef>);
    destructor Destroy; override;
    property FieldValues[FieldIndex: Integer]: Variant read GetFieldValue write SetFieldValue;
  end;

  // Paradox ???????
  TParadoxReader = class
  private
    FFileName: string;
    FFileStream: TFileStream;
    FFieldDefs: TObjectList<TParadoxFieldDef>;
    FRecordCount: Integer;
    FRecordSize: Integer;
    FHeaderSize: Integer;
    FTableVersionID: Byte;
    FModified: Boolean;

    procedure ReadHeader;
    function ReadRecordData(RecordIndex: Integer): TBytes;
    procedure WriteRecordData(RecordIndex: Integer; const Data: TBytes);
    function GetFieldCount: Integer;
    function GetFieldDef(Index: Integer): TParadoxFieldDef;
  public
    constructor Create(const AFileName: string);
    destructor Destroy; override;

    // ?????
    function GetFieldName(Index: Integer): string;
    function GetFieldType(Index: Integer): TParadoxFieldType;
    function GetFieldSize(Index: Integer): Integer;

    // ???????
    function GetRecordCount: Integer;
    function ReadRecord(RecordIndex: Integer): TParadoxTableRecord;
    procedure WriteRecord(RecordIndex: Integer; ARecord: TParadoxTableRecord);
    procedure SaveChanges;

    // ????
    property FieldCount: Integer read GetFieldCount;
    property FieldDefs[Index: Integer]: TParadoxFieldDef read GetFieldDef;
    property RecordCount: Integer read GetRecordCount;
    property Modified: Boolean read FModified;
  end;

  // ????
  EParadoxError = class(Exception);

implementation

{ TParadoxFieldDef }

constructor TParadoxFieldDef.Create(const AName: string; AFieldType: TParadoxFieldType; ASize, AOffset: Integer);
begin
  FName := AName;
  FFieldType := AFieldType;
  FSize := ASize;
  FOffset := AOffset;
end;

{ TParadoxTableRecord }

constructor TParadoxTableRecord.Create(AData: TBytes; AFieldDefs: TObjectList<TParadoxFieldDef>);
begin
  FData := AData;
  FFieldDefs := AFieldDefs;
end;

destructor TParadoxTableRecord.Destroy;
begin
  // ????? FFieldDefs??????????? TParadoxReader ?????
  inherited;
end;

function TParadoxTableRecord.GetFieldValue(FieldIndex: Integer): Variant;
var
  FieldDef: TParadoxFieldDef;
  Offset, Size: Integer;
  S: string;
  I: Integer;
  B: Boolean;
  D: TDateTime;
begin
  Result := Variants.Null;

  if (FieldIndex < 0) or (FieldIndex >= FFieldDefs.Count) then
    Exit;

  FieldDef := FFieldDefs[FieldIndex];
  Offset := FieldDef.Offset;
  Size := FieldDef.Size;

  // ????????????????
  if Offset + Size > Length(FData) then
    Exit;

  // ??????????????????
  case FieldDef.FieldType of
    pftAlpha:
      begin
        SetLength(S, Size);
        for I := 0 to Size - 1 do
        begin
          if FData[Offset + I] = 0 then
            Break;
          S[I+1] := Char(FData[Offset + I]);
        end;
        Result := Trim(S);
      end;

    pftShort:
      begin
        if Size >= 2 then
          Result := SmallInt(FData[Offset] or (FData[Offset + 1] shl 8));
      end;

    pftLong:
      begin
        if Size >= 4 then
          Result := Integer(FData[Offset] or (FData[Offset + 1] shl 8) or
                           (FData[Offset + 2] shl 16) or (FData[Offset + 3] shl 24));
      end;

    pftLogical:
      begin
        if Size >= 1 then
        begin
          B := FData[Offset] <> 0;
          Result := B;
        end;
      end;

    pftDate:
      begin
        if Size >= 4 then
        begin
          I := Integer(FData[Offset] or (FData[Offset + 1] shl 8) or
                      (FData[Offset + 2] shl 16) or (FData[Offset + 3] shl 24));
          try
            D := EncodeDate(1900 + (I div 10000), (I div 100) mod 100, I mod 100);
            Result := D;
          except
            Result := Variants.Null;
          end;
        end;
      end;

    // ??????????????????????????
  end;
end;

procedure TParadoxTableRecord.SetFieldValue(FieldIndex: Integer; const Value: Variant);
var
  FieldDef: TParadoxFieldDef;
  Offset, Size: Integer;
  S: string;
  I, IntValue: Integer;
  Year, Month, Day: Word;
begin
  if (FieldIndex < 0) or (FieldIndex >= FFieldDefs.Count) then
    Exit;

  FieldDef := FFieldDefs[FieldIndex];
  Offset := FieldDef.Offset;
  Size := FieldDef.Size;

  // ??????????????
  if Offset + Size > Length(FData) then
    Exit;

  // ???????????????????
  case FieldDef.FieldType of
    pftAlpha:
      begin
        if VarIsNull(Value) or VarIsClear(Value) then
        begin
          FillChar(FData[Offset], Size, 0);
        end
        else
        begin
          S := VarToStr(Value);
          FillChar(FData[Offset], Size, 0);
          for I := 1 to Min(Length(S), Size) do
            FData[Offset + I - 1] := Byte(S[I]);
        end;
      end;

    pftShort:
      begin
        if VarIsNull(Value) or VarIsClear(Value) then
        begin
          FillChar(FData[Offset], Size, 0);
        end
        else
        begin
          IntValue := Value;
          FData[Offset] := Byte(IntValue and $FF);
          FData[Offset + 1] := Byte((IntValue shr 8) and $FF);
        end;
      end;

    pftLong:
      begin
        if VarIsNull(Value) or VarIsClear(Value) then
        begin
          FillChar(FData[Offset], Size, 0);
        end
        else
        begin
          IntValue := Value;
          FData[Offset] := Byte(IntValue and $FF);
          FData[Offset + 1] := Byte((IntValue shr 8) and $FF);
          FData[Offset + 2] := Byte((IntValue shr 16) and $FF);
          FData[Offset + 3] := Byte((IntValue shr 24) and $FF);
        end;
      end;

    pftLogical:
      begin
        if VarIsNull(Value) or VarIsClear(Value) then
          FData[Offset] := 0
        else if Value then
          FData[Offset] := 1
        else
          FData[Offset] := 0;
      end;

    pftDate:
      begin
        if VarIsNull(Value) or VarIsEmpty(Value) then
        begin
          FillChar(FData[Offset], Size, 0);
        end
        else
        begin
          DecodeDate(VarToDateTime(Value), Year, Month, Day);
          IntValue := ((Year - 1900) * 10000) + (Month * 100) + Day;
          FData[Offset] := Byte(IntValue and $FF);
          FData[Offset + 1] := Byte((IntValue shr 8) and $FF);
          FData[Offset + 2] := Byte((IntValue shr 16) and $FF);
          FData[Offset + 3] := Byte((IntValue shr 24) and $FF);
        end;
      end;

    // ??????????????????????????
  end;
end;

{ TParadoxReader }

constructor TParadoxReader.Create(const AFileName: string);
begin
  FFileName := AFileName;
  FFieldDefs := TObjectList<TParadoxFieldDef>.Create(True);
  FModified := False;

  if not FileExists(FFileName) then
    raise EParadoxError.CreateFmt('Paradox?????????: %s', [FFileName]);

  try
    FFileStream := TFileStream.Create(FFileName, fmOpenReadWrite or fmShareDenyWrite);
    ReadHeader;
  except
    on E: Exception do
    begin
      FreeAndNil(FFileStream);
      raise EParadoxError.CreateFmt('?????Paradox???: %s', [E.Message]);
    end;
  end;
end;

destructor TParadoxReader.Destroy;
begin
  if FModified then
    SaveChanges;

  FFieldDefs.Free;
  FFileStream.Free;
  inherited;
end;

procedure TParadoxReader.ReadHeader;
var
  HeaderBuffer: TBytes;
  RecordSizeBytes: Word;
  FieldCount: Integer;
  I, Offset, FieldSize: Integer;
  FieldType: Byte;
  FieldName: string;
  ParadoxFieldType: TParadoxFieldType;
begin
  // ???????
  SetLength(HeaderBuffer, 2048); // ??????????????????
  FFileStream.Position := 0;
  FFileStream.Read(HeaderBuffer[0], Length(HeaderBuffer));

  // ????????
  FTableVersionID := HeaderBuffer[0]; // ?????ID

  // ??????? (2???)
  RecordSizeBytes := HeaderBuffer[2] or (HeaderBuffer[3] shl 8);
  FRecordSize := RecordSizeBytes;

  // ??????? (2???)
  FHeaderSize := HeaderBuffer[4] or (HeaderBuffer[5] shl 8);

  // ????? (4???)
  FRecordCount := HeaderBuffer[6] or (HeaderBuffer[7] shl 8) or
                 (HeaderBuffer[8] shl 16) or (HeaderBuffer[9] shl 24);

  // ????? (2???)
  FieldCount := HeaderBuffer[15];

  // ???????????
  Offset := 0;
  for I := 0 to FieldCount - 1 do
  begin
    // ??????? (1???)
    FieldType := HeaderBuffer[21 + I];

    // ??????? (???????????)
    case FieldType of
      1: begin FieldSize := HeaderBuffer[47 + I]; ParadoxFieldType := pftAlpha; end;     // Alpha
      2: begin FieldSize := 4; ParadoxFieldType := pftDate; end;                         // Date
      3: begin FieldSize := 2; ParadoxFieldType := pftShort; end;                        // Short
      4: begin FieldSize := 4; ParadoxFieldType := pftLong; end;                         // Long
      5: begin FieldSize := 8; ParadoxFieldType := pftCurrency; end;                     // Currency
      6: begin FieldSize := 8; ParadoxFieldType := pftNumber; end;                       // Number
      7: begin FieldSize := 1; ParadoxFieldType := pftLogical; end;                      // Logical
      8: begin FieldSize := 8; ParadoxFieldType := pftMemoBLOB; end;                     // MemoBLOB
      9: begin FieldSize := 8; ParadoxFieldType := pftBLOB; end;                         // BLOB
      10: begin FieldSize := 8; ParadoxFieldType := pftFmtMemoBLOB; end;                 // FmtMemoBLOB
      11: begin FieldSize := 8; ParadoxFieldType := pftOLE; end;                         // OLE
      12: begin FieldSize := 8; ParadoxFieldType := pftGraphic; end;                     // Graphic
      14: begin FieldSize := 4; ParadoxFieldType := pftTime; end;                        // Time
      15: begin FieldSize := 8; ParadoxFieldType := pftTimestamp; end;                   // Timestamp
      16: begin FieldSize := 4; ParadoxFieldType := pftAutoInc; end;                     // AutoInc
      17: begin FieldSize := HeaderBuffer[47 + I]; ParadoxFieldType := pftBytes; end;    // Bytes
      18: begin FieldSize := 8; ParadoxFieldType := pftBCD; end;                         // BCD
      else
        begin FieldSize := 1; ParadoxFieldType := pftAlpha; end;                         // ???????
    end;

    // ????? (???????261????????????????261???)
    FieldName := '';
    for I := 0 to 25 do // ???26?????
    begin
      if HeaderBuffer[261 + (I * FieldCount) + I] = 0 then
        Break;
      FieldName := FieldName + Char(HeaderBuffer[261 + (I * FieldCount) + I]);
    end;

    // ???????????
    FFieldDefs.Add(TParadoxFieldDef.Create(FieldName, ParadoxFieldType, FieldSize, Offset));

    // ???????????????????
    Inc(Offset, FieldSize);
  end;
end;

function TParadoxReader.ReadRecordData(RecordIndex: Integer): TBytes;
var
  Data: TBytes;
begin
  if (RecordIndex < 0) or (RecordIndex >= FRecordCount) then
    raise EParadoxError.CreateFmt('???????????????: %d', [RecordIndex]);

  SetLength(Data, FRecordSize);
  FFileStream.Position := FHeaderSize + (RecordIndex * FRecordSize);
  FFileStream.Read(Data[0], FRecordSize);
  Result := Data;
end;

procedure TParadoxReader.WriteRecordData(RecordIndex: Integer; const Data: TBytes);
begin
  if (RecordIndex < 0) or (RecordIndex >= FRecordCount) then
    raise EParadoxError.CreateFmt('???????????????: %d', [RecordIndex]);

  if Length(Data) <> FRecordSize then
    raise EParadoxError.Create('???????????????');

  FFileStream.Position := FHeaderSize + (RecordIndex * FRecordSize);
  FFileStream.Write(Data[0], FRecordSize);
  FModified := True;
end;

function TParadoxReader.GetFieldCount: Integer;
begin
  Result := FFieldDefs.Count;
end;

function TParadoxReader.GetFieldDef(Index: Integer): TParadoxFieldDef;
begin
  if (Index < 0) or (Index >= FFieldDefs.Count) then
    raise EParadoxError.CreateFmt('???????????????: %d', [Index]);

  Result := FFieldDefs[Index];
end;

function TParadoxReader.GetFieldName(Index: Integer): string;
begin
  Result := GetFieldDef(Index).Name;
end;

function TParadoxReader.GetFieldType(Index: Integer): TParadoxFieldType;
begin
  Result := GetFieldDef(Index).FieldType;
end;

function TParadoxReader.GetFieldSize(Index: Integer): Integer;
begin
  Result := GetFieldDef(Index).Size;
end;

function TParadoxReader.GetRecordCount: Integer;
begin
  Result := FRecordCount;
end;

function TParadoxReader.ReadRecord(RecordIndex: Integer): TParadoxTableRecord;
var
  Data: TBytes;
begin
  Data := ReadRecordData(RecordIndex);
  Result := TParadoxTableRecord.Create(Data, FFieldDefs);
end;

procedure TParadoxReader.WriteRecord(RecordIndex: Integer; ARecord: TParadoxTableRecord);
begin
  WriteRecordData(RecordIndex, ARecord.FData);
end;

procedure TParadoxReader.SaveChanges;
begin
  if FModified then
  begin
    FFileStream.Flush;
    FModified := False;
  end;
end;

end.
