procedure TDBConnectionManager.LoadIndices;
var
  Tables, Indices: TStringList;
  i, j: Integer;
  IndicesNode, TableNode, IndexNode: TTreeNode;
begin
  // 检查连接是否有效
  if not Assigned(FConnection) then
    Exit;

  if not FConnection.Connected then
    Exit;

  // 检查树视图是否有效
  if not Assigned(FTreeView) then
    Exit;

  // 查找索引节点
  IndicesNode := nil;
  for i := 0 to FTreeView.Items.Count - 1 do
  begin
    if (FTreeView.Items[i].Level = 1) and (FTreeView.Items[i].Text = '索引') then
    begin
      IndicesNode := FTreeView.Items[i];
      Break;
    end;
  end;

  if not Assigned(IndicesNode) then
    Exit;

  // 清空索引节点下的所有子节点
  while IndicesNode.Count > 0 do
    IndicesNode.Item[0].Delete;

  // 获取表名列表
  Tables := nil;
  try
    if IsSQLite then
    begin
      // 使用SQLite查询获取表名
      try
        with ExecuteQuery('SELECT name FROM sqlite_master WHERE type=''table'' ORDER BY name') do
        begin
          Tables := TStringList.Create;
          while not Eof do
          begin
            Tables.Add(FieldByName('name').AsString);
            Next;
          end;
          Free;
        end;
      except
        // 忽略错误
      end;
    end
    else if IsAccess then
    begin
      // 使用Access方法获取表名
      Tables := GetAccessTableNames;
    end
    else if IsParadox then
    begin
      // 使用Paradox方法获取表名
      Tables := GetParadoxTableNames;
    end;

    // 遍历表，获取每个表的索引
    if Assigned(Tables) and (Tables.Count > 0) then
    begin
      for i := 0 to Tables.Count - 1 do
      begin
        Indices := nil;
        try
          if IsSQLite then
          begin
            // 使用SQLite查询获取索引名
            try
              with ExecuteQuery(Format('SELECT name FROM sqlite_master WHERE type=''index'' AND tbl_name=''%s'' ORDER BY name', [Tables[i]])) do
              begin
                Indices := TStringList.Create;
                while not Eof do
                begin
                  Indices.Add(FieldByName('name').AsString);
                  Next;
                end;
                Free;
              end;
            except
              // 忽略错误
            end;
          end
          else if IsAccess then
          begin
            // 使用Access方法获取索引名
            Indices := GetAccessIndexNames(Tables[i]);
          end
          else if IsParadox then
          begin
            // 使用Paradox方法获取索引名
            Indices := GetParadoxIndexNames(Tables[i]);
          end;

          // 添加表节点和索引节点
          if Assigned(Indices) and (Indices.Count > 0) then
          begin
            TableNode := FTreeView.Items.AddChild(IndicesNode, Tables[i]);
            TableNode.ImageIndex := 1; // 表图标索引
            TableNode.SelectedIndex := 1;

            for j := 0 to Indices.Count - 1 do
            begin
              IndexNode := FTreeView.Items.AddChild(TableNode, Indices[j]);
              IndexNode.ImageIndex := 3; // 索引图标索引
              IndexNode.SelectedIndex := 3;
            end;
          end;
        finally
          if Assigned(Indices) then
            Indices.Free;
        end;
      end;

      // 展开索引节点
      IndicesNode.Expand(False);
    end;
  finally
    if Assigned(Tables) then
      Tables.Free;
  end;
end;

procedure TDBConnectionManager.LoadTriggers;
var
  i: Integer;
  TriggersNode, TriggerNode: TTreeNode;
begin
  // 检查连接是否有效
  if not Assigned(FConnection) then
    Exit;

  if not FConnection.Connected then
    Exit;

  // 检查树视图是否有效
  if not Assigned(FTreeView) then
    Exit;

  // 查找触发器节点
  TriggersNode := nil;
  for i := 0 to FTreeView.Items.Count - 1 do
  begin
    if (FTreeView.Items[i].Level = 1) and (FTreeView.Items[i].Text = '触发器') then
    begin
      TriggersNode := FTreeView.Items[i];
      Break;
    end;
  end;

  if not Assigned(TriggersNode) then
    Exit;

  // 清空触发器节点下的所有子节点
  while TriggersNode.Count > 0 do
    TriggersNode.Item[0].Delete;

  // 只有SQLite支持触发器
  if IsSQLite then
  begin
    // 使用SQLite查询获取触发器名
    try
      with ExecuteQuery('SELECT name FROM sqlite_master WHERE type=''trigger'' ORDER BY name') do
      begin
        while not Eof do
        begin
          TriggerNode := FTreeView.Items.AddChild(TriggersNode, FieldByName('name').AsString);
          TriggerNode.ImageIndex := 4; // 触发器图标索引
          TriggerNode.SelectedIndex := 4;
          Next;
        end;
        Free;
      end;

      // 展开触发器节点
      if TriggersNode.Count > 0 then
        TriggersNode.Expand(False);
    except
      // 忽略错误
    end;
  end;
end;
