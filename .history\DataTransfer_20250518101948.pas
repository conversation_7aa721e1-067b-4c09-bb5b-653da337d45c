unit DataTransfer;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  System.StrUtils, System.Math,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, Vcl.ComCtrls,
  Vcl.StdCtrls, Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Error, FireDAC.UI.Intf, FireDAC.Phys.Intf, FireDAC.Stan.Def,
  FireDAC.Stan.Pool, FireDAC.Stan.Async, FireDAC.Phys, FireDAC.Phys.SQLite,
  FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs, FireDAC.VCLUI.Wait,
  FireDAC.Comp.Client, FireDAC.Comp.DataSet, System.Actions, Vcl.ActnList,
  Vcl.ToolWin, Vcl.But<PERSON>, Vcl.Menus;

type
  TfrmDataTransfer = class(TForm)
    pnlMain: TPanel;
    pcMain: TPageControl;
    tsExport: TTabSheet;
    tsImport: TTabSheet;
    pnlExportTop: TPanel;
    lblExportTable: TLabel;
    cmbExportTable: TComboBox;
    lblExportFormat: TLabel;
    cmbExportFormat: TComboBox;
    lblExportFile: TLabel;
    edtExportFile: TEdit;
    btnBrowseExport: TButton;
    pnlExportOptions: TPanel;
    chkExportHeaders: TCheckBox;
    chkExportQuotes: TCheckBox;
    lblExportDelimiter: TLabel;
    cmbExportDelimiter: TComboBox;
    pnlExportBottom: TPanel;
    btnExport: TButton;
    pnlImportTop: TPanel;
    lblImportTable: TLabel;
    cmbImportTable: TComboBox;
    lblImportFormat: TLabel;
    cmbImportFormat: TComboBox;
    lblImportFile: TLabel;
    edtImportFile: TEdit;
    btnBrowseImport: TButton;
    pnlImportOptions: TPanel;
    chkImportHeaders: TCheckBox;
    lblImportDelimiter: TLabel;
    cmbImportDelimiter: TComboBox;
    pnlImportBottom: TPanel;
    btnImport: TButton;
    chkCreateTable: TCheckBox;
    edtNewTable: TEdit;
    alMain: TActionList;
    actExport: TAction;
    actImport: TAction;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure actExportExecute(Sender: TObject);
    procedure actImportExecute(Sender: TObject);
    procedure btnBrowseExportClick(Sender: TObject);
    procedure btnBrowseImportClick(Sender: TObject);
    procedure cmbExportFormatChange(Sender: TObject);
    procedure cmbImportFormatChange(Sender: TObject);
    procedure chkCreateTableClick(Sender: TObject);
  private
    { Private declarations }
    FConnection: TFDConnection;

    procedure InitializeControls;
    procedure LoadTables;
    procedure ExportToCSV(const ATableName, AFileName: string;
      AIncludeHeaders, AQuoteStrings: Boolean; const ADelimiter: string);
    procedure ExportToSQL(const ATableName, AFileName: string);
    procedure ExportToJSON(const ATableName, AFileName: string);
    procedure ImportFromCSV(const ATableName, AFileName: string;
      AHasHeaders: Boolean; const ADelimiter: string; ACreateTable: Boolean;
      const ANewTableName: string);
  public
    { Public declarations }
    procedure Initialize(AConnection: TFDConnection);
  end;

var
  frmDataTransfer: TfrmDataTransfer;

implementation

{$R *.dfm}

procedure TfrmDataTransfer.FormCreate(Sender: TObject);
begin
  InitializeControls;
end;

procedure TfrmDataTransfer.FormDestroy(Sender: TObject);
begin
  // ������Դ
end;

procedure TfrmDataTransfer.InitializeControls;
begin
  // ��ʼ��������ʽ������
  cmbExportFormat.Items.Clear;
  cmbExportFormat.Items.Add('CSV');
  cmbExportFormat.Items.Add('SQL');
  cmbExportFormat.Items.Add('JSON');
  cmbExportFormat.ItemIndex := 0;

  // ��ʼ�������ʽ������
  cmbImportFormat.Items.Clear;
  cmbImportFormat.Items.Add('CSV');
  cmbImportFormat.ItemIndex := 0;

  // ��ʼ���ָ���������
  cmbExportDelimiter.Items.Clear;
  cmbExportDelimiter.Items.Add(',');
  cmbExportDelimiter.Items.Add(';');
  cmbExportDelimiter.Items.Add('|');
  cmbExportDelimiter.Items.Add('Tab');
  cmbExportDelimiter.ItemIndex := 0;

  cmbImportDelimiter.Items.Clear;
  cmbImportDelimiter.Items.Add(',');
  cmbImportDelimiter.Items.Add(';');
  cmbImportDelimiter.Items.Add('|');
  cmbImportDelimiter.Items.Add('Tab');
  cmbImportDelimiter.ItemIndex := 0;

  // ��ʼ��ѡ��
  chkExportHeaders.Checked := True;
  chkExportQuotes.Checked := True;
  chkImportHeaders.Checked := True;

  edtNewTable.Enabled := False;
end;

procedure TfrmDataTransfer.Initialize(AConnection: TFDConnection);
begin
  FConnection := AConnection;

  LoadTables;
end;

procedure TfrmDataTransfer.LoadTables;
var
  Query: TFDQuery;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  cmbExportTable.Items.Clear;
  cmbImportTable.Items.Clear;

  Query := TFDQuery.Create(nil);
  try
    Query.Connection := FConnection;
    Query.SQL.Text := 'SELECT name FROM sqlite_master WHERE type=''table'' AND name NOT LIKE ''sqlite_%'' ORDER BY name';

    try
      Query.Open;

      while not Query.Eof do
      begin
        cmbExportTable.Items.Add(Query.FieldByName('name').AsString);
        cmbImportTable.Items.Add(Query.FieldByName('name').AsString);
        Query.Next;
      end;

      if cmbExportTable.Items.Count > 0 then
      begin
        cmbExportTable.ItemIndex := 0;
        cmbImportTable.ItemIndex := 0;
      end;
    except
      on E: Exception do
      begin
        ShowMessage('���ر��б�ʧ��: ' + E.Message);
      end;
    end;
  finally
    Query.Free;
  end;
end;

procedure TfrmDataTransfer.btnBrowseExportClick(Sender: TObject);
var
  SaveDialog: TSaveDialog;
  FileExt: string;
begin
  SaveDialog := TSaveDialog.Create(Self);
  try
    case cmbExportFormat.ItemIndex of
      0: begin // CSV
        SaveDialog.Filter := 'CSV�ļ� (*.csv)|*.csv|�����ļ� (*.*)|*.*';
        FileExt := '.csv';
      end;
      1: begin // SQL
        SaveDialog.Filter := 'SQL�ļ� (*.sql)|*.sql|�����ļ� (*.*)|*.*';
        FileExt := '.sql';
      end;
      2: begin // JSON
        SaveDialog.Filter := 'JSON�ļ� (*.json)|*.json|�����ļ� (*.*)|*.*';
        FileExt := '.json';
      end;
    end;

    SaveDialog.DefaultExt := FileExt;
    SaveDialog.Title := '���浼���ļ�';

    if SaveDialog.Execute then
    begin
      edtExportFile.Text := SaveDialog.FileName;
    end;
  finally
    SaveDialog.Free;
  end;
end;

procedure TfrmDataTransfer.btnBrowseImportClick(Sender: TObject);
var
  OpenDialog: TOpenDialog;
begin
  OpenDialog := TOpenDialog.Create(Self);
  try
    case cmbImportFormat.ItemIndex of
      0: begin // CSV
        OpenDialog.Filter := 'CSV�ļ� (*.csv)|*.csv|�����ļ� (*.*)|*.*';
      end;
    end;

    OpenDialog.Title := 'ѡ�����ļ�';

    if OpenDialog.Execute then
    begin
      edtImportFile.Text := OpenDialog.FileName;
    end;
  finally
    OpenDialog.Free;
  end;
end;

procedure TfrmDataTransfer.cmbExportFormatChange(Sender: TObject);
begin
  case cmbExportFormat.ItemIndex of
    0: begin // CSV
      chkExportHeaders.Enabled := True;
      chkExportQuotes.Enabled := True;
      lblExportDelimiter.Enabled := True;
      cmbExportDelimiter.Enabled := True;
    end;
    1, 2: begin // SQL, JSON
      chkExportHeaders.Enabled := False;
      chkExportQuotes.Enabled := False;
      lblExportDelimiter.Enabled := False;
      cmbExportDelimiter.Enabled := False;
    end;
  end;
end;

procedure TfrmDataTransfer.cmbImportFormatChange(Sender: TObject);
begin
  // Ŀǰֻ֧��CSV���룬���Բ���Ҫ����
end;

procedure TfrmDataTransfer.chkCreateTableClick(Sender: TObject);
begin
  edtNewTable.Enabled := chkCreateTable.Checked;
  cmbImportTable.Enabled := not chkCreateTable.Checked;
end;

procedure TfrmDataTransfer.actExportExecute(Sender: TObject);
var
  TableName, FileName, Delimiter: string;
  IncludeHeaders, QuoteStrings: Boolean;
begin
  TableName := cmbExportTable.Text;
  FileName := edtExportFile.Text;

  if TableName = '' then
  begin
    ShowMessage('��ѡ��Ҫ�����ı�');
    Exit;
  end;

  if FileName = '' then
  begin
    ShowMessage('��ѡ�񵼳��ļ�');
    Exit;
  end;

  IncludeHeaders := chkExportHeaders.Checked;
  QuoteStrings := chkExportQuotes.Checked;

  if cmbExportDelimiter.Text = 'Tab' then
    Delimiter := #9
  else
    Delimiter := cmbExportDelimiter.Text;

  case cmbExportFormat.ItemIndex of
    0: ExportToCSV(TableName, FileName, IncludeHeaders, QuoteStrings, Delimiter);
    1: ExportToSQL(TableName, FileName);
    2: ExportToJSON(TableName, FileName);
  end;
end;

procedure TfrmDataTransfer.ExportToCSV(const ATableName, AFileName: string;
  AIncludeHeaders, AQuoteStrings: Boolean; const ADelimiter: string);
var
  Query: TFDQuery;
  CSV: TStringList;
  i: Integer;
  Line, Value: string;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  Query := TFDQuery.Create(nil);
  CSV := TStringList.Create;
  try
    Query.Connection := FConnection;
    Query.SQL.Text := Format('SELECT * FROM %s', [ATableName]);

    try
      Query.Open;

      // ���ӱ�ͷ
      if AIncludeHeaders then
      begin
        Line := '';
        for i := 0 to Query.FieldCount - 1 do
        begin
          if Line <> '' then
            Line := Line + ADelimiter;

          if AQuoteStrings then
            Line := Line + '"' + Query.Fields[i].FieldName + '"'
          else
            Line := Line + Query.Fields[i].FieldName;
        end;
        CSV.Add(Line);
      end;

      // ����������
      while not Query.Eof do
      begin
        Line := '';
        for i := 0 to Query.FieldCount - 1 do
        begin
          if Line <> '' then
            Line := Line + ADelimiter;

          Value := Query.Fields[i].AsString;

          if AQuoteStrings then
          begin
            // �����ַ����е�˫����
            Value := StringReplace(Value, '"', '""', [rfReplaceAll]);
            Line := Line + '"' + Value + '"';
          end
          else
            Line := Line + Value;
        end;
        CSV.Add(Line);
        Query.Next;
      end;

      CSV.SaveToFile(AFileName);
      ShowMessage('������CSV�ɹ�');
    except
      on E: Exception do
      begin
        ShowMessage('������CSVʧ��: ' + E.Message);
      end;
    end;
  finally
    Query.Free;
    CSV.Free;
  end;
end;

procedure TfrmDataTransfer.ExportToSQL(const ATableName, AFileName: string);
var
  Query, SchemaQuery: TFDQuery;
  SQL: TStringList;
  i: Integer;
  InsertSQL, Values, Value: string;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  Query := TFDQuery.Create(nil);
  SchemaQuery := TFDQuery.Create(nil);
  SQL := TStringList.Create;
  try
    Query.Connection := FConnection;
    SchemaQuery.Connection := FConnection;

    // ��ȡ���ṹ
    SchemaQuery.SQL.Text := Format('PRAGMA table_info(%s)', [ATableName]);
    SchemaQuery.Open;

    SQL.Add('-- ���ṹ');
    SQL.Add(Format('CREATE TABLE IF NOT EXISTS %s (', [ATableName]));

    while not SchemaQuery.Eof do
    begin
      SQL.Add(Format('  %s %s%s%s%s', [
        SchemaQuery.FieldByName('name').AsString,
        SchemaQuery.FieldByName('type').AsString,
        IfThen(SchemaQuery.FieldByName('notnull').AsInteger = 1, ' NOT NULL', ''),
        IfThen(SchemaQuery.FieldByName('dflt_value').AsString <> '', ' DEFAULT ' + SchemaQuery.FieldByName('dflt_value').AsString, ''),
        IfThen(SchemaQuery.FieldByName('pk').AsInteger = 1, ' PRIMARY KEY', '')
      ]) + IfThen(SchemaQuery.RecNo < SchemaQuery.RecordCount, ',', ''));

      SchemaQuery.Next;
    end;

    SQL.Add(');');
    SQL.Add('');

    // ��ȡ����
    Query.SQL.Text := Format('SELECT * FROM %s', [ATableName]);
    Query.Open;

    SQL.Add('-- ������');
    SQL.Add('BEGIN TRANSACTION;');

    while not Query.Eof do
    begin
      InsertSQL := Format('INSERT INTO %s (', [ATableName]);
      Values := '';

      for i := 0 to Query.FieldCount - 1 do
      begin
        if i > 0 then
        begin
          InsertSQL := InsertSQL + ', ';
          Values := Values + ', ';
        end;

        InsertSQL := InsertSQL + Query.Fields[i].FieldName;

        if Query.Fields[i].IsNull then
          Values := Values + 'NULL'
        else
        begin
          case Query.Fields[i].DataType of
            ftString, ftWideString, ftMemo, ftWideMemo, ftFixedChar, ftFixedWideChar:
              begin
                Value := StringReplace(Query.Fields[i].AsString, '''', '''''', [rfReplaceAll]);
                Values := Values + '''' + Value + '''';
              end;
            ftDateTime, ftDate, ftTime:
              Values := Values + '''' + Query.Fields[i].AsString + '''';
            else
              Values := Values + Query.Fields[i].AsString;
          end;
        end;
      end;

      InsertSQL := InsertSQL + ') VALUES (' + Values + ');';
      SQL.Add(InsertSQL);

      Query.Next;
    end;

    SQL.Add('COMMIT;');

    SQL.SaveToFile(AFileName);
    ShowMessage('������SQL�ɹ�');
  finally
    Query.Free;
    SchemaQuery.Free;
    SQL.Free;
  end;
end;

procedure TfrmDataTransfer.ExportToJSON(const ATableName, AFileName: string);
var
  Query: TFDQuery;
  JSON: TStringList;
  i: Integer;
  RowJSON, FieldJSON: string;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  Query := TFDQuery.Create(nil);
  JSON := TStringList.Create;
  try
    Query.Connection := FConnection;
    Query.SQL.Text := Format('SELECT * FROM %s', [ATableName]);

    try
      Query.Open;

      JSON.Add('[');

      while not Query.Eof do
      begin
        RowJSON := '  {';

        for i := 0 to Query.FieldCount - 1 do
        begin
          if i > 0 then
            RowJSON := RowJSON + ',';

          RowJSON := RowJSON + Format('%s"%s": ', [#13#10 + '    ', Query.Fields[i].FieldName]);

          if Query.Fields[i].IsNull then
            RowJSON := RowJSON + 'null'
          else
          begin
            case Query.Fields[i].DataType of
              ftString, ftWideString, ftMemo, ftWideMemo, ftFixedChar, ftFixedWideChar,
              ftDateTime, ftDate, ftTime:
                begin
                  FieldJSON := StringReplace(Query.Fields[i].AsString, '\', '\\', [rfReplaceAll]);
                  FieldJSON := StringReplace(FieldJSON, '"', '\"', [rfReplaceAll]);
                  FieldJSON := StringReplace(FieldJSON, #13, '\r', [rfReplaceAll]);
                  FieldJSON := StringReplace(FieldJSON, #10, '\n', [rfReplaceAll]);
                  RowJSON := RowJSON + '"' + FieldJSON + '"';
                end;
              ftBoolean:
                RowJSON := RowJSON + LowerCase(Query.Fields[i].AsString);
              else
                RowJSON := RowJSON + Query.Fields[i].AsString;
            end;
          end;
        end;

        RowJSON := RowJSON + #13#10 + '  }';

        if not Query.Eof then
          RowJSON := RowJSON + ',';

        JSON.Add(RowJSON);
        Query.Next;
      end;

      JSON.Add(']');

      JSON.SaveToFile(AFileName);
      ShowMessage('������JSON�ɹ�');
    except
      on E: Exception do
      begin
        ShowMessage('������JSONʧ��: ' + E.Message);
      end;
    end;
  finally
    Query.Free;
    JSON.Free;
  end;
end;

procedure TfrmDataTransfer.actImportExecute(Sender: TObject);
var
  TableName, FileName, Delimiter, NewTableName: string;
  HasHeaders, CreateTable: Boolean;
begin
  FileName := edtImportFile.Text;
  CreateTable := chkCreateTable.Checked;

  if CreateTable then
  begin
    NewTableName := edtNewTable.Text;
    if NewTableName = '' then
    begin
      ShowMessage('�������±���');
      Exit;
    end;
    TableName := NewTableName;
  end
  else
  begin
    TableName := cmbImportTable.Text;
    if TableName = '' then
    begin
      ShowMessage('��ѡ��Ҫ����ı�');
      Exit;
    end;
  end;

  if FileName = '' then
  begin
    ShowMessage('��ѡ�����ļ�');
    Exit;
  end;

  HasHeaders := chkImportHeaders.Checked;

  if cmbImportDelimiter.Text = 'Tab' then
    Delimiter := #9
  else
    Delimiter := cmbImportDelimiter.Text;

  case cmbImportFormat.ItemIndex of
    0: ImportFromCSV(TableName, FileName, HasHeaders, Delimiter, CreateTable, NewTableName);
  end;
end;

procedure TfrmDataTransfer.ImportFromCSV(const ATableName, AFileName: string;
  AHasHeaders: Boolean; const ADelimiter: string; ACreateTable: Boolean;
  const ANewTableName: string);
var
  CSV: TStringList;
  i, j, StartRow: Integer;
  Headers: TStringList;
  Query: TFDQuery;
  SQL, InsertSQL, Values, Value: string;
  Fields: TArray<string>;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  CSV := TStringList.Create;
  Headers := TStringList.Create;
  Query := TFDQuery.Create(nil);
  try
    Query.Connection := FConnection;

    try
      CSV.LoadFromFile(AFileName);

      if CSV.Count = 0 then
      begin
        ShowMessage('CSV�ļ�Ϊ��');
        Exit;
      end;

      // ������ͷ
      if AHasHeaders then
      begin
        Fields := CSV[0].Split([ADelimiter]);
        for i := 0 to Length(Fields) - 1 do
        begin
          Value := Fields[i];
          // �Ƴ�����
          if (Value.StartsWith('"')) and (Value.EndsWith('"')) then
            Value := Value.Substring(1, Value.Length - 2);
          Headers.Add(Value);
        end;
        StartRow := 1;
      end
      else
      begin
        // ���û�б�ͷ��ʹ����������Ϊ�ֶ���
        Fields := CSV[0].Split([ADelimiter]);
        for i := 0 to Length(Fields) - 1 do
          Headers.Add('Field' + IntToStr(i + 1));
        StartRow := 0;
      end;

      // �����Ҫ������
      if ACreateTable then
      begin
        SQL := Format('CREATE TABLE %s (', [ANewTableName]);

        for i := 0 to Headers.Count - 1 do
        begin
          if i > 0 then
            SQL := SQL + ', ';
          SQL := SQL + Headers[i] + ' TEXT';
        end;

        SQL := SQL + ')';

        Query.SQL.Text := SQL;
        Query.ExecSQL;
      end;

      // ��������
      FConnection.StartTransaction;
      try
        for i := StartRow to CSV.Count - 1 do
        begin
          Fields := CSV[i].Split([ADelimiter]);

          InsertSQL := Format('INSERT INTO %s (', [ATableName]);
          Values := '';

          for j := 0 to Min(Headers.Count - 1, Length(Fields) - 1) do
          begin
            if j > 0 then
            begin
              InsertSQL := InsertSQL + ', ';
              Values := Values + ', ';
            end;

            InsertSQL := InsertSQL + Headers[j];

            Value := Fields[j];
            // �Ƴ�����
            if (Value.StartsWith('"')) and (Value.EndsWith('"')) then
              Value := Value.Substring(1, Value.Length - 2);
            // ����˫����
            Value := StringReplace(Value, '""', '"', [rfReplaceAll]);

            Values := Values + '''' + StringReplace(Value, '''', '''''', [rfReplaceAll]) + '''';
          end;

          InsertSQL := InsertSQL + ') VALUES (' + Values + ')';

          Query.SQL.Text := InsertSQL;
          Query.ExecSQL;
        end;

        FConnection.Commit;
        ShowMessage('����CSV�ɹ�');
      except
        on E: Exception do
        begin
          FConnection.Rollback;
          ShowMessage('����CSVʧ��: ' + E.Message);
        end;
      end;
    except
      on E: Exception do
      begin
        ShowMessage('����CSVʧ��: ' + E.Message);
      end;
    end;
  finally
    Query.Free;
    Headers.Free;
    CSV.Free;
  end;
end;

end.
