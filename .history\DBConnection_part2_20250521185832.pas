function TDBConnectionManager.GetAccessViewNames: TStringList;
var
  Query: TFDQuery;
  Views: TStringList;
  Catalog: OleVariant;
  ViewList: OleVariant;
  i: Integer;
  ADOConnection: TADOConnection;
  ConnectionString: string;
  FileExt: string;
begin
  Views := TStringList.Create;
  Result := Views; // 初始化返回值

  try
    // 检查连接是否有效
    if not Assigned(FConnection) then
      Exit;

    if not FConnection.Connected then
      Exit;

    // 方法1：使用ADO Catalog对象获取视图名
    try
      // 获取数据库文件路径
      FileExt := LowerCase(ExtractFileExt(FConnection.Params.Values['Database']));

      // 创建ADO连接
      ADOConnection := TADOConnection.Create(nil);
      try
        // 设置ADO连接字符串
        if FileExt = '.mdb' then
          ConnectionString := 'Provider=Microsoft.Jet.OLEDB.4.0;Data Source=' + FConnection.Params.Values['Database'] + ';Persist Security Info=False;'
        else // .accdb
          ConnectionString := 'Provider=Microsoft.ACE.OLEDB.12.0;Data Source=' + FConnection.Params.Values['Database'] + ';Persist Security Info=False;';

        ADOConnection.ConnectionString := ConnectionString;
        ADOConnection.LoginPrompt := False;
        ADOConnection.Open;

        // 获取Catalog对象
        Catalog := CreateOleObject('ADOX.Catalog');
        Catalog.ActiveConnection := ADOConnection.ConnectionObject;

        // 获取视图列表
        ViewList := Catalog.Views;

        // 遍历视图列表
        for i := 0 to ViewList.Count - 1 do
        begin
          Views.Add(ViewList.Item[i].Name);
        end;

        // 如果成功获取视图名，直接返回
        if Views.Count > 0 then
        begin
          Result := Views;
          Exit;
        end;
      finally
        if Assigned(ADOConnection) then
        begin
          if ADOConnection.Connected then
            ADOConnection.Close;
          ADOConnection.Free;
        end;

        // 释放COM对象
        Catalog := Unassigned;
        ViewList := Unassigned;
      end;
    except
      // 忽略错误，尝试下一种方法
    end;

    // 方法2：使用直接SQL查询
    Query := TFDQuery.Create(nil);
    try
      Query.Connection := FConnection;

      // 尝试方法2.1：使用标准Access系统表查询
      try
        Query.SQL.Text := 'SELECT MSysObjects.Name FROM MSysObjects ' +
                          'WHERE (((MSysObjects.Type)=5) AND ((MSysObjects.Flags)=0))';
        Query.Open;

        while not Query.Eof do
        begin
          if Query.FieldByName('Name').AsString <> '' then
            Views.Add(Query.FieldByName('Name').AsString);
          Query.Next;
        end;

        // 如果成功获取视图名，直接返回
        if Views.Count > 0 then
        begin
          Result := Views;
          Exit;
        end;
      except
        // 忽略错误，尝试下一种方法
      end;

      // 尝试方法2.2：使用INFORMATION_SCHEMA
      try
        Query.Close;
        Query.SQL.Text := 'SELECT TABLE_NAME FROM INFORMATION_SCHEMA.VIEWS';
        Query.Open;

        while not Query.Eof do
        begin
          if Query.FieldByName('TABLE_NAME').AsString <> '' then
            Views.Add(Query.FieldByName('TABLE_NAME').AsString);
          Query.Next;
        end;
      except
        // 忽略错误
      end;
    finally
      Query.Free;
    end;
  except
    // 忽略所有错误，返回当前收集到的视图名
  end;

  Result := Views;
end;
