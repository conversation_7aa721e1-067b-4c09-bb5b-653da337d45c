procedure TfrmConnectionDialog.btnConnectClick(Sender: TObject);
begin
  if Trim(edtGameDir.Text) = '' then
  begin
    ShowMessage('请选择游戏根目录');
    Exit;
  end;

  FGameDir := edtGameDir.Text;

  // 尝试自动检测数据库
  if AutoDetectDatabase(FGameDir) then
  begin
    ModalResult := mrOk;
  end
  else
  begin
    ShowMessage('在选择目录中未找到有效的数据库配置，请选择其他目录。');
  end;
end;

procedure TfrmConnectionDialog.btnCancelClick(Sender: TObject);
begin
  ModalResult := mrCancel;
end;

function TfrmConnectionDialog.Execute: Boolean;
begin
  Result := (ShowModal = mrOk);
end;

end.
