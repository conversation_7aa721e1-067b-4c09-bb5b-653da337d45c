object frmMain: TfrmMain
  Left = 0
  Top = 0
  Caption = 'SQLite数据库工具'
  ClientHeight = 600
  ClientWidth = 900
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Menu = mmMain
  Position = poScreenCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  TextHeight = 15
  object pnlMain: TPanel
    Left = 0
    Top = 26
    Width = 900
    Height = 555
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    object splVertical: TSplitter
      Left = 250
      Top = 0
      Height = 555
      ExplicitLeft = 200
      ExplicitTop = 232
      ExplicitHeight = 100
    end
    object pnlLeft: TPanel
      Left = 0
      Top = 0
      Width = 250
      Height = 555
      Align = alLeft
      BevelOuter = bvNone
      TabOrder = 0
      object tvDatabases: TTreeView
        Left = 0
        Top = 0
        Width = 250
        Height = 555
        Align = alClient
        Indent = 19
        ReadOnly = True
        TabOrder = 0
        OnDblClick = tvDatabasesDblClick
      end
    end
    object pcRight: TPageControl
      Left = 253
      Top = 0
      Width = 647
      Height = 555
      ActivePage = tsData
      Align = alClient
      TabOrder = 1
      object tsData: TTabSheet
        Caption = '数据'
        object dbgData: TDBGrid
          Left = 0
          Top = 0
          Width = 639
          Height = 525
          Align = alClient
          DataSource = dsData
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgConfirmDelete, dgCancelOnExit, dgTitleClick, dgTitleHotTrack]
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -12
          TitleFont.Name = 'Segoe UI'
          TitleFont.Style = []
        end
      end

    end
  end
  object sbMain: TStatusBar
    Left = 0
    Top = 581
    Width = 900
    Height = 19
    Panels = <>
    SimplePanel = True
    SimpleText = '未连接'
  end
  object tbMain: TToolBar
    Left = 0
    Top = 0
    Width = 900
    Height = 26
    Caption = 'tbMain'
    Images = ilMain
    TabOrder = 2
    object btnConnect: TToolButton
      Left = 0
      Top = 0
      Action = actConnect
    end
    object btnDisconnect: TToolButton
      Left = 23
      Top = 0
      Action = actDisconnect
    end
    object btnExportData: TToolButton
      Left = 46
      Top = 0
      Action = actExportData
    end
    object btnImportData: TToolButton
      Left = 69
      Top = 0
      Action = actImportData
    end
    object btnRefreshData: TToolButton
      Left = 92
      Top = 0
      Action = actRefreshData
    end
    object btnModifyData: TToolButton
      Left = 115
      Top = 0
      Action = actModifyData
    end
    object btnRefreshDB: TToolButton
      Left = 138
      Top = 0
      Action = actRefreshDB
    end
    object btnSearchDB: TToolButton
      Left = 161
      Top = 0
      Action = actSearchDB
    end
    object btnConvertDB: TToolButton
      Left = 184
      Top = 0
      Action = actConvertDB
    end
  end
  object mmMain: TMainMenu
    Left = 48
    Top = 72
    object miFile: TMenuItem
      Caption = '文件(&F)'
      object miConnect: TMenuItem
        Action = actConnect
      end
      object miDisconnect: TMenuItem
        Action = actDisconnect
      end
      object N1: TMenuItem
        Caption = '-'
      end
      object miExit: TMenuItem
        Action = actExit
      end
    end
    object miEdit: TMenuItem
      Caption = '编辑(&E)'
    end

    object miHelp: TMenuItem
      Caption = '帮助(&H)'
      object miAbout: TMenuItem
        Caption = '关于(&A)...'
      end
    end
  end
  object ilMain: TImageList
    Left = 48
    Top = 128
  end
  object alMain: TActionList
    Images = ilMain
    Left = 48
    Top = 184
    object actConnect: TAction
      Caption = '连接(&C)'
      OnExecute = actConnectExecute
    end
    object actDisconnect: TAction
      Caption = '断开连接(&D)'
      OnExecute = actDisconnectExecute
    end
    object actExit: TAction
      Caption = '退出(&X)'
      OnExecute = actExitExecute
    end
    object actExportData: TAction
      Caption = '导出数据(&E)'
      OnExecute = actExportDataExecute
    end
    object actImportData: TAction
      Caption = '导入数据(&I)'
      OnExecute = actImportDataExecute
    end
    object actRefreshData: TAction
      Caption = '刷新数据(&R)'
      OnExecute = actRefreshDataExecute
    end
    object actModifyData: TAction
      Caption = '修改数据(&M)'
      OnExecute = actModifyDataExecute
    end
    object actRefreshDB: TAction
      Caption = '刷新数据库(&F)'
      OnExecute = actRefreshDBExecute
    end
    object actSearchDB: TAction
      Caption = '搜索数据库(&S)'
      OnExecute = actSearchDBExecute
    end
    object actConvertDB: TAction
      Caption = '转换数据库(&C)'
      OnExecute = actConvertDBExecute
    end
  end
  object dsData: TDataSource
    Left = 120
    Top = 184
  end
  object pmTreeView: TPopupMenu
    Left = 120
    Top = 72
    object miExportData: TMenuItem
      Action = actExportData
    end
    object miImportData: TMenuItem
      Action = actImportData
    end
    object miRefreshData: TMenuItem
      Action = actRefreshData
    end
    object miModifyData: TMenuItem
      Action = actModifyData
    end
    object miRefreshDB: TMenuItem
      Action = actRefreshDB
    end
    object miSearchDB: TMenuItem
      Action = actSearchDB
    end
    object miConvertDB: TMenuItem
      Action = actConvertDB
    end
  end
  object pmDBGrid: TPopupMenu
    Left = 120
    Top = 128
  end
end
