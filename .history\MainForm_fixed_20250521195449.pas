unit MainForm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ComCtrls, Vcl.ExtCtrls,
  Vcl.<PERSON>us, <PERSON>c<PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON>cl<PERSON>, System.ImageList, Vcl.ImgList,
  Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error, FireDAC.UI.Intf,
  FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool, FireDAC.Stan.Async,
  FireDAC.Phys, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs,
  FireDAC.VCLUI.Wait, FireDAC.Comp.Client, System.Actions, Vcl.ActnList, Vcl.DBGrids,
  FireDAC.Comp.DataSet;

type
  TfrmMain = class(TForm)
    pnlMain: TPanel;
    sbMain: TStatusBar;
    mmMain: TMainMenu;
    miFile: TMenuItem;
    miConnect: TMenuItem;
    miDisconnect: TMenuItem;
    N1: TMenuItem;
    miExit: TMenuItem;
    miEdit: TMenuItem;
    miView: TMenuItem;
    miTools: TMenuItem;
    miHelp: TMenuItem;
    miAbout: TMenuItem;
    tbMain: TToolBar;
    ilMain: TImageList;
    pnlLeft: TPanel;
    tvDatabases: TTreeView;
    splVertical: TSplitter;
    pcRight: TPageControl;
    tsQuery: TTabSheet;
    tsData: TTabSheet;
    tsStructure: TTabSheet;
    alMain: TActionList;
    actConnect: TAction;
    actDisconnect: TAction;
    actExit: TAction;
    actExecuteQuery: TAction;
    actNewQuery: TAction;
    actSaveQuery: TAction;
    actOpenQuery: TAction;
    btnConnect: TToolButton;
    btnDisconnect: TToolButton;
    btnSep1: TToolButton;
    btnNewQuery: TToolButton;
    btnOpenQuery: TToolButton;
    btnSaveQuery: TToolButton;
    btnSep2: TToolButton;
    btnExecuteQuery: TToolButton;
    miQuery: TMenuItem;
    miNewQuery: TMenuItem;
    miOpenQuery: TMenuItem;
    miSaveQuery: TMenuItem;
    N2: TMenuItem;
    miExecuteQuery: TMenuItem;
    dbgData: TDBGrid;
    dsData: TDataSource;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure actConnectExecute(Sender: TObject);
    procedure actDisconnectExecute(Sender: TObject);
    procedure actExitExecute(Sender: TObject);
    procedure actExecuteQueryExecute(Sender: TObject);
    procedure actNewQueryExecute(Sender: TObject);
    procedure actSaveQueryExecute(Sender: TObject);
    procedure actOpenQueryExecute(Sender: TObject);
    procedure tvDatabasesDblClick(Sender: TObject);
  private
    { Private declarations }
    FConnection: TFDConnection;
    FCurrentQuery: TFDQuery;
    FCurrentTable: string;
    procedure InitializeControls;
    procedure DisconnectFromDatabase;
    procedure LoadDatabaseStructure;
    procedure LoadTableData(const ATableName: string);
  public
    { Public declarations }
  end;

var
  frmMain: TfrmMain;

implementation

{$R *.dfm}

uses
  DBConnection, SQLExecutor, TableBrowser, QueryEditor, DataViewer,
  StructureManager, DataTransfer, Settings, ConnectionDialog;
