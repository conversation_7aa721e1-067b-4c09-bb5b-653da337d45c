function TfrmConnectionDialog.ReadIniValue(const FileName, Section, Key, DefaultValue: string): string;
var
  IniFile: TIniFile;
begin
  Result := DefaultValue;

  if not FileExists(FileName) then
    Exit;

  IniFile := TIniFile.Create(FileName);
  try
    Result := IniFile.ReadString(Section, Key, DefaultValue);
  finally
    IniFile.Free;
  end;
end;

function TfrmConnectionDialog.AutoDetectDatabase(const AGameDir: string): Boolean;
var
  GamePath, MirServerPath, ConfigIniPath, DBPath, DBFile: string;
  UseAccessDB: Integer;
  DBType, AccessFileName: string;
  ParentDir, CurrentDir: string;
  IsFile: Boolean;
  IniFile: TIniFile;
begin
  Result := False;

  if AGameDir = '' then
    Exit(False);

  // 判断选择的是文件还是目录
  IsFile := FileExists(AGameDir);
  
  // 确保路径以反斜杠结尾
  if IsFile then
    GamePath := IncludeTrailingPathDelimiter(ExtractFilePath(AGameDir))
  else
    GamePath := IncludeTrailingPathDelimiter(AGameDir);

  // 查找MirServer目录
  // 1. 检查当前目录是否就是MirServer
  if ExtractFileName(ExcludeTrailingPathDelimiter(GamePath)) = 'MirServer' then
    MirServerPath := GamePath
  // 2. 检查当前目录下是否有MirServer子目录
  else if DirectoryExists(GamePath + 'MirServer') then
    MirServerPath := GamePath + 'MirServer\'
  // 3. 检查当前目录是否在MirServer目录下
  else
  begin
    // 向上查找MirServer目录
    CurrentDir := GamePath;
    while (CurrentDir <> '') do
    begin
      ParentDir := IncludeTrailingPathDelimiter(ExtractFilePath(ExcludeTrailingPathDelimiter(CurrentDir)));
      
      // 检查父目录是否为MirServer
      if ExtractFileName(ExcludeTrailingPathDelimiter(ParentDir)) = 'MirServer' then
      begin
        MirServerPath := ParentDir;
        Break;
      end;
      
      // 检查父目录下是否有MirServer
      if DirectoryExists(ParentDir + 'MirServer') then
      begin
        MirServerPath := ParentDir + 'MirServer\';
        Break;
      end;
      
      // 如果已经到达根目录，则退出循环
      if (ParentDir = CurrentDir) then
        Break;
        
      CurrentDir := ParentDir;
    end;
    
    // 如果没有找到MirServer目录，则使用当前目录
    if CurrentDir = '' then
      MirServerPath := GamePath;
  end;

  // 构建Config.ini路径
  ConfigIniPath := MirServerPath + 'Config.ini';

  if not FileExists(ConfigIniPath) then
  begin
    // 尝试在上级目录查找
    ConfigIniPath := IncludeTrailingPathDelimiter(ExtractFilePath(ExcludeTrailingPathDelimiter(MirServerPath))) + 'Config.ini';

    if not FileExists(ConfigIniPath) then
      Exit(False);
  end;
