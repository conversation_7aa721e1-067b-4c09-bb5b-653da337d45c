unit ConnectionDialog;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.StdCtrls, Vcl.ExtCtrls,
  Vcl.ComCtrls, Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error,
  FireDAC.UI.Intf, FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool,
  FireDAC.Stan.Async, FireDAC.Phys, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef,
  FireDAC.Stan.ExprFuncs, FireDAC.VCLUI.Wait, FireDAC.Comp.Client, FireDAC.Phys.ODBC,
  FireDAC.Phys.ODBCDef;

type
  TfrmConnectionDialog = class(TForm)
    pnlMain: TPanel;
    rgDatabaseType: TRadioGroup;
    pnlSQLite: TPanel;
    lblSQLiteFile: TLabel;
    edtSQLiteFile: TEdit;
    btnBrowseSQLite: TButton;
    pnlAccess: TPanel;
    lblAccessFile: TLabel;
    edtAccessFile: TEdit;
    btnBrowseAccess: TButton;
    pnlButtons: TPanel;
    btnConnect: TButton;
    btnCancel: TButton;
    procedure FormCreate(Sender: TObject);
    procedure rgDatabaseTypeClick(Sender: TObject);
    procedure btnBrowseSQLiteClick(Sender: TObject);
    procedure btnBrowseAccessClick(Sender: TObject);
    procedure btnConnectClick(Sender: TObject);
    procedure btnCancelClick(Sender: TObject);
  private
    { Private declarations }
    FConnection: TFDConnection;
    FDBType: DBConnection.TDBType;
    procedure UpdatePanels;
    function ConnectToSQLite(const AFileName: string): Boolean;
    function ConnectToAccess(const AFileName: string): Boolean;
  public
    { Public declarations }
    constructor Create(AOwner: TComponent; AConnection: TFDConnection); reintroduce;
    function Execute: Boolean;
  end;

var
  frmConnectionDialog: TfrmConnectionDialog;

implementation

{$R *.dfm}

constructor TfrmConnectionDialog.Create(AOwner: TComponent; AConnection: TFDConnection);
begin
  inherited Create(AOwner);
  FConnection := AConnection;
end;

procedure TfrmConnectionDialog.FormCreate(Sender: TObject);
begin
  FDBType := DBConnection.dbtSQLite;
  rgDatabaseType.ItemIndex := 0;
  UpdatePanels;
end;

procedure TfrmConnectionDialog.UpdatePanels;
begin
  pnlSQLite.Visible := (FDBType = DBConnection.dbtSQLite);
  pnlAccess.Visible := (FDBType = DBConnection.dbtAccess);
end;

procedure TfrmConnectionDialog.rgDatabaseTypeClick(Sender: TObject);
begin
  case rgDatabaseType.ItemIndex of
    0: FDBType := DBConnection.dbtSQLite;
    1: FDBType := DBConnection.dbtAccess;
  end;
  UpdatePanels;
end;

procedure TfrmConnectionDialog.btnBrowseSQLiteClick(Sender: TObject);
var
  OpenDialog: TOpenDialog;
begin
  OpenDialog := TOpenDialog.Create(Self);
  try
    OpenDialog.Filter := 'SQLite????? (*.db;*.sqlite;*.sqlite3)|*.db;*.sqlite;*.sqlite3|??????? (*.*)|*.*';
    OpenDialog.Title := '??SQLite?????';

    if OpenDialog.Execute then
    begin
      edtSQLiteFile.Text := OpenDialog.FileName;
    end;
  finally
    OpenDialog.Free;
  end;
end;

procedure TfrmConnectionDialog.btnBrowseAccessClick(Sender: TObject);
var
  OpenDialog: TOpenDialog;
begin
  OpenDialog := TOpenDialog.Create(Self);
  try
    OpenDialog.Filter := 'Access????? (*.mdb;*.accdb)|*.mdb;*.accdb|??????? (*.*)|*.*';
    OpenDialog.Title := '??Access?????';

    if OpenDialog.Execute then
    begin
      edtAccessFile.Text := OpenDialog.FileName;
    end;
  finally
    OpenDialog.Free;
  end;
end;

function TfrmConnectionDialog.ConnectToSQLite(const AFileName: string): Boolean;
begin
  Result := False;

  try
    if FConnection.Connected then
      FConnection.Close;

    // ???????????
    FConnection.Params.Clear;
    FConnection.Params.DriverID := 'SQLite';

    // ??????????????
    FConnection.FormatOptions.MapRules.Clear;
    FConnection.FormatOptions.OwnMapRules := True;
    FConnection.FormatOptions.StrsEmpty2Null := False;
    FConnection.FormatOptions.StrsTrim := False;
    // ???gb2312???????????????????
    FConnection.Params.Add('CharacterSet=gb2312');

    FConnection.Params.Database := AFileName;
    FConnection.Open;

    Result := True;
  except
    on E: Exception do
    begin
      ShowMessage('????SQLite????????: ' + E.Message);
    end;
  end;
end;

function TfrmConnectionDialog.ConnectToAccess(const AFileName: string): Boolean;
begin
  Result := False;

  try
    if FConnection.Connected then
      FConnection.Close;

    // ???????????
    FConnection.Params.Clear;
    FConnection.Params.DriverID := 'ODBC';
    FConnection.Params.Add('Database=' + AFileName);

    // ???Microsoft Access Driver
    FConnection.Params.Add('DriverID=MSAcc');

    // ??????????????
    FConnection.FormatOptions.MapRules.Clear;
    FConnection.FormatOptions.OwnMapRules := True;
    FConnection.FormatOptions.StrsEmpty2Null := False;
    FConnection.FormatOptions.StrsTrim := False;

    FConnection.Open;

    Result := True;
  except
    on E: Exception do
    begin
      ShowMessage('????Access????????: ' + E.Message);
    end;
  end;
end;

procedure TfrmConnectionDialog.btnConnectClick(Sender: TObject);
var
  FileName: string;
  Connected: Boolean;
begin
  Connected := False;

  case FDBType of
    dbtSQLite:
      begin
        FileName := edtSQLiteFile.Text;
        if FileName = '' then
        begin
          ShowMessage('?????SQLite????????');
          Exit;
        end;
        Connected := ConnectToSQLite(FileName);
      end;
    dbtAccess:
      begin
        FileName := edtAccessFile.Text;
        if FileName = '' then
        begin
          ShowMessage('?????Access????????');
          Exit;
        end;
        Connected := ConnectToAccess(FileName);
      end;
  end;

  if Connected then
    ModalResult := mrOk;
end;

procedure TfrmConnectionDialog.btnCancelClick(Sender: TObject);
begin
  ModalResult := mrCancel;
end;

function TfrmConnectionDialog.Execute: Boolean;
begin
  Result := (ShowModal = mrOk);
end;

end.
