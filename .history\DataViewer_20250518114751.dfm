object frmDataViewer: TfrmDataViewer
  Left = 0
  Top = 0
  Caption = '数据查看器'
  ClientHeight = 500
  ClientWidth = 700
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poScreenCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  TextHeight = 15
  object pnlMain: TPanel
    Left = 0
    Top = 26
    Width = 700
    Height = 455
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    object navData: TDBNavigator
      Left = 0
      Top = 0
      Width = 700
      Height = 25
      DataSource = dsData
      Align = alTop
      TabOrder = 0
    end
    object dbgData: TDBGrid
      Left = 0
      Top = 65
      Width = 700
      Height = 390
      Align = alClient
      DataSource = dsData
      Options = [dgEditing, dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgConfirmDelete, dgCancelOnExit, dgTitleClick, dgTitleHotTrack]
      PopupMenu = pmData
      TabOrder = 1
      TitleFont.Charset = DEFAULT_CHARSET
      TitleFont.Color = clWindowText
      TitleFont.Height = -12
      TitleFont.Name = 'Segoe UI'
      TitleFont.Style = []
    end
    object pnlFilter: TPanel
      Left = 0
      Top = 25
      Width = 700
      Height = 40
      Align = alTop
      BevelOuter = bvNone
      TabOrder = 2
      object lblFilter: TLabel
        Left = 8
        Top = 14
        Width = 52
        Height = 15
        Caption = '过滤条件:'
      end
      object edtFilter: TEdit
        Left = 66
        Top = 10
        Width = 465
        Height = 23
        TabOrder = 0
        TextHint = '输入WHERE子句过滤条件'
      end
      object btnApplyFilter: TButton
        Left = 537
        Top = 9
        Width = 75
        Height = 25
        Caption = '应用'
        TabOrder = 1
        OnClick = btnApplyFilterClick
      end
      object btnClearFilter: TButton
        Left = 618
        Top = 9
        Width = 75
        Height = 25
        Caption = '清除'
        TabOrder = 2
        OnClick = btnClearFilterClick
      end
    end
  end
  object tbMain: TToolBar
    Left = 0
    Top = 0
    Width = 700
    Height = 26
    Caption = 'tbMain'
    TabOrder = 1
    object btnRefresh: TToolButton
      Left = 0
      Top = 0
      Action = actRefresh
    end
    object btnSep1: TToolButton
      Left = 23
      Top = 0
      Width = 8
      Caption = 'btnSep1'
      ImageIndex = 1
      Style = tbsSeparator
    end
    object btnPost: TToolButton
      Left = 31
      Top = 0
      Action = actPost
    end
    object btnCancel: TToolButton
      Left = 54
      Top = 0
      Action = actCancel
    end
    object btnSep2: TToolButton
      Left = 77
      Top = 0
      Width = 8
      Caption = 'btnSep2'
      ImageIndex = 4
      Style = tbsSeparator
    end
    object btnExport: TToolButton
      Left = 85
      Top = 0
      Action = actExport
    end
    object btnFilter: TToolButton
      Left = 108
      Top = 0
      Action = actFilter
    end
  end
  object dsData: TDataSource
    Left = 40
    Top = 88
  end
  object alMain: TActionList
    Left = 40
    Top = 152
    object actRefresh: TAction
      Caption = '刷新'
      OnExecute = actRefreshExecute
    end
    object actPost: TAction
      Caption = '保存'
      OnExecute = actPostExecute
    end
    object actCancel: TAction
      Caption = '取消'
      OnExecute = actCancelExecute
    end
    object actExport: TAction
      Caption = '导出'
      OnExecute = actExportExecute
    end
    object actFilter: TAction
      Caption = '过滤'
      OnExecute = actFilterExecute
    end
  end
  object pmData: TPopupMenu
    Left = 40
    Top = 216
    object miRefresh: TMenuItem
      Action = actRefresh
    end
    object miPost: TMenuItem
      Action = actPost
    end
    object miCancel: TMenuItem
      Action = actCancel
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object miExport: TMenuItem
      Action = actExport
    end
    object miFilter: TMenuItem
      Action = actFilter
    end
  end
  object sbMain: TStatusBar
    Left = 0
    Top = 481
    Width = 700
    Height = 19
    Panels = <>
    SimplePanel = True
  end
end
