unit MainForm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ComCtrls, Vcl.ExtCtrls,
  Vcl.<PERSON>us, <PERSON>cl<PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON>cl<PERSON>, System.ImageList, Vcl.ImgList,
  Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error, FireDAC.UI.Intf,
  FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool, FireDAC.Stan.Async,
  FireDAC.Phys, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs,
  FireDAC.VCLUI.Wait, FireDAC.Comp.Client, System.Actions, Vcl.ActnList;

type
  TfrmMain = class(TForm)
    pnlMain: TPanel;
    sbMain: TStatusBar;
    mmMain: TMainMenu;
    miFile: TMenuItem;
    miConnect: TMenuItem;
    miDisconnect: TMenuItem;
    N1: TMenuItem;
    miExit: TMenuItem;
    miEdit: TMenuItem;
    miView: TMenuItem;
    miTools: TMenuItem;
    miHelp: TMenuItem;
    miAbout: TMenuItem;
    tbMain: TToolBar;
    ilMain: TImageList;
    pnlLeft: TPanel;
    tvDatabases: TTreeView;
    splVertical: TSplitter;
    pcRight: TPageControl;
    tsQuery: TTabSheet;
    tsData: TTabSheet;
    tsStructure: TTabSheet;
    alMain: TActionList;
    actConnect: TAction;
    actDisconnect: TAction;
    actExit: TAction;
    actExecuteQuery: TAction;
    actNewQuery: TAction;
    actSaveQuery: TAction;
    actOpenQuery: TAction;
    btnConnect: TToolButton;
    btnDisconnect: TToolButton;
    btnSep1: TToolButton;
    btnNewQuery: TToolButton;
    btnOpenQuery: TToolButton;
    btnSaveQuery: TToolButton;
    btnSep2: TToolButton;
    btnExecuteQuery: TToolButton;
    miQuery: TMenuItem;
    miNewQuery: TMenuItem;
    miOpenQuery: TMenuItem;
    miSaveQuery: TMenuItem;
    N2: TMenuItem;
    miExecuteQuery: TMenuItem;
    dbgData: TDBGrid;
    dsData: TDataSource;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure actConnectExecute(Sender: TObject);
    procedure actDisconnectExecute(Sender: TObject);
    procedure actExitExecute(Sender: TObject);
    procedure actExecuteQueryExecute(Sender: TObject);
    procedure actNewQueryExecute(Sender: TObject);
    procedure actSaveQueryExecute(Sender: TObject);
    procedure actOpenQueryExecute(Sender: TObject);
    procedure tvDatabasesDblClick(Sender: TObject);
  private
    { Private declarations }
    FConnection: TFDConnection;
    FQuery: TFDQuery;
    procedure InitializeControls;
    procedure ConnectToDatabase(const AFileName: string);
    procedure DisconnectFromDatabase;
    procedure RefreshDatabaseObjects;
    procedure LoadDatabaseStructure;
    procedure ShowTableData(const ATableName: string);
  public
    { Public declarations }
  end;

var
  frmMain: TfrmMain;

implementation

{$R *.dfm}

uses
  DBConnection, SQLExecutor, TableBrowser, QueryEditor, DataViewer,
  StructureManager, DataTransfer, Settings;

procedure TfrmMain.FormCreate(Sender: TObject);
begin
  InitializeControls;
  FConnection := TFDConnection.Create(Self);
  FConnection.Params.DriverID := 'SQLite';
end;

procedure TfrmMain.FormDestroy(Sender: TObject);
begin
  if FConnection.Connected then
    FConnection.Close;
  FConnection.Free;
end;

procedure TfrmMain.InitializeControls;
begin
  pcRight.ActivePage := tsQuery;
  actDisconnect.Enabled := False;
  actExecuteQuery.Enabled := False;
  actNewQuery.Enabled := False;
  actSaveQuery.Enabled := False;
  actOpenQuery.Enabled := True;
end;

procedure TfrmMain.actConnectExecute(Sender: TObject);
var
  OpenDialog: TOpenDialog;
begin
  OpenDialog := TOpenDialog.Create(Self);
  try
    OpenDialog.Filter := 'SQLite????? (*.db;*.sqlite;*.sqlite3)|*.db;*.sqlite;*.sqlite3|??????? (*.*)|*.*';
    OpenDialog.Title := '??SQLite?????';

    if OpenDialog.Execute then
    begin
      ConnectToDatabase(OpenDialog.FileName);
    end;
  finally
    OpenDialog.Free;
  end;
end;

procedure TfrmMain.ConnectToDatabase(const AFileName: string);
begin
  try
    if FConnection.Connected then
      FConnection.Close;

    // ??????????????????
    FConnection.FormatOptions.MapRules.Clear;
    FConnection.FormatOptions.OwnMapRules := True;
    FConnection.FormatOptions.StrsEmpty2Null := False;
    FConnection.FormatOptions.StrsTrim := False;
    FConnection.Params.Add('CharacterSet=UTF8');

    FConnection.Params.Database := AFileName;
    FConnection.Open;

    sbMain.SimpleText := '???????: ' + AFileName;
    actConnect.Enabled := False;
    actDisconnect.Enabled := True;
    actExecuteQuery.Enabled := True;
    actNewQuery.Enabled := True;

    LoadDatabaseStructure;
  except
    on E: Exception do
    begin
      ShowMessage('????????????: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.actDisconnectExecute(Sender: TObject);
begin
  DisconnectFromDatabase;
end;

procedure TfrmMain.DisconnectFromDatabase;
begin
  if FConnection.Connected then
    FConnection.Close;

  sbMain.SimpleText := '??????';
  actConnect.Enabled := True;
  actDisconnect.Enabled := False;
  actExecuteQuery.Enabled := False;
  actNewQuery.Enabled := False;
  actSaveQuery.Enabled := False;

  tvDatabases.Items.Clear;
end;

procedure TfrmMain.LoadDatabaseStructure;
var
  DBManager: TDBConnectionManager;
begin
  tvDatabases.Items.Clear;

  if not FConnection.Connected then
    Exit;

  DBManager := TDBConnectionManager.Create(FConnection, tvDatabases);
  try
    DBManager.RefreshDatabaseObjects;
  finally
    DBManager.Free;
  end;
end;

procedure TfrmMain.RefreshDatabaseObjects;
var
  DBManager: TDBConnectionManager;
begin
  if not FConnection.Connected then
    Exit;

  DBManager := TDBConnectionManager.Create(FConnection, tvDatabases);
  try
    DBManager.RefreshDatabaseObjects;
  finally
    DBManager.Free;
  end;
end;

procedure TfrmMain.actExitExecute(Sender: TObject);
begin
  Close;
end;

procedure TfrmMain.actExecuteQueryExecute(Sender: TObject);
begin
  // ????SQLExecutor????????
end;

procedure TfrmMain.actNewQueryExecute(Sender: TObject);
begin
  // ????QueryEditor????????
end;

procedure TfrmMain.actOpenQueryExecute(Sender: TObject);
begin
  // ????QueryEditor????????
end;

procedure TfrmMain.actSaveQueryExecute(Sender: TObject);
begin
  // ????QueryEditor????????
end;

procedure TfrmMain.tvDatabasesDblClick(Sender: TObject);
var
  Node: TTreeNode;
  NodeType, ObjectName: string;
  TableBrowser: TfrmTableBrowser;
begin
  Node := tvDatabases.Selected;
  if not Assigned(Node) or not FConnection.Connected then
    Exit;

  // ?????????
  if (Node.Level = 2) and (Node.Parent.Text = '??') then
  begin
    // ????????????
    ObjectName := Node.Text;

    // ????????
    TableBrowser := TfrmTableBrowser.Create(Self);
    try
      TableBrowser.Initialize(FConnection, ObjectName);
      TableBrowser.ShowModal;
    finally
      TableBrowser.Free;
    end;
  end;
end;

end.
