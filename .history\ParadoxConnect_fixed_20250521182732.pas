function TfrmConnectionDialog.ConnectToParadox(const ADirectory: string): Boolean;
var
  SearchRec: TSearchRec;
  Directory: string;
  ADOConnection: TADOConnection;
  ConnectionString: string;
  DSNString: string;
  Registry: TRegistry;
  ODBCDriverInstalled: Boolean;
  ErrorMsg: string;
  FoundDBFiles: TStringList;
begin
  Result := False;
  FoundDBFiles := TStringList.Create;
  try
    if FConnection.Connected then
      FConnection.Close;

    // 确认目录存在
    Directory := ADirectory;
    if not DirectoryExists(Directory) then
    begin
      ShowMessage('指定的Paradox数据库目录不存在');
      Exit;
    end;

    // 确认目录以反斜杠结尾
    if (Directory <> '') and (Directory[Length(Directory)] <> PathDelim) then
      Directory := Directory + PathDelim;

    // 检查目录中是否有Paradox数据文件
    if FindFirst(Directory + '*.db', faAnyFile, SearchRec) = 0 then
    begin
      try
        repeat
          FoundDBFiles.Add(SearchRec.Name);
        until FindNext(SearchRec) <> 0;
      finally
        FindClose(SearchRec);
      end;
    end;

    if FoundDBFiles.Count = 0 then
    begin
      ShowMessage('指定的目录中没有找到Paradox数据文件 (*.db)');
      Exit;
    end;

    // 检查是否安装了Paradox ODBC驱动程序
    ODBCDriverInstalled := False;
    Registry := TRegistry.Create;
    try
      Registry.RootKey := HKEY_LOCAL_MACHINE;
      if Registry.OpenKeyReadOnly('SOFTWARE\ODBC\ODBCINST.INI\ODBC Drivers') then
      begin
        ODBCDriverInstalled := Registry.ValueExists('Microsoft Paradox Driver (*.db)') or
                              Registry.ValueExists('Paradox Driver');
      end;
    finally
      Registry.Free;
    end;

    // 方法1：使用ADO直接连接
    try
      ADOConnection := TADOConnection.Create(nil);
      try
        // 设置ADO连接字符串 - 尝试多种提供程序
        ConnectionString := 'Provider=Microsoft.Jet.OLEDB.4.0;Data Source=' + Directory + ';Extended Properties=Paradox 5.x;';

        ADOConnection.ConnectionString := ConnectionString;
        ADOConnection.LoginPrompt := False;
        ADOConnection.Open;

        // 如果ADO连接成功，使用FireDAC的ODBC连接
        FConnection.Params.Clear;
        FConnection.Params.DriverID := 'ODBC';

        // 创建一个临时DSN
        DSNString := 'DRIVER={Microsoft Paradox Driver (*.db)};DefaultDir=' + Directory + ';';
        FConnection.Params.Add('ConnectionString=' + DSNString);

        // 设置字符串支持
        FConnection.FormatOptions.MapRules.Clear;
        FConnection.FormatOptions.OwnMapRules := True;
        FConnection.FormatOptions.StrsEmpty2Null := False;
        FConnection.FormatOptions.StrsTrim := False;

        // 设置字符集
        FConnection.Params.Add('CharacterSet=gb2312');

        FConnection.Open;
        Result := True;
        Exit;
      finally
        ADOConnection.Free;
      end;
    except
      on E: Exception do
      begin
        ErrorMsg := E.Message;
        // 如果ADO连接失败，尝试方法2
      end;
    end;
