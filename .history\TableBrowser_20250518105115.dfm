object frmTableBrowser: TfrmTableBrowser
  Left = 0
  Top = 0
  Caption = '表浏览器'
  ClientHeight = 500
  ClientWidth = 700
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poScreenCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  TextHeight = 15
  object pnlMain: TPanel
    Left = 0
    Top = 26
    Width = 700
    Height = 474
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    object pcMain: TPageControl
      Left = 0
      Top = 41
      Width = 700
      Height = 433
      ActivePage = tsData
      Align = alClient
      TabOrder = 0
      object tsData: TTabSheet
        Caption = '数据'
        object dbgData: TDBGrid
          Left = 0
          Top = 0
          Width = 692
          Height = 403
          Align = alClient
          DataSource = dsData
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgConfirmDelete, dgCancelOnExit, dgTitleClick, dgTitleHotTrack]
          PopupMenu = pmData
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -12
          TitleFont.Name = 'Segoe UI'
          TitleFont.Style = []
        end
      end
      object tsStructure: TTabSheet
        Caption = '结构'
        ImageIndex = 1
        object lvStructure: TListView
          Left = 0
          Top = 0
          Width = 692
          Height = 403
          Align = alClient
          Columns = <
            item
              Caption = '列名'
              Width = 150
            end
            item
              Caption = '类型'
              Width = 100
            end
            item
              Caption = '非空'
              Width = 50
            end
            item
              Caption = '默认值'
              Width = 100
            end
            item
              Caption = '主键'
              Width = 50
            end>
          GridLines = True
          ReadOnly = True
          RowSelect = True
          TabOrder = 0
          ViewStyle = vsReport
        end
      end
      object tsIndices: TTabSheet
        Caption = '索引'
        ImageIndex = 2
        object lvIndices: TListView
          Left = 0
          Top = 0
          Width = 692
          Height = 403
          Align = alClient
          Columns = <
            item
              Caption = '索引名'
              Width = 150
            end
            item
              Caption = '唯一'
              Width = 50
            end
            item
              Caption = '列'
              Width = 200
            end>
          GridLines = True
          ReadOnly = True
          RowSelect = True
          TabOrder = 0
          ViewStyle = vsReport
        end
      end
      object tsTriggers: TTabSheet
        Caption = '触发器'
        ImageIndex = 3
        object lvTriggers: TListView
          Left = 0
          Top = 0
          Width = 692
          Height = 403
          Align = alClient
          Columns = <
            item
              Caption = '触发器名'
              Width = 150
            end
            item
              Caption = 'SQL'
              Width = 500
            end>
          GridLines = True
          ReadOnly = True
          RowSelect = True
          TabOrder = 0
          ViewStyle = vsReport
        end
      end
    end
    object pnlTop: TPanel
      Left = 0
      Top = 0
      Width = 700
      Height = 41
      Align = alTop
      BevelOuter = bvNone
      TabOrder = 1
      object lblTable: TLabel
        Left = 8
        Top = 14
        Width = 24
        Height = 15
        Caption = '表：'
      end
      object edtFilter: TEdit
        Left = 344
        Top = 10
        Width = 265
        Height = 23
        TabOrder = 0
        TextHint = '输入WHERE子句过滤条件'
      end
      object btnApplyFilter: TButton
        Left = 615
        Top = 9
        Width = 75
        Height = 25
        Caption = '应用过滤'
        TabOrder = 1
        OnClick = btnApplyFilterClick
      end
    end
  end
  object tbMain: TToolBar
    Left = 0
    Top = 0
    Width = 700
    Height = 26
    Caption = 'tbMain'
    TabOrder = 1
    object btnRefresh: TToolButton
      Left = 0
      Top = 0
      Action = actRefresh
    end
    object btnExport: TToolButton
      Left = 23
      Top = 0
      Action = actExport
    end
    object btnImport: TToolButton
      Left = 46
      Top = 0
      Action = actImport
    end
    object btnSep1: TToolButton
      Left = 69
      Top = 0
      Width = 8
      Caption = 'btnSep1'
      ImageIndex = 3
      Style = tbsSeparator
    end
    object btnFilter: TToolButton
      Left = 77
      Top = 0
      Action = actFilter
    end
  end
  object dsData: TDataSource
    Left = 40
    Top = 88
  end
  object alMain: TActionList
    Left = 40
    Top = 152
    object actRefresh: TAction
      Caption = '刷新'
      OnExecute = actRefreshExecute
    end
    object actExport: TAction
      Caption = '导出'
      OnExecute = actExportExecute
    end
    object actImport: TAction
      Caption = '导入'
      OnExecute = actImportExecute
    end
    object actFilter: TAction
      Caption = '过滤'
      OnExecute = actFilterExecute
    end
  end
  object pmData: TPopupMenu
    Left = 40
    Top = 216
    object miRefresh: TMenuItem
      Action = actRefresh
    end
    object miExport: TMenuItem
      Action = actExport
    end
    object miImport: TMenuItem
      Action = actImport
    end
    object N1: TMenuItem
      Caption = '-'
    end
    object miFilter: TMenuItem
      Action = actFilter
    end
  end
end
