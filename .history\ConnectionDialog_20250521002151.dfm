object frmConnectionDialog: TfrmConnectionDialog
  Left = 0
  Top = 0
  BorderStyle = bsDialog
  Caption = #25968#25454#24211#36830#25509
  ClientHeight = 250
  ClientWidth = 450
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Position = poScreenCenter
  OnCreate = FormCreate
  TextHeight = 15
  object pnlMain: TPanel
    Left = 0
    Top = 0
    Width = 450
    Height = 200
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    object rgDatabaseType: TRadioGroup
      Left = 16
      Top = 16
      Width = 417
      Height = 49
      Caption = #25968#25454#24211#31867#22411
      Columns = 3
      ItemIndex = 0
      Items.Strings = (
        'SQLite'
        'Access'
        'Paradox')
      TabOrder = 0
      OnClick = rgDatabaseTypeClick
    end
    object pnlSQLite: TPanel
      Left = 16
      Top = 80
      Width = 417
      Height = 105
      BevelOuter = bvNone
      TabOrder = 1
      object lblSQLiteFile: TLabel
        Left = 16
        Top = 16
        Width = 84
        Height = 15
        Caption = 'SQLite'#25968#25454#24211#25991#20214
      end
      object edtSQLiteFile: TEdit
        Left = 16
        Top = 40
        Width = 329
        Height = 23
        TabOrder = 0
      end
      object btnBrowseSQLite: TButton
        Left = 351
        Top = 39
        Width = 50
        Height = 25
        Caption = #27983#35272'...'
        TabOrder = 1
        OnClick = btnBrowseSQLiteClick
      end
    end
    object pnlAccess: TPanel
      Left = 16
      Top = 80
      Width = 417
      Height = 105
      BevelOuter = bvNone
      TabOrder = 2
      Visible = False
      object lblAccessFile: TLabel
        Left = 16
        Top = 16
        Width = 90
        Height = 15
        Caption = 'Access'#25968#25454#24211#25991#20214
      end
      object edtAccessFile: TEdit
        Left = 16
        Top = 40
        Width = 329
        Height = 23
        TabOrder = 0
      end
      object btnBrowseAccess: TButton
        Left = 351
        Top = 39
        Width = 50
        Height = 25
        Caption = #27983#35272'...'
        TabOrder = 1
        OnClick = btnBrowseAccessClick
      end
    end
    object pnlParadox: TPanel
      Left = 16
      Top = 80
      Width = 417
      Height = 105
      BevelOuter = bvNone
      TabOrder = 3
      Visible = False
      object lblParadoxDir: TLabel
        Left = 16
        Top = 16
        Width = 90
        Height = 15
        Caption = 'Paradox'#25968#25454#24211#30446#24405
      end
      object edtParadoxDir: TEdit
        Left = 16
        Top = 40
        Width = 329
        Height = 23
        TabOrder = 0
      end
      object btnBrowseParadox: TButton
        Left = 351
        Top = 39
        Width = 50
        Height = 25
        Caption = #27983#35272'...'
        TabOrder = 1
        OnClick = btnBrowseParadoxClick
      end
    end
  end
  object pnlButtons: TPanel
    Left = 0
    Top = 200
    Width = 450
    Height = 50
    Align = alBottom
    BevelOuter = bvNone
    TabOrder = 1
    object btnConnect: TButton
      Left = 264
      Top = 12
      Width = 75
      Height = 25
      Caption = #36830#25509
      Default = True
      TabOrder = 0
      OnClick = btnConnectClick
    end
    object btnCancel: TButton
      Left = 358
      Top = 12
      Width = 75
      Height = 25
      Cancel = True
      Caption = #21462#28040
      TabOrder = 1
      OnClick = btnCancelClick
    end
    object btnAutoDetect: TButton
      Left = 16
      Top = 12
      Width = 120
      Height = 25
      Caption = #33258#21160#26816#27979#25968#25454#24211
      TabOrder = 2
      OnClick = btnAutoDetectClick
    end
  end
end
