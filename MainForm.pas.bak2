unit MainForm;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ComCtrls, Vcl.ExtCtrls,
  Vcl<PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON><PERSON>l<PERSON>, System.ImageList, Vcl.ImgList,
  Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option, FireDAC.Stan.Error, FireDAC.UI.Intf,
  FireDAC.Phys.Intf, FireDAC.Stan.Def, FireDAC.Stan.Pool, FireDAC.Stan.Async,
  FireDAC.Phys, FireDAC.Phys.SQLite, FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs,
  FireDAC.VCLUI.Wait, FireDAC.Comp.Client, System.Actions, Vcl.ActnList, Vcl.DBGrids,
  FireDAC.Comp.DataSet, System.Generics.Collections;

type
  TfrmMain = class(TForm)
    pnlMain: TPanel;
    sbMain: TStatusBar;
    mmMain: TMainMenu;
    miFile: TMenuItem;
    miConnect: TMenuItem;
    miDisconnect: TMenuItem;
    N1: TMenuItem;
    miExit: TMenuItem;
    miEdit: TMenuItem;
    miHelp: TMenuItem;
    miAbout: TMenuItem;
    tbMain: TToolBar;
    ilMain: TImageList;
    pnlLeft: TPanel;
    tvDatabases: TTreeView;
    splVertical: TSplitter;
    pcRight: TPageControl;
    tsData: TTabSheet;
    alMain: TActionList;
    actConnect: TAction;
    actDisconnect: TAction;
    actExit: TAction;
    btnConnect: TToolButton;
    btnDisconnect: TToolButton;
    dbgData: TDBGrid;
    dsData: TDataSource;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure actConnectExecute(Sender: TObject);
    procedure actDisconnectExecute(Sender: TObject);
    procedure actExitExecute(Sender: TObject);
    procedure tvDatabasesDblClick(Sender: TObject);
  private
    { Private declarations }
    FConnection: TFDConnection;
    FCurrentQuery: TFDQuery;
    FCurrentTable: string;
    procedure InitializeControls;
    procedure DisconnectFromDatabase;
    procedure LoadDatabaseStructure;
    procedure LoadTablesIntoDirectories(DBManager: TDBConnectionManager; RootNode: TTreeNode; const DBPath: string);
    procedure LoadTableData(const ATableName: string);
  public
    { Public declarations }
  end;

var
  frmMain: TfrmMain;

implementation

{$R *.dfm}

uses
  DBConnection, ConnectionDialog;

procedure TfrmMain.FormCreate(Sender: TObject);
begin
  InitializeControls;
  FConnection := TFDConnection.Create(Self);
  FConnection.Params.DriverID := 'SQLite';

  // 创建查询对象
  FCurrentQuery := TFDQuery.Create(Self);
  dsData.DataSet := FCurrentQuery;
end;

procedure TfrmMain.FormDestroy(Sender: TObject);
begin
  if FConnection.Connected then
    FConnection.Close;

  // 释放查询对象
  if Assigned(FCurrentQuery) then
    FCurrentQuery.Free;

  FConnection.Free;
end;

procedure TfrmMain.InitializeControls;
begin
  pcRight.ActivePage := tsData;
  actDisconnect.Enabled := False;
end;

procedure TfrmMain.actConnectExecute(Sender: TObject);
var
  ConnectionDlg: TfrmConnectionDialog;
begin
  ConnectionDlg := TfrmConnectionDialog.Create(Self, FConnection);
  try
    if ConnectionDlg.Execute then
    begin
      // 连接成功，更新UI
      var DBManager := TDBConnectionManager.Create(FConnection, nil);
      try
        // 显示连接方式信息
        sbMain.SimpleText := '已连接到数据库: ' + FConnection.Params.Database + ' | 连接方式: ' + DBManager.GetConnectionInfo;
      finally
        DBManager.Free;
      end;

      actConnect.Enabled := False;
      actDisconnect.Enabled := True;

      // 加载数据库结构
      LoadDatabaseStructure;
    end;
  finally
    ConnectionDlg.Free;
  end;
end;

procedure TfrmMain.actDisconnectExecute(Sender: TObject);
begin
  DisconnectFromDatabase;
end;

procedure TfrmMain.DisconnectFromDatabase;
begin
  if FConnection.Connected then
    FConnection.Close;

  sbMain.SimpleText := '已断开连接';
  actConnect.Enabled := True;
  actDisconnect.Enabled := False;

  tvDatabases.Items.Clear;
end;

procedure TfrmMain.LoadDatabaseStructure;
var
  DBManager: TDBConnectionManager;
  RootNode: TTreeNode;
  DBPath, DBName: string;
begin
  // 清空树视图
  tvDatabases.Items.Clear;

  if not FConnection.Connected then
    Exit;

  // 获取数据库路径
  DBPath := FConnection.Params.Database;
  if DBPath = '' then
    DBPath := FConnection.Params.Values['DefaultDir'];
  
  // 获取数据库名称（使用目录名）
  if DBPath <> '' then
  begin
    // 去掉末尾的路径分隔符
    if (Length(DBPath) > 0) and (DBPath[Length(DBPath)] = PathDelim) then
      DBPath := Copy(DBPath, 1, Length(DBPath) - 1);
    
    // 获取最后一级目录名
    DBName := ExtractFileName(DBPath);
    if DBName = '' then
      DBName := DBPath;
  end
  else
    DBName := '数据库';

  // 创建根节点（使用数据库名称）
  RootNode := tvDatabases.Items.Add(nil, DBName);
  RootNode.ImageIndex := 0; // 数据库图标索引
  RootNode.SelectedIndex := 0;
  
  // 展开根节点
  RootNode.Expand(False);

  // 加载数据库对象
  DBManager := TDBConnectionManager.Create(FConnection, nil);
  try
    // 加载表并按目录组织
    LoadTablesIntoDirectories(DBManager, RootNode, DBPath);
  finally
    DBManager.Free;
  end;
end;

procedure TfrmMain.actExitExecute(Sender: TObject);
begin
  Close;
end;

procedure TfrmMain.LoadTableData(const ATableName: string);
var
  DBManager: TDBConnectionManager;
  IsSQLite: Boolean;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  try
    // 保存当前表名
    FCurrentTable := ATableName;

    // 设置页面标题
    tsData.Caption := '数据表: ' + ATableName;

    // 切换到数据页
    pcRight.ActivePage := tsData;

    // 关闭之前的查询
    if Assigned(FCurrentQuery) then
    begin
      FCurrentQuery.Close;

      // 设置字符串支持
      FCurrentQuery.Connection := FConnection;
      FCurrentQuery.FormatOptions.MapRules.Clear;
      FCurrentQuery.FormatOptions.OwnMapRules := True;
      FCurrentQuery.FormatOptions.StrsEmpty2Null := False;
      FCurrentQuery.FormatOptions.StrsTrim := False;

      // 根据数据库类型构建不同的SQL语句
      DBManager := TDBConnectionManager.Create(FConnection, nil);
      try
        IsSQLite := (DBManager.DBType = dbtSQLite);
      finally
        DBManager.Free;
      end;

      if IsSQLite then
        // SQLite使用双引号包围表名
        FCurrentQuery.SQL.Text := Format('SELECT * FROM "%s"', [ATableName])
      else
        // Access使用方括号包围表名
        FCurrentQuery.SQL.Text := Format('SELECT * FROM [%s]', [ATableName]);

      // 打开查询
      FCurrentQuery.Open;

      // 更新状态栏信息
      DBManager := TDBConnectionManager.Create(FConnection, nil);
      try
        sbMain.SimpleText := Format('已加载表 "%s"，共 %d 条记录 | 数据库: %s | 连接方式: %s',
          [ATableName, FCurrentQuery.RecordCount, FConnection.Params.Database, DBManager.GetConnectionInfo]);
      finally
        DBManager.Free;
      end;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('加载表数据失败: ' + E.Message);
    end;
  end;
end;

procedure TfrmMain.tvDatabasesDblClick(Sender: TObject);
var
  Node: TTreeNode;
  ObjectName: string;
begin
  Node := tvDatabases.Selected;
  if not Assigned(Node) then
    Exit;

  if not FConnection.Connected then
    Exit;

  // 节点类型判断 - 修改判断条件适应不同的根节点类型
  if (Node.Level = 2) then
  begin
    // 获取表名
    ObjectName := Node.Text;

    // 在右侧面板显示表数据
    LoadTableData(ObjectName);
  end;
end;
