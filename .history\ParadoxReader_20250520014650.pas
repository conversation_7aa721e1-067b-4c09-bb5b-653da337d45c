unit ParadoxReader;

interface

uses
  System.SysUtils, System.Classes, Data.DB, System.Generics.Collections;

type
  // Paradox 字段类型
  TParadoxFieldType = (
    pftAlpha,      // 字符串
    pftDate,       // 日期
    pftShort,      // 短整数
    pftLong,       // 长整数
    pftCurrency,   // 货币
    pftNumber,     // 数字
    pftLogical,    // 布尔
    pftMemoBLOB,   // 备注/BLOB
    pftBLOB,       // BLOB
    pftFmtMemoBLOB,// 格式化备注
    pftOLE,        // OLE对象
    pftGraphic,    // 图形
    pftTime,       // 时间
    pftTimestamp,  // 时间戳
    pftAutoInc,    // 自增
    pftBytes,      // 字节数组
    pftBCD         // BCD数字
  );

  // Paradox 字段定义
  TParadoxFieldDef = class
  private
    FName: string;
    FFieldType: TParadoxFieldType;
    FSize: Integer;
    FOffset: Integer;
  public
    constructor Create(const AName: string; AFieldType: TParadoxFieldType; ASize, AOffset: Integer);
    property Name: string read FName;
    property FieldType: TParadoxFieldType read FFieldType;
    property Size: Integer read FSize;
    property Offset: Integer read FOffset;
  end;

  // Paradox 记录
  TParadoxRecord = class
  private
    FData: TBytes;
    FFieldDefs: TObjectList<TParadoxFieldDef>;
    function GetFieldValue(FieldIndex: Integer): Variant;
    procedure SetFieldValue(FieldIndex: Integer; const Value: Variant);
  public
    constructor Create(AData: TBytes; AFieldDefs: TObjectList<TParadoxFieldDef>);
    destructor Destroy; override;
    property FieldValues[FieldIndex: Integer]: Variant read GetFieldValue write SetFieldValue;
  end;

  // Paradox 表读取器
  TParadoxReader = class
  private
    FFileName: string;
    FFileStream: TFileStream;
    FFieldDefs: TObjectList<TParadoxFieldDef>;
    FRecordCount: Integer;
    FRecordSize: Integer;
    FHeaderSize: Integer;
    FTableVersionID: Byte;
    FModified: Boolean;
    
    procedure ReadHeader;
    function ReadRecordData(RecordIndex: Integer): TBytes;
    procedure WriteRecordData(RecordIndex: Integer; const Data: TBytes);
    function GetFieldCount: Integer;
    function GetFieldDef(Index: Integer): TParadoxFieldDef;
  public
    constructor Create(const AFileName: string);
    destructor Destroy; override;
    
    // 表信息
    function GetFieldName(Index: Integer): string;
    function GetFieldType(Index: Integer): TParadoxFieldType;
    function GetFieldSize(Index: Integer): Integer;
    
    // 记录操作
    function GetRecordCount: Integer;
    function ReadRecord(RecordIndex: Integer): TParadoxRecord;
    procedure WriteRecord(RecordIndex: Integer; Record: TParadoxRecord);
    procedure SaveChanges;
    
    // 属性
    property FieldCount: Integer read GetFieldCount;
    property FieldDefs[Index: Integer]: TParadoxFieldDef read GetFieldDef;
    property RecordCount: Integer read GetRecordCount;
    property Modified: Boolean read FModified;
  end;

  // 异常类
  EParadoxError = class(Exception);

implementation

{ TParadoxFieldDef }

constructor TParadoxFieldDef.Create(const AName: string; AFieldType: TParadoxFieldType; ASize, AOffset: Integer);
begin
  FName := AName;
  FFieldType := AFieldType;
  FSize := ASize;
  FOffset := AOffset;
end;

{ TParadoxRecord }

constructor TParadoxRecord.Create(AData: TBytes; AFieldDefs: TObjectList<TParadoxFieldDef>);
begin
  FData := AData;
  FFieldDefs := AFieldDefs;
end;

destructor TParadoxRecord.Destroy;
begin
  // 不释放 FFieldDefs，因为它是由 TParadoxReader 拥有的
  inherited;
end;

function TParadoxRecord.GetFieldValue(FieldIndex: Integer): Variant;
var
  FieldDef: TParadoxFieldDef;
  Offset, Size: Integer;
  S: string;
  I: Integer;
  B: Boolean;
  D: TDateTime;
begin
  Result := Null;
  
  if (FieldIndex < 0) or (FieldIndex >= FFieldDefs.Count) then
    Exit;
    
  FieldDef := FFieldDefs[FieldIndex];
  Offset := FieldDef.Offset;
  Size := FieldDef.Size;
  
  // 检查是否有足够的数据
  if Offset + Size > Length(FData) then
    Exit;
    
  // 根据字段类型解析数据
  case FieldDef.FieldType of
    pftAlpha:
      begin
        SetLength(S, Size);
        for I := 0 to Size - 1 do
        begin
          if FData[Offset + I] = 0 then
            Break;
          S[I+1] := Char(FData[Offset + I]);
        end;
        Result := Trim(S);
      end;
      
    pftShort:
      begin
        if Size >= 2 then
          Result := SmallInt(FData[Offset] or (FData[Offset + 1] shl 8));
      end;
      
    pftLong:
      begin
        if Size >= 4 then
          Result := Integer(FData[Offset] or (FData[Offset + 1] shl 8) or 
                           (FData[Offset + 2] shl 16) or (FData[Offset + 3] shl 24));
      end;
      
    pftLogical:
      begin
        if Size >= 1 then
        begin
          B := FData[Offset] <> 0;
          Result := B;
        end;
      end;
      
    pftDate:
      begin
        if Size >= 4 then
        begin
          I := Integer(FData[Offset] or (FData[Offset + 1] shl 8) or 
                      (FData[Offset + 2] shl 16) or (FData[Offset + 3] shl 24));
          try
            D := EncodeDate(1900 + (I div 10000), (I div 100) mod 100, I mod 100);
            Result := D;
          except
            Result := Null;
          end;
        end;
      end;
      
    // 其他类型的处理可以根据需要添加
  end;
end;

procedure TParadoxRecord.SetFieldValue(FieldIndex: Integer; const Value: Variant);
var
  FieldDef: TParadoxFieldDef;
  Offset, Size: Integer;
  S: string;
  I, IntValue: Integer;
  Year, Month, Day: Word;
begin
  if (FieldIndex < 0) or (FieldIndex >= FFieldDefs.Count) then
    Exit;
    
  FieldDef := FFieldDefs[FieldIndex];
  Offset := FieldDef.Offset;
  Size := FieldDef.Size;
  
  // 检查是否有足够的空间
  if Offset + Size > Length(FData) then
    Exit;
    
  // 根据字段类型设置数据
  case FieldDef.FieldType of
    pftAlpha:
      begin
        if VarIsNull(Value) or VarIsEmpty(Value) then
        begin
          FillChar(FData[Offset], Size, 0);
        end
        else
        begin
          S := VarToStr(Value);
          FillChar(FData[Offset], Size, 0);
          for I := 1 to Min(Length(S), Size) do
            FData[Offset + I - 1] := Byte(S[I]);
        end;
      end;
      
    pftShort:
      begin
        if VarIsNull(Value) or VarIsEmpty(Value) then
        begin
          FillChar(FData[Offset], Size, 0);
        end
        else
        begin
          IntValue := Value;
          FData[Offset] := Byte(IntValue and $FF);
          FData[Offset + 1] := Byte((IntValue shr 8) and $FF);
        end;
      end;
      
    pftLong:
      begin
        if VarIsNull(Value) or VarIsEmpty(Value) then
        begin
          FillChar(FData[Offset], Size, 0);
        end
        else
        begin
          IntValue := Value;
          FData[Offset] := Byte(IntValue and $FF);
          FData[Offset + 1] := Byte((IntValue shr 8) and $FF);
          FData[Offset + 2] := Byte((IntValue shr 16) and $FF);
          FData[Offset + 3] := Byte((IntValue shr 24) and $FF);
        end;
      end;
      
    pftLogical:
      begin
        if VarIsNull(Value) or VarIsEmpty(Value) then
          FData[Offset] := 0
        else if Value then
          FData[Offset] := 1
        else
          FData[Offset] := 0;
      end;
      
    pftDate:
      begin
        if VarIsNull(Value) or VarIsEmpty(Value) then
        begin
          FillChar(FData[Offset], Size, 0);
        end
        else
        begin
          DecodeDate(VarToDateTime(Value), Year, Month, Day);
          IntValue := ((Year - 1900) * 10000) + (Month * 100) + Day;
          FData[Offset] := Byte(IntValue and $FF);
          FData[Offset + 1] := Byte((IntValue shr 8) and $FF);
          FData[Offset + 2] := Byte((IntValue shr 16) and $FF);
          FData[Offset + 3] := Byte((IntValue shr 24) and $FF);
        end;
      end;
      
    // 其他类型的处理可以根据需要添加
  end;
end;

{ TParadoxReader }

constructor TParadoxReader.Create(const AFileName: string);
begin
  FFileName := AFileName;
  FFieldDefs := TObjectList<TParadoxFieldDef>.Create(True);
  FModified := False;
  
  if not FileExists(FFileName) then
    raise EParadoxError.CreateFmt('Paradox文件不存在: %s', [FFileName]);
    
  try
    FFileStream := TFileStream.Create(FFileName, fmOpenReadWrite or fmShareDenyWrite);
    ReadHeader;
  except
    on E: Exception do
    begin
      FreeAndNil(FFileStream);
      raise EParadoxError.CreateFmt('无法打开Paradox文件: %s', [E.Message]);
    end;
  end;
end;

destructor TParadoxReader.Destroy;
begin
  if FModified then
    SaveChanges;
    
  FFieldDefs.Free;
  FFileStream.Free;
  inherited;
end;

procedure TParadoxReader.ReadHeader;
var
  HeaderBuffer: TBytes;
  RecordSizeBytes: Word;
  FieldCount: Integer;
  I, Offset, FieldSize: Integer;
  FieldType: Byte;
  FieldName: string;
  ParadoxFieldType: TParadoxFieldType;
begin
  // 读取文件头
  SetLength(HeaderBuffer, 2048); // 足够大的缓冲区来读取头部
  FFileStream.Position := 0;
  FFileStream.Read(HeaderBuffer[0], Length(HeaderBuffer));
  
  // 解析文件头
  FTableVersionID := HeaderBuffer[0]; // 表版本ID
  
  // 记录大小 (2字节)
  RecordSizeBytes := HeaderBuffer[2] or (HeaderBuffer[3] shl 8);
  FRecordSize := RecordSizeBytes;
  
  // 头部大小 (2字节)
  FHeaderSize := HeaderBuffer[4] or (HeaderBuffer[5] shl 8);
  
  // 记录数 (4字节)
  FRecordCount := HeaderBuffer[6] or (HeaderBuffer[7] shl 8) or 
                 (HeaderBuffer[8] shl 16) or (HeaderBuffer[9] shl 24);
  
  // 字段数 (2字节)
  FieldCount := HeaderBuffer[15];
  
  // 解析字段定义
  Offset := 0;
  for I := 0 to FieldCount - 1 do
  begin
    // 字段类型 (1字节)
    FieldType := HeaderBuffer[21 + I];
    
    // 字段大小 (根据类型确定)
    case FieldType of
      1: begin FieldSize := HeaderBuffer[47 + I]; ParadoxFieldType := pftAlpha; end;     // Alpha
      2: begin FieldSize := 4; ParadoxFieldType := pftDate; end;                         // Date
      3: begin FieldSize := 2; ParadoxFieldType := pftShort; end;                        // Short
      4: begin FieldSize := 4; ParadoxFieldType := pftLong; end;                         // Long
      5: begin FieldSize := 8; ParadoxFieldType := pftCurrency; end;                     // Currency
      6: begin FieldSize := 8; ParadoxFieldType := pftNumber; end;                       // Number
      7: begin FieldSize := 1; ParadoxFieldType := pftLogical; end;                      // Logical
      8: begin FieldSize := 8; ParadoxFieldType := pftMemoBLOB; end;                     // MemoBLOB
      9: begin FieldSize := 8; ParadoxFieldType := pftBLOB; end;                         // BLOB
      10: begin FieldSize := 8; ParadoxFieldType := pftFmtMemoBLOB; end;                 // FmtMemoBLOB
      11: begin FieldSize := 8; ParadoxFieldType := pftOLE; end;                         // OLE
      12: begin FieldSize := 8; ParadoxFieldType := pftGraphic; end;                     // Graphic
      14: begin FieldSize := 4; ParadoxFieldType := pftTime; end;                        // Time
      15: begin FieldSize := 8; ParadoxFieldType := pftTimestamp; end;                   // Timestamp
      16: begin FieldSize := 4; ParadoxFieldType := pftAutoInc; end;                     // AutoInc
      17: begin FieldSize := HeaderBuffer[47 + I]; ParadoxFieldType := pftBytes; end;    // Bytes
      18: begin FieldSize := 8; ParadoxFieldType := pftBCD; end;                         // BCD
      else
        begin FieldSize := 1; ParadoxFieldType := pftAlpha; end;                         // 未知类型
    end;
    
    // 字段名 (从偏移量261开始，每个字段名最多261字节)
    FieldName := '';
    for I := 0 to 25 do // 最多26个字符
    begin
      if HeaderBuffer[261 + (I * FieldCount) + I] = 0 then
        Break;
      FieldName := FieldName + Char(HeaderBuffer[261 + (I * FieldCount) + I]);
    end;
    
    // 创建字段定义
    FFieldDefs.Add(TParadoxFieldDef.Create(FieldName, ParadoxFieldType, FieldSize, Offset));
    
    // 更新下一个字段的偏移量
    Inc(Offset, FieldSize);
  end;
end;

function TParadoxReader.ReadRecordData(RecordIndex: Integer): TBytes;
var
  Data: TBytes;
begin
  if (RecordIndex < 0) or (RecordIndex >= FRecordCount) then
    raise EParadoxError.CreateFmt('记录索引超出范围: %d', [RecordIndex]);
    
  SetLength(Data, FRecordSize);
  FFileStream.Position := FHeaderSize + (RecordIndex * FRecordSize);
  FFileStream.Read(Data[0], FRecordSize);
  Result := Data;
end;

procedure TParadoxReader.WriteRecordData(RecordIndex: Integer; const Data: TBytes);
begin
  if (RecordIndex < 0) or (RecordIndex >= FRecordCount) then
    raise EParadoxError.CreateFmt('记录索引超出范围: %d', [RecordIndex]);
    
  if Length(Data) <> FRecordSize then
    raise EParadoxError.Create('记录数据大小不匹配');
    
  FFileStream.Position := FHeaderSize + (RecordIndex * FRecordSize);
  FFileStream.Write(Data[0], FRecordSize);
  FModified := True;
end;

function TParadoxReader.GetFieldCount: Integer;
begin
  Result := FFieldDefs.Count;
end;

function TParadoxReader.GetFieldDef(Index: Integer): TParadoxFieldDef;
begin
  if (Index < 0) or (Index >= FFieldDefs.Count) then
    raise EParadoxError.CreateFmt('字段索引超出范围: %d', [Index]);
    
  Result := FFieldDefs[Index];
end;

function TParadoxReader.GetFieldName(Index: Integer): string;
begin
  Result := GetFieldDef(Index).Name;
end;

function TParadoxReader.GetFieldType(Index: Integer): TParadoxFieldType;
begin
  Result := GetFieldDef(Index).FieldType;
end;

function TParadoxReader.GetFieldSize(Index: Integer): Integer;
begin
  Result := GetFieldDef(Index).Size;
end;

function TParadoxReader.GetRecordCount: Integer;
begin
  Result := FRecordCount;
end;

function TParadoxReader.ReadRecord(RecordIndex: Integer): TParadoxRecord;
var
  Data: TBytes;
begin
  Data := ReadRecordData(RecordIndex);
  Result := TParadoxRecord.Create(Data, FFieldDefs);
end;

procedure TParadoxReader.WriteRecord(RecordIndex: Integer; Record: TParadoxRecord);
begin
  WriteRecordData(RecordIndex, Record.FData);
end;

procedure TParadoxReader.SaveChanges;
begin
  if FModified then
  begin
    FFileStream.Flush;
    FModified := False;
  end;
end;

end.
