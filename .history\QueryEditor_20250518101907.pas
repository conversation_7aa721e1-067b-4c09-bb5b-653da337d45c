unit QueryEditor;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, Vcl.ComCtrls,
  Vcl.StdCtrls, Vcl.Grids, Vcl.DBGrids, Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Error, FireDAC.UI.Intf, FireDAC.Phys.Intf, FireDAC.Stan.Def,
  FireDAC.Stan.Pool, FireDAC.Stan.Async, FireDAC.Phys, FireDAC.Phys.SQLite,
  FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs, FireDAC.VCLUI.Wait,
  FireDAC.Comp.Client, FireDAC.Comp.DataSet, System.Actions, Vcl.ActnList,
  Vcl<PERSON>, <PERSON>cl<PERSON>, <PERSON><PERSON><PERSON><PERSON>, SQLExecutor;

type
  TfrmQueryEditor = class(TForm)
    pnlMain: TPanel;
    splHorizontal: TSplitter;
    pnlTop: TPanel;
    pnlBottom: TPanel;
    memSQL: TMemo;
    pcResults: TPageControl;
    tsData: TTabSheet;
    tsMessages: TTabSheet;
    dbgResults: TDBGrid;
    memMessages: TMemo;
    tbMain: TToolBar;
    btnExecute: TToolButton;
    btnExecuteScript: TToolButton;
    btnSep1: TToolButton;
    btnOpen: TToolButton;
    btnSave: TToolButton;
    btnSep2: TToolButton;
    btnExplain: TToolButton;
    alMain: TActionList;
    actExecute: TAction;
    actExecuteScript: TAction;
    actOpen: TAction;
    actSave: TAction;
    actExplain: TAction;
    dsResults: TDataSource;
    sbMain: TStatusBar;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure actExecuteExecute(Sender: TObject);
    procedure actExecuteScriptExecute(Sender: TObject);
    procedure actOpenExecute(Sender: TObject);
    procedure actSaveExecute(Sender: TObject);
    procedure actExplainExecute(Sender: TObject);
  private
    { Private declarations }
    FConnection: TFDConnection;
    FSQLExecutor: TSQLExecutor;

    procedure InitializeControls;
  public
    { Public declarations }
    procedure Initialize(AConnection: TFDConnection);
  end;

var
  frmQueryEditor: TfrmQueryEditor;

implementation

{$R *.dfm}

procedure TfrmQueryEditor.FormCreate(Sender: TObject);
begin
  InitializeControls;
end;

procedure TfrmQueryEditor.FormDestroy(Sender: TObject);
begin
  FSQLExecutor.Free;
end;

procedure TfrmQueryEditor.InitializeControls;
begin
  pcResults.ActivePage := tsData;
  memSQL.Lines.Clear;
  memMessages.Lines.Clear;
end;

procedure TfrmQueryEditor.Initialize(AConnection: TFDConnection);
begin
  FConnection := AConnection;

  if Assigned(FSQLExecutor) then
    FSQLExecutor.Free;

  FSQLExecutor := TSQLExecutor.Create(FConnection, memSQL, pcResults, sbMain,
    dsResults, dbgResults, memMessages);
end;

procedure TfrmQueryEditor.actExecuteExecute(Sender: TObject);
begin
  if Assigned(FSQLExecutor) then
    FSQLExecutor.ExecuteSQL;
end;

procedure TfrmQueryEditor.actExecuteScriptExecute(Sender: TObject);
begin
  if Assigned(FSQLExecutor) then
    FSQLExecutor.ExecuteScript;
end;

procedure TfrmQueryEditor.actExplainExecute(Sender: TObject);
begin
  if Assigned(FSQLExecutor) then
    FSQLExecutor.ExplainQuery;
end;

procedure TfrmQueryEditor.actOpenExecute(Sender: TObject);
var
  OpenDialog: TOpenDialog;
begin
  OpenDialog := TOpenDialog.Create(Self);
  try
    OpenDialog.Filter := 'SQL�ļ� (*.sql)|*.sql|�����ļ� (*.*)|*.*';
    OpenDialog.Title := '��SQL�ļ�';

    if OpenDialog.Execute then
    begin
      try
        memSQL.Lines.LoadFromFile(OpenDialog.FileName);
      except
        on E: Exception do
        begin
          ShowMessage('���ļ�ʧ��: ' + E.Message);
        end;
      end;
    end;
  finally
    OpenDialog.Free;
  end;
end;

procedure TfrmQueryEditor.actSaveExecute(Sender: TObject);
var
  SaveDialog: TSaveDialog;
begin
  SaveDialog := TSaveDialog.Create(Self);
  try
    SaveDialog.Filter := 'SQL�ļ� (*.sql)|*.sql|�����ļ� (*.*)|*.*';
    SaveDialog.Title := '����SQL�ļ�';
    SaveDialog.DefaultExt := 'sql';

    if SaveDialog.Execute then
    begin
      try
        memSQL.Lines.SaveToFile(SaveDialog.FileName);
      except
        on E: Exception do
        begin
          ShowMessage('�����ļ�ʧ��: ' + E.Message);
        end;
      end;
    end;
  finally
    SaveDialog.Free;
  end;
end;

end.
