function TDBConnectionManager.GetTableStructure(const ATableName: string): TFDQuery;
begin
  // 检查连接是否有效
  if not Assigned(FConnection) then
    raise Exception.Create('数据库连接未初始化');

  if not FConnection.Connected then
    raise Exception.Create('数据库连接已关闭');

  // 根据数据库类型获取表结构
  if IsSQLite then
  begin
    // 使用SQLite PRAGMA获取表结构
    Result := ExecuteQuery(Format('PRAGMA table_info("%s")', [ATableName]));
  end
  else if IsAccess or IsParadox then
  begin
    // 使用通用方法获取表结构
    try
      Result := ExecuteQuery(Format('SELECT * FROM [%s] WHERE 1=0', [ATableName]));
    except
      // 如果上面的查询失败，尝试不带方括号的查询
      Result := ExecuteQuery(Format('SELECT * FROM %s WHERE 1=0', [ATableName]));
    end;
  end
  else
  begin
    raise Exception.Create('不支持的数据库类型');
  end;
end;

function TDBConnectionManager.GetTableData(const ATableName: string): TFDQuery;
begin
  // 检查连接是否有效
  if not Assigned(FConnection) then
    raise Exception.Create('数据库连接未初始化');

  if not FConnection.Connected then
    raise Exception.Create('数据库连接已关闭');

  // 根据数据库类型获取表数据
  if IsSQLite then
  begin
    // 使用SQLite查询获取表数据
    Result := ExecuteQuery(Format('SELECT * FROM "%s"', [ATableName]));
  end
  else if IsAccess or IsParadox then
  begin
    // 使用通用方法获取表数据
    try
      Result := ExecuteQuery(Format('SELECT * FROM [%s]', [ATableName]));
    except
      // 如果上面的查询失败，尝试不带方括号的查询
      Result := ExecuteQuery(Format('SELECT * FROM %s', [ATableName]));
    end;
  end
  else
  begin
    raise Exception.Create('不支持的数据库类型');
  end;
end;

function TDBConnectionManager.ExecuteSQL(const ASQL: string): TFDQuery;
begin
  // 检查连接是否有效
  if not Assigned(FConnection) then
    raise Exception.Create('数据库连接未初始化');

  if not FConnection.Connected then
    raise Exception.Create('数据库连接已关闭');

  // 执行SQL查询
  Result := ExecuteQuery(ASQL);
end;

function TDBConnectionManager.GetDatabaseInfo: TStringList;
var
  Info: TStringList;
begin
  Info := TStringList.Create;
  Result := Info; // 初始化返回值

  try
    // 检查连接是否有效
    if not Assigned(FConnection) then
      Exit;

    if not FConnection.Connected then
      Exit;

    // 添加数据库类型信息
    case FDBType of
      dbtSQLite: Info.Add('数据库类型: SQLite');
      dbtAccess: Info.Add('数据库类型: Microsoft Access');
      dbtParadox: Info.Add('数据库类型: Paradox');
    else
      Info.Add('数据库类型: 未知');
    end;

    // 添加连接方式信息
    Info.Add(FConnectionInfo);

    // 添加数据库文件路径
    if IsSQLite then
    begin
      // 使用SQLite查询获取数据库文件路径
      try
        with ExecuteQuery('PRAGMA database_list') do
        begin
          while not Eof do
          begin
            Info.Add(Format('数据库文件: %s', [FieldByName('file').AsString]));
            Next;
          end;
          Free;
        end;
      except
        // 忽略错误
      end;
    end
    else
    begin
      // 使用连接参数获取数据库文件路径
      Info.Add(Format('数据库文件: %s', [FConnection.Params.Values['Database']]));
    end;

    // 添加SQLite版本信息
    if IsSQLite then
    begin
      try
        with ExecuteQuery('SELECT sqlite_version() as version') do
        begin
          if not Eof then
            Info.Add(Format('SQLite版本: %s', [FieldByName('version').AsString]));
          Free;
        end;
      except
        // 忽略错误
      end;
    end;

    // 添加编译选项信息
    if IsSQLite then
    begin
      try
        with ExecuteQuery('PRAGMA compile_options') do
        begin
          Info.Add('编译选项:');
          while not Eof do
          begin
            Info.Add('  ' + FieldByName('value').AsString);
            Next;
          end;
          Free;
        end;
      except
        // 忽略错误
      end;
    end;
  except
    // 忽略所有错误，返回当前收集到的信息
  end;

  Result := Info;
end;

end.
