object frmMain: TfrmMain
  Left = 0
  Top = 0
  Caption = 'SQLite'#25968#25454#24211#24037#20855
  ClientHeight = 600
  ClientWidth = 900
  Color = clBtnFace
  Font.Charset = DEFAULT_CHARSET
  Font.Color = clWindowText
  Font.Height = -12
  Font.Name = 'Segoe UI'
  Font.Style = []
  Menu = mmMain
  Position = poScreenCenter
  OnCreate = FormCreate
  OnDestroy = FormDestroy
  TextHeight = 15
  object pnlMain: TPanel
    Left = 0
    Top = 26
    Width = 900
    Height = 555
    Align = alClient
    BevelOuter = bvNone
    TabOrder = 0
    object splVertical: TSplitter
      Left = 250
      Top = 0
      Height = 555
      ExplicitLeft = 200
      ExplicitTop = 232
      ExplicitHeight = 100
    end
    object pnlLeft: TPanel
      Left = 0
      Top = 0
      Width = 250
      Height = 555
      Align = alLeft
      BevelOuter = bvNone
      TabOrder = 0
      object tvDatabases: TTreeView
        Left = 0
        Top = 0
        Width = 250
        Height = 555
        Align = alClient
        Indent = 19
        ReadOnly = True
        TabOrder = 0
        OnDblClick = tvDatabasesDblClick
      end
    end
    object pcRight: TPageControl
      Left = 253
      Top = 0
      Width = 647
      Height = 555
      ActivePage = tsData
      Align = alClient
      TabOrder = 1
      object tsData: TTabSheet
        Caption = #25968#25454
        object dbgData: TDBGrid
          Left = 0
          Top = 0
          Width = 639
          Height = 525
          Align = alClient
          DataSource = dsData
          Options = [dgTitles, dgIndicator, dgColumnResize, dgColLines, dgRowLines, dgTabs, dgConfirmDelete, dgCancelOnExit, dgTitleClick, dgTitleHotTrack]
          TabOrder = 0
          TitleFont.Charset = DEFAULT_CHARSET
          TitleFont.Color = clWindowText
          TitleFont.Height = -12
          TitleFont.Name = 'Segoe UI'
          TitleFont.Style = []
        end
      end
    end
  end
  object sbMain: TStatusBar
    Left = 0
    Top = 581
    Width = 900
    Height = 19
    Panels = <>
    SimplePanel = True
    SimpleText = #26410#36830#25509
  end
  object tbMain: TToolBar
    Left = 0
    Top = 0
    Width = 900
    Height = 26
    Caption = 'tbMain'
    Images = ilMain
    ShowHint = True
    TabOrder = 2
    object btnConnect: TToolButton
      Left = 0
      Top = 0
      Action = actConnect
    end
    object btnDisconnect: TToolButton
      Left = 23
      Top = 0
      Action = actDisconnect
    end
    object btnExportData: TToolButton
      Left = 46
      Top = 0
      Action = actExportData
    end
    object btnImportData: TToolButton
      Left = 69
      Top = 0
      Action = actImportData
    end
    object btnRefreshData: TToolButton
      Left = 92
      Top = 0
      Action = actRefreshData
    end
    object btnModifyData: TToolButton
      Left = 115
      Top = 0
      Action = actModifyData
    end
    object btnRefreshDB: TToolButton
      Left = 138
      Top = 0
      Action = actRefreshDB
    end
    object btnSearchDB: TToolButton
      Left = 161
      Top = 0
      Action = actSearchDB
    end
    object btnConvertDB: TToolButton
      Left = 184
      Top = 0
      Action = actConvertDB
    end
  end
  object mmMain: TMainMenu
    Left = 48
    Top = 72
    object miFile: TMenuItem
      Caption = #25991#20214'(&F)'
      object miConnect: TMenuItem
        Action = actConnect
      end
      object miDisconnect: TMenuItem
        Action = actDisconnect
      end
      object N1: TMenuItem
        Caption = '-'
      end
      object miExit: TMenuItem
        Action = actExit
      end
    end
    object miEdit: TMenuItem
      Caption = #32534#36753'(&E)'
    end
    object miHelp: TMenuItem
      Caption = #24110#21161'(&H)'
      object miAbout: TMenuItem
        Caption = #20851#20110'(&A)...'
      end
    end
  end
  object ilMain: TImageList
    Left = 48
    Top = 128
  end
  object alMain: TActionList
    Images = ilMain
    Left = 48
    Top = 184
    object actConnect: TAction
      Caption = #36830#25509'(&C)'
      Hint = #36830#25509#21040#25968#25454#24211
      OnExecute = actConnectExecute
    end
    object actDisconnect: TAction
      Caption = #26029#24320#36830#25509'(&D)'
      Hint = #26029#24320#25968#25454#24211#36830#25509
      OnExecute = actDisconnectExecute
    end
    object actExit: TAction
      Caption = #36864#20986'(&X)'
      Hint = #36864#20986#31243#24207
      OnExecute = actExitExecute
    end
    object actExportData: TAction
      Caption = #23548#20986#25968#25454'(&E)'
      Hint = #23548#20986#34920#25968#25454#21040#25991#20214
      OnExecute = actExportDataExecute
    end
    object actImportData: TAction
      Caption = #23548#20837#25968#25454'(&I)'
      Hint = #20174#25991#20214#23548#20837#25968#25454
      OnExecute = actImportDataExecute
    end
    object actRefreshData: TAction
      Caption = #21047#26032#25968#25454'(&R)'
      Hint = #21047#26032#24403#21069#34920#25968#25454
      OnExecute = actRefreshDataExecute
    end
    object actModifyData: TAction
      Caption = #20462#25913#25968#25454'(&M)'
      Hint = #20462#25913#34920#25968#25454
      OnExecute = actModifyDataExecute
    end
    object actRefreshDB: TAction
      Caption = #21047#26032#25968#25454#24211'(&F)'
      Hint = #21047#26032#25968#25454#24211#32467#26500
      OnExecute = actRefreshDBExecute
    end
    object actSearchDB: TAction
      Caption = #25628#32034#25968#25454#24211'(&S)'
      Hint = #22312#25968#25454#24211#20013#25628#32034#20869#23481
      OnExecute = actSearchDBExecute
    end
    object actConvertDB: TAction
      Caption = #36716#25442#25968#25454#24211'(&C)'
      Hint = #36716#25442#25968#25454#24211#26684#24335
      OnExecute = actConvertDBExecute
    end
  end
  object dsData: TDataSource
    Left = 120
    Top = 184
  end
  object pmTreeView: TPopupMenu
    Left = 120
    Top = 72
    object miExportData: TMenuItem
      Action = actExportData
    end
    object miImportData: TMenuItem
      Action = actImportData
    end
    object miRefreshData: TMenuItem
      Action = actRefreshData
    end
    object miModifyData: TMenuItem
      Action = actModifyData
    end
    object miRefreshDB: TMenuItem
      Action = actRefreshDB
    end
    object miSearchDB: TMenuItem
      Action = actSearchDB
    end
    object miConvertDB: TMenuItem
      Action = actConvertDB
    end
  end
  object pmDBGrid: TPopupMenu
    Left = 120
    Top = 128
  end
end
