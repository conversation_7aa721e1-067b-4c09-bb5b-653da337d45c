program NewDBTool;

{$CODEPAGE 936}

uses
  Vcl.Forms,
  MainForm in 'MainForm.pas' {frmMain},
  DBConnection in 'DBConnection.pas',
  SQLExecutor in 'SQLExecutor.pas',
  TableBrowser in 'TableBrowser.pas' {frmTableBrowser},
  QueryEditor in 'QueryEditor.pas' {frmQueryEditor},
  DataViewer in 'DataViewer.pas' {frmDataViewer},
  StructureManager in 'StructureManager.pas' {frmStructureManager},
  DataTransfer in 'DataTransfer.pas' {frmDataTransfer},
  Settings in 'Settings.pas' {frmSettings};

{$R *.res}

begin
  Application.Initialize;
  Application.MainFormOnTaskbar := True;
  Application.CreateForm(TfrmMain, frmMain);
  Application.Run;
end.
