unit DataViewer;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, Vcl.ComCtrls,
  Vcl.StdCtrls, Vcl.Grids, Vcl.DBGrids, Vcl.DBCtrls, Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Error, FireDAC.UI.Intf, FireDAC.Phys.Intf, FireDAC.Stan.Def,
  FireDAC.Stan.Pool, FireDAC.Stan.Async, FireDAC.Phys, FireDAC.Phys.SQLite,
  FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs, FireDAC.VCLUI.Wait,
  FireDAC.Comp.Client, FireDAC.Comp.DataSet, System.Actions, Vcl.ActnList,
  <PERSON>c<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, FireDAC.Comp.UI, FireDAC.DApt;

type
  TfrmDataViewer = class(TForm)
    pnlMain: TPanel;
    navData: TDBNavigator;
    dbgData: TDBGrid;
    dsData: TDataSource;
    alMain: TActionList;
    actRefresh: TAction;
    actPost: TAction;
    actCancel: TAction;
    actExport: TAction;
    actFilter: TAction;
    tbMain: TToolBar;
    btnRefresh: TToolButton;
    btnSep1: TToolButton;
    btnPost: TToolButton;
    btnCancel: TToolButton;
    btnSep2: TToolButton;
    btnExport: TToolButton;
    btnFilter: TToolButton;
    pnlFilter: TPanel;
    lblFilter: TLabel;
    edtFilter: TEdit;
    btnApplyFilter: TButton;
    btnClearFilter: TButton;
    pmData: TPopupMenu;
    miRefresh: TMenuItem;
    miPost: TMenuItem;
    miCancel: TMenuItem;
    N1: TMenuItem;
    miExport: TMenuItem;
    miFilter: TMenuItem;
    sbMain: TStatusBar;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure actRefreshExecute(Sender: TObject);
    procedure actPostExecute(Sender: TObject);
    procedure actCancelExecute(Sender: TObject);
    procedure actExportExecute(Sender: TObject);
    procedure actFilterExecute(Sender: TObject);
    procedure btnApplyFilterClick(Sender: TObject);
    procedure btnClearFilterClick(Sender: TObject);
  private
    { Private declarations }
    FConnection: TFDConnection;
    FQuery: TFDQuery;
    FTableName: string;

    procedure LoadData;
    procedure ApplyFilter;
  public
    { Public declarations }
    procedure Initialize(AConnection: TFDConnection; const ATableName: string);
  end;

var
  frmDataViewer: TfrmDataViewer;

implementation

{$R *.dfm}

uses
  DataTransfer;

procedure TfrmDataViewer.FormCreate(Sender: TObject);
begin
  FQuery := TFDQuery.Create(Self);
  dsData.DataSet := FQuery;
  pnlFilter.Visible := False;
end;

procedure TfrmDataViewer.FormDestroy(Sender: TObject);
begin
  FQuery.Free;
end;

procedure TfrmDataViewer.Initialize(AConnection: TFDConnection; const ATableName: string);
begin
  FConnection := AConnection;
  FTableName := ATableName;

  Caption := '���ݲ鿴�� - ' + FTableName;

  FQuery.Connection := FConnection;

  LoadData;
end;

procedure TfrmDataViewer.LoadData;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  try
    FQuery.Close;
    FQuery.SQL.Text := Format('SELECT * FROM %s', [FTableName]);
    FQuery.Open;

    // ���ÿɱ༭
    FQuery.UpdateOptions.UpdateTableName := FTableName;
    FQuery.UpdateOptions.KeyFields := ''; // ��Ҫ���������ֶ�

    // ���Ի�ȡ������Ϣ
    var PragmaQuery := TFDQuery.Create(nil);
    try
      PragmaQuery.Connection := FConnection;
      PragmaQuery.SQL.Text := Format('PRAGMA table_info(%s)', [FTableName]);
      PragmaQuery.Open;

      var KeyFields := '';
      while not PragmaQuery.Eof do
      begin
        if PragmaQuery.FieldByName('pk').AsInteger = 1 then
        begin
          if KeyFields <> '' then
            KeyFields := KeyFields + ';';
          KeyFields := KeyFields + PragmaQuery.FieldByName('name').AsString;
        end;
        PragmaQuery.Next;
      end;

      if KeyFields <> '' then
        FQuery.UpdateOptions.KeyFields := KeyFields;
    finally
      PragmaQuery.Free;
    end;

    sbMain.SimpleText := Format('��: %s����¼��: %d', [FTableName, FQuery.RecordCount]);
  except
    on E: Exception do
    begin
      ShowMessage('��������ʧ��: ' + E.Message);
    end;
  end;
end;

procedure TfrmDataViewer.actRefreshExecute(Sender: TObject);
begin
  LoadData;
end;

procedure TfrmDataViewer.actPostExecute(Sender: TObject);
begin
  if FQuery.State in [dsEdit, dsInsert] then
  begin
    try
      FQuery.Post;
      sbMain.SimpleText := '�����ѱ���';
    except
      on E: Exception do
      begin
        ShowMessage('��������ʧ��: ' + E.Message);
      end;
    end;
  end;
end;

procedure TfrmDataViewer.actCancelExecute(Sender: TObject);
begin
  if FQuery.State in [dsEdit, dsInsert] then
  begin
    FQuery.Cancel;
    sbMain.SimpleText := '�༭��ȡ��';
  end;
end;

procedure TfrmDataViewer.actExportExecute(Sender: TObject);
begin
  // ����DataTransfer��Ԫ��ʵ��
end;

procedure TfrmDataViewer.actFilterExecute(Sender: TObject);
begin
  pnlFilter.Visible := not pnlFilter.Visible;

  if pnlFilter.Visible then
    edtFilter.SetFocus;
end;

procedure TfrmDataViewer.ApplyFilter;
var
  FilterStr: string;
begin
  FilterStr := Trim(edtFilter.Text);

  if FilterStr = '' then
  begin
    LoadData;
    Exit;
  end;

  try
    FQuery.Close;
    FQuery.SQL.Text := Format('SELECT * FROM %s WHERE %s', [FTableName, FilterStr]);
    FQuery.Open;

    sbMain.SimpleText := Format('��: %s�����˺��¼��: %d', [FTableName, FQuery.RecordCount]);
  except
    on E: Exception do
    begin
      ShowMessage('Ӧ�ù�����ʧ��: ' + E.Message);
      LoadData; // ���¼���ԭʼ����
    end;
  end;
end;

procedure TfrmDataViewer.btnApplyFilterClick(Sender: TObject);
begin
  ApplyFilter;
end;

procedure TfrmDataViewer.btnClearFilterClick(Sender: TObject);
begin
  edtFilter.Text := '';
  LoadData;
end;

end.
