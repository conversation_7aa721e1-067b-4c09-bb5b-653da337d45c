unit Settings;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, Vcl.ComCtrls,
  Vcl.StdCtrls, System.IniFiles;

type
  TfrmSettings = class(TForm)
    pnlMain: TPanel;
    pcSettings: TPageControl;
    tsGeneral: TTabSheet;
    tsDisplay: TTabSheet;
    pnlButtons: TPanel;
    btnOK: TButton;
    btnCancel: TButton;
    btnApply: TButton;
    grpGeneral: TGroupBox;
    chkAutoConnect: TCheckBox;
    lblLastDB: TLabel;
    edtLastDB: TEdit;
    btnBrowseLastDB: TButton;
    chkConfirmExit: TCheckBox;
    grpDisplay: TGroupBox;
    lblFontSize: TLabel;
    edtFontSize: TEdit;
    udFontSize: TUpDown;
    chkShowGrid: TCheckBox;
    chkShowHeaders: TCheckBox;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure btnOKClick(Sender: TObject);
    procedure btnCancelClick(Sender: TObject);
    procedure btnApplyClick(Sender: TObject);
    procedure btnBrowseLastDBClick(Sender: TObject);
  private
    { Private declarations }
    FIniFile: TIniFile;
    
    procedure LoadSettings;
    procedure SaveSettings;
  public
    { Public declarations }
  end;

var
  frmSettings: TfrmSettings;

implementation

{$R *.dfm}

procedure TfrmSettings.FormCreate(Sender: TObject);
begin
  FIniFile := TIniFile.Create(ChangeFileExt(Application.ExeName, '.ini'));
  LoadSettings;
end;

procedure TfrmSettings.FormDestroy(Sender: TObject);
begin
  FIniFile.Free;
end;

procedure TfrmSettings.LoadSettings;
begin
  // 常规设置
  chkAutoConnect.Checked := FIniFile.ReadBool('General', 'AutoConnect', False);
  edtLastDB.Text := FIniFile.ReadString('General', 'LastDatabase', '');
  chkConfirmExit.Checked := FIniFile.ReadBool('General', 'ConfirmExit', True);
  
  // 显示设置
  udFontSize.Position := FIniFile.ReadInteger('Display', 'FontSize', 10);
  chkShowGrid.Checked := FIniFile.ReadBool('Display', 'ShowGrid', True);
  chkShowHeaders.Checked := FIniFile.ReadBool('Display', 'ShowHeaders', True);
end;

procedure TfrmSettings.SaveSettings;
begin
  // 常规设置
  FIniFile.WriteBool('General', 'AutoConnect', chkAutoConnect.Checked);
  FIniFile.WriteString('General', 'LastDatabase', edtLastDB.Text);
  FIniFile.WriteBool('General', 'ConfirmExit', chkConfirmExit.Checked);
  
  // 显示设置
  FIniFile.WriteInteger('Display', 'FontSize', udFontSize.Position);
  FIniFile.WriteBool('Display', 'ShowGrid', chkShowGrid.Checked);
  FIniFile.WriteBool('Display', 'ShowHeaders', chkShowHeaders.Checked);
  
  FIniFile.UpdateFile;
end;

procedure TfrmSettings.btnOKClick(Sender: TObject);
begin
  SaveSettings;
  ModalResult := mrOk;
end;

procedure TfrmSettings.btnCancelClick(Sender: TObject);
begin
  ModalResult := mrCancel;
end;

procedure TfrmSettings.btnApplyClick(Sender: TObject);
begin
  SaveSettings;
end;

procedure TfrmSettings.btnBrowseLastDBClick(Sender: TObject);
var
  OpenDialog: TOpenDialog;
begin
  OpenDialog := TOpenDialog.Create(Self);
  try
    OpenDialog.Filter := 'SQLite数据库 (*.db;*.sqlite;*.sqlite3)|*.db;*.sqlite;*.sqlite3|所有文件 (*.*)|*.*';
    OpenDialog.Title := '选择默认数据库';
    
    if OpenDialog.Execute then
    begin
      edtLastDB.Text := OpenDialog.FileName;
    end;
  finally
    OpenDialog.Free;
  end;
end;

end.
