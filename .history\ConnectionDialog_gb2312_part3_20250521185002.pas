  // 直接从INI文件读取配置，避免编码问题
  IniFile := TIniFile.Create(ConfigIniPath);
  try
    // 读取数据库类型配置
    DBType := IniFile.ReadString('Database', 'DBType', '');
    UseAccessDB := IniFile.ReadInteger('Database', 'UseAccessDB', 0);

    // 首先检查是否是SQLite数据库
    if DBType = 'SQLite' then
    begin
      // 读取SQLite数据库文件路径
      DBFile := IniFile.ReadString('Database', 'DBFile', '');

      if DBFile = '' then
      begin
        // 尝试在默认位置查找SQLite数据库
        DBPath := MirServerPath + 'Mud2\DB\';

        if DirectoryExists(DBPath) then
        begin
          if FileExists(DBPath + 'mir2.db') then
            DBFile := DBPath + 'mir2.db'
          else if FileExists(DBPath + 'game.db') then
            DBFile := DBPath + 'game.db';
        end;
      end
      else
      begin
        // 如果DBFile是相对路径，转换为绝对路径
        if not FileExists(DBFile) and (Length(DBFile) > 0) then
        begin
          // 首先检查是否是绝对路径
          if (DBFile[1] = PathDelim) or ((Length(DBFile) > 2) and (DBFile[2] = ':')) then
          begin
            // 已经是绝对路径，但文件不存在
            ShowMessage('Config.ini中指定的SQLite数据库文件不存在: ' + DBFile);
            Exit(False);
          end
          else
          begin
            // 尝试相对于MirServer根目录
            if FileExists(MirServerPath + DBFile) then
              DBFile := MirServerPath + DBFile
            // 尝试相对于Mud2\DB目录
            else if FileExists(MirServerPath + 'Mud2\DB\' + DBFile) then
              DBFile := MirServerPath + 'Mud2\DB\' + DBFile
            // 尝试相对于当前目录
            else if FileExists(ExtractFilePath(ParamStr(0)) + DBFile) then
              DBFile := ExtractFilePath(ParamStr(0)) + DBFile
            else
            begin
              ShowMessage('无法找到Config.ini中指定的SQLite数据库文件: ' + DBFile);
              Exit(False);
            end;
          end;
        end;
      end;

      if (DBFile <> '') and FileExists(DBFile) then
      begin
        // 连接SQLite数据库
        if ConnectToSQLite(DBFile) then
        begin
          FAutoDetected := True;
          Result := True;
          Exit; // 成功连接后立即退出
        end;
      end
      else
      begin
        ShowMessage('未找到有效的SQLite数据库文件');
        Exit(False);
      end;
    end
    // 然后检查是否是Access数据库
    else if UseAccessDB = 1 then
    begin
      // 读取Access数据库文件路径
      AccessFileName := IniFile.ReadString('Database', 'AccessFileName', '');

      if AccessFileName = '' then
      begin
        // 尝试在默认位置查找Access数据库
        DBPath := MirServerPath + 'Mud2\DB\';

        if DirectoryExists(DBPath) then
        begin
          if FileExists(DBPath + 'Mir2.mdb') then
            AccessFileName := DBPath + 'Mir2.mdb'
          else if FileExists(DBPath + 'GameData.mdb') then
            AccessFileName := DBPath + 'GameData.mdb';
        end;
      end
      else
      begin
        // 如果AccessFileName是相对路径，转换为绝对路径
        if not FileExists(AccessFileName) and (Length(AccessFileName) > 0) then
        begin
          // 首先检查是否是绝对路径
          if (AccessFileName[1] = PathDelim) or ((Length(AccessFileName) > 2) and (AccessFileName[2] = ':')) then
          begin
            // 已经是绝对路径，但文件不存在
            ShowMessage('Config.ini中指定的Access数据库文件不存在: ' + AccessFileName);
            Exit(False);
          end
          else
          begin
            // 尝试相对于MirServer根目录
            if FileExists(MirServerPath + AccessFileName) then
              AccessFileName := MirServerPath + AccessFileName
            // 尝试相对于Mud2\DB目录
            else if FileExists(MirServerPath + 'Mud2\DB\' + AccessFileName) then
              AccessFileName := MirServerPath + 'Mud2\DB\' + AccessFileName
            // 尝试相对于当前目录
            else if FileExists(ExtractFilePath(ParamStr(0)) + AccessFileName) then
              AccessFileName := ExtractFilePath(ParamStr(0)) + AccessFileName
            else
            begin
              ShowMessage('无法找到Config.ini中指定的Access数据库文件: ' + AccessFileName);
              Exit(False);
            end;
          end;
        end;
      end;

      if (AccessFileName <> '') and FileExists(AccessFileName) then
      begin
        // 连接Access数据库
        if ConnectToAccess(AccessFileName) then
        begin
          FAutoDetected := True;
          Result := True;
          Exit; // 成功连接后立即退出
        end;
      end
      else
      begin
        ShowMessage('未找到有效的Access数据库文件');
        Exit(False);
      end;
    end
    // 最后默认使用Paradox数据库
    else
    begin
      // 使用Paradox数据库
      DBPath := MirServerPath + 'Mud2\DB\';

      if DirectoryExists(DBPath) then
      begin
        // 连接Paradox数据库
        if ConnectToParadox(DBPath) then
        begin
          FAutoDetected := True;
          Result := True;
          Exit; // 成功连接后立即退出
        end;
      end
      else
      begin
        ShowMessage('未找到Paradox数据库目录: ' + DBPath);
        Exit(False);
      end;
    end;
  finally
    IniFile.Free;
  end;
end;
