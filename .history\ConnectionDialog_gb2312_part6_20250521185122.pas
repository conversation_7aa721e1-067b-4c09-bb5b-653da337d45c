    // 方法2：使用不同的ADO提供程序
    try
      ADOConnection := TADOConnection.Create(nil);
      try
        // 尝试使用不同的提供程序
        ConnectionString := 'Provider=MSDASQL.1;Extended Properties="Driver={Microsoft Paradox Driver (*.db)};DriverID=538;Fil=Paradox 5.X;DefaultDir=' + Directory + ';Dbq=' + Directory + ';CollatingSequence=ASCII;"';

        ADOConnection.ConnectionString := ConnectionString;
        ADOConnection.LoginPrompt := False;
        ADOConnection.Open;

        // 如果ADO连接成功，使用FireDAC的ODBC连接
        FConnection.Params.Clear;
        FConnection.Params.DriverID := 'ODBC';
        FConnection.Params.Add('ConnectionString=' + ConnectionString);

        // 设置字符串支持
        FConnection.FormatOptions.MapRules.Clear;
        FConnection.FormatOptions.OwnMapRules := True;
        FConnection.FormatOptions.StrsEmpty2Null := False;
        FConnection.FormatOptions.StrsTrim := False;

        // 设置字符集
        FConnection.Params.Add('CharacterSet=gb2312');

        FConnection.Open;
        Result := True;
        Exit;
      finally
        ADOConnection.Free;
      end;
    except
      on E: Exception do
      begin
        ErrorMsg := ErrorMsg + #13#10 + E.Message;
        // 如果方法2失败，尝试方法3
      end;
    end;

    // 方法3：直接使用FireDAC的ODBC连接，尝试多种连接字符串格式
    try
      // 尝试格式1
      FConnection.Params.Clear;
      FConnection.Params.DriverID := 'ODBC';
      FConnection.Params.Add('Database=' + Directory);

      // 如果检测到ODBC驱动程序已安装，使用具体的驱动名称
      if ODBCDriverInstalled then
        FConnection.Params.Add('DriverName=Microsoft Paradox Driver (*.db)')
      else
        FConnection.Params.Add('DriverName=Paradox');

      // 设置字符串支持
      FConnection.FormatOptions.MapRules.Clear;
      FConnection.FormatOptions.OwnMapRules := True;
      FConnection.FormatOptions.StrsEmpty2Null := False;
      FConnection.FormatOptions.StrsTrim := False;

      // 设置字符集
      FConnection.Params.Add('CharacterSet=gb2312');

      FConnection.Open;
      Result := True;
      Exit;
    except
      on E: Exception do
      begin
        ErrorMsg := ErrorMsg + #13#10 + E.Message;
        // 如果格式1失败，尝试格式2
        try
          FConnection.Params.Clear;
          FConnection.Params.DriverID := 'ODBC';
          FConnection.Params.Add('ConnectionString=Driver={Microsoft Paradox Driver (*.db)};DefaultDir=' + Directory + ';');

          // 设置字符串支持
          FConnection.FormatOptions.MapRules.Clear;
          FConnection.FormatOptions.OwnMapRules := True;
          FConnection.FormatOptions.StrsEmpty2Null := False;
          FConnection.FormatOptions.StrsTrim := False;

          // 设置字符集
          FConnection.Params.Add('CharacterSet=gb2312');

          FConnection.Open;
          Result := True;
          Exit;
        except
          on E2: Exception do
          begin
            ErrorMsg := ErrorMsg + #13#10 + E2.Message;
            // 如果格式2失败，尝试格式3
            try
              FConnection.Params.Clear;
              FConnection.Params.DriverID := 'ODBC';
              FConnection.Params.Add('ConnectionString=Driver={Microsoft Paradox Driver (*.db)};DriverID=538;Fil=Paradox 5.X;DefaultDir=' + Directory + ';Dbq=' + Directory + ';');

              // 设置字符串支持
              FConnection.FormatOptions.MapRules.Clear;
              FConnection.FormatOptions.OwnMapRules := True;
              FConnection.FormatOptions.StrsEmpty2Null := False;
              FConnection.FormatOptions.StrsTrim := False;

              // 设置字符集
              FConnection.Params.Add('CharacterSet=gb2312');

              FConnection.Open;
              Result := True;
              Exit;
            except
              on E3: Exception do
              begin
                ErrorMsg := ErrorMsg + #13#10 + E3.Message;
                // 如果格式3失败，尝试格式4
                try
                  FConnection.Params.Clear;
                  FConnection.Params.DriverID := 'ODBC';
                  FConnection.Params.Add('ConnectionString=DSN=MS Paradox Driver;DBQ=' + Directory + ';DefaultDir=' + Directory + ';DriverID=538;FIL=Paradox 5.X;');

                  // 设置字符串支持
                  FConnection.FormatOptions.MapRules.Clear;
                  FConnection.FormatOptions.OwnMapRules := True;
                  FConnection.FormatOptions.StrsEmpty2Null := False;
                  FConnection.FormatOptions.StrsTrim := False;

                  // 设置字符集
                  FConnection.Params.Add('CharacterSet=gb2312');

                  FConnection.Open;
                  Result := True;
                  Exit;
                except
                  on E4: Exception do
                  begin
                    // 所有方法都失败，显示详细错误信息
                    ShowMessage('连接Paradox数据库失败: ' + #13#10 +
                               '找到的数据库文件: ' + FoundDBFiles.CommaText + #13#10 +
                               '错误信息: ' + E4.Message + #13#10 +
                               '请确认已安装Paradox ODBC驱动程序' + #13#10 +
                               '您可以尝试在Windows控制面板中的"ODBC数据源管理器"中配置Paradox驱动程序');
                  end;
                end;
              end;
            end;
          end;
        end;
      end;
    end;
  except
    on E: Exception do
    begin
      ShowMessage('连接Paradox数据库失败: ' + E.Message);
    end;
  end;

  FoundDBFiles.Free;
end;
