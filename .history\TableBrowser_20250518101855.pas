unit TableBrowser;

interface

uses
  Winapi.Windows, Winapi.Messages, System.SysUtils, System.Variants, System.Classes,
  Vcl.Graphics, Vcl.Controls, Vcl.Forms, Vcl.Dialogs, Vcl.ExtCtrls, Vcl.ComCtrls,
  Vcl.StdCtrls, Vcl.Grids, Vcl.DBGrids, Data.DB, FireDAC.Stan.Intf, FireDAC.Stan.Option,
  FireDAC.Stan.Error, FireDAC.UI.Intf, FireDAC.Phys.Intf, FireDAC.Stan.Def,
  FireDAC.Stan.Pool, FireDAC.Stan.Async, FireDAC.Phys, FireDAC.Phys.SQLite,
  FireDAC.Phys.SQLiteDef, FireDAC.Stan.ExprFuncs, FireDAC.VCLUI.Wait,
  FireDAC.Comp.Client, FireDAC.Comp.DataSet, System.Actions, Vcl.ActnList,
  Vcl.<PERSON>, Vcl.<PERSON>, Vcl.Menus;

type
  TfrmTableBrowser = class(TForm)
    pnlMain: TPanel;
    pcMain: TPageControl;
    tsData: TTabSheet;
    tsStructure: TTabSheet;
    tsIndices: TTabSheet;
    tsTriggers: TTabSheet;
    pnlTop: TPanel;
    lblTable: TLabel;
    edtFilter: TEdit;
    btnApplyFilter: TButton;
    dbgData: TDBGrid;
    lvStructure: TListView;
    lvIndices: TListView;
    lvTriggers: TListView;
    dsData: TDataSource;
    alMain: TActionList;
    actRefresh: TAction;
    actExport: TAction;
    actImport: TAction;
    actFilter: TAction;
    tbMain: TToolBar;
    btnRefresh: TToolButton;
    btnExport: TToolButton;
    btnImport: TToolButton;
    btnSep1: TToolButton;
    btnFilter: TToolButton;
    pmData: TPopupMenu;
    miRefresh: TMenuItem;
    miExport: TMenuItem;
    miImport: TMenuItem;
    N1: TMenuItem;
    miFilter: TMenuItem;
    procedure FormCreate(Sender: TObject);
    procedure FormDestroy(Sender: TObject);
    procedure actRefreshExecute(Sender: TObject);
    procedure actExportExecute(Sender: TObject);
    procedure actImportExecute(Sender: TObject);
    procedure actFilterExecute(Sender: TObject);
    procedure btnApplyFilterClick(Sender: TObject);
  private
    { Private declarations }
    FConnection: TFDConnection;
    FTableName: string;
    FQuery: TFDQuery;

    procedure LoadTableData;
    procedure LoadTableStructure;
    procedure LoadTableIndices;
    procedure LoadTableTriggers;
    procedure ApplyFilter;
  public
    { Public declarations }
    procedure Initialize(AConnection: TFDConnection; const ATableName: string);
  end;

var
  frmTableBrowser: TfrmTableBrowser;

implementation

{$R *.dfm}

uses
  DataTransfer;

procedure TfrmTableBrowser.FormCreate(Sender: TObject);
begin
  FQuery := TFDQuery.Create(Self);
  dsData.DataSet := FQuery;
end;

procedure TfrmTableBrowser.FormDestroy(Sender: TObject);
begin
  FQuery.Free;
end;

procedure TfrmTableBrowser.Initialize(AConnection: TFDConnection; const ATableName: string);
begin
  FConnection := AConnection;
  FTableName := ATableName;

  Caption := '������� - ' + FTableName;
  lblTable.Caption := '��: ' + FTableName;

  FQuery.Connection := FConnection;

  LoadTableData;
  LoadTableStructure;
  LoadTableIndices;
  LoadTableTriggers;
end;

procedure TfrmTableBrowser.LoadTableData;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  try
    FQuery.Close;
    FQuery.SQL.Text := Format('SELECT * FROM %s', [FTableName]);
    FQuery.Open;
  except
    on E: Exception do
    begin
      ShowMessage('���ر�����ʧ��: ' + E.Message);
    end;
  end;
end;

procedure TfrmTableBrowser.LoadTableStructure;
var
  Query: TFDQuery;
  Item: TListItem;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  lvStructure.Items.Clear;

  Query := TFDQuery.Create(nil);
  try
    Query.Connection := FConnection;
    Query.SQL.Text := Format('PRAGMA table_info(%s)', [FTableName]);

    try
      Query.Open;

      while not Query.Eof do
      begin
        Item := lvStructure.Items.Add;
        Item.Caption := Query.FieldByName('name').AsString;
        Item.SubItems.Add(Query.FieldByName('type').AsString);

        if Query.FieldByName('notnull').AsInteger = 1 then
          Item.SubItems.Add('��')
        else
          Item.SubItems.Add('��');

        Item.SubItems.Add(Query.FieldByName('dflt_value').AsString);

        if Query.FieldByName('pk').AsInteger = 1 then
          Item.SubItems.Add('��')
        else
          Item.SubItems.Add('��');

        Query.Next;
      end;
    except
      on E: Exception do
      begin
        ShowMessage('���ر��ṹʧ��: ' + E.Message);
      end;
    end;
  finally
    Query.Free;
  end;
end;

procedure TfrmTableBrowser.LoadTableIndices;
var
  Query: TFDQuery;
  Item: TListItem;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  lvIndices.Items.Clear;

  Query := TFDQuery.Create(nil);
  try
    Query.Connection := FConnection;
    Query.SQL.Text := Format('PRAGMA index_list(%s)', [FTableName]);

    try
      Query.Open;

      while not Query.Eof do
      begin
        Item := lvIndices.Items.Add;
        Item.Caption := Query.FieldByName('name').AsString;

        if Query.FieldByName('unique').AsInteger = 1 then
          Item.SubItems.Add('��')
        else
          Item.SubItems.Add('��');

        // ��ȡ������
        var IndexQuery := TFDQuery.Create(nil);
        try
          IndexQuery.Connection := FConnection;
          IndexQuery.SQL.Text := Format('PRAGMA index_info(%s)', [Query.FieldByName('name').AsString]);
          IndexQuery.Open;

          var Columns := '';
          while not IndexQuery.Eof do
          begin
            if Columns <> '' then
              Columns := Columns + ', ';
            Columns := Columns + IndexQuery.FieldByName('name').AsString;
            IndexQuery.Next;
          end;

          Item.SubItems.Add(Columns);
        finally
          IndexQuery.Free;
        end;

        Query.Next;
      end;
    except
      on E: Exception do
      begin
        ShowMessage('���ر�����ʧ��: ' + E.Message);
      end;
    end;
  finally
    Query.Free;
  end;
end;

procedure TfrmTableBrowser.LoadTableTriggers;
var
  Query: TFDQuery;
  Item: TListItem;
begin
  if not Assigned(FConnection) or not FConnection.Connected then
    Exit;

  lvTriggers.Items.Clear;

  Query := TFDQuery.Create(nil);
  try
    Query.Connection := FConnection;
    Query.SQL.Text := Format('SELECT name, sql FROM sqlite_master WHERE type=''trigger'' AND tbl_name=''%s''', [FTableName]);

    try
      Query.Open;

      while not Query.Eof do
      begin
        Item := lvTriggers.Items.Add;
        Item.Caption := Query.FieldByName('name').AsString;
        Item.SubItems.Add(Query.FieldByName('sql').AsString);
        Query.Next;
      end;
    except
      on E: Exception do
      begin
        ShowMessage('���ر�������ʧ��: ' + E.Message);
      end;
    end;
  finally
    Query.Free;
  end;
end;

procedure TfrmTableBrowser.actRefreshExecute(Sender: TObject);
begin
  LoadTableData;
  LoadTableStructure;
  LoadTableIndices;
  LoadTableTriggers;
end;

procedure TfrmTableBrowser.actExportExecute(Sender: TObject);
begin
  // ����DataTransfer��Ԫ��ʵ��
end;

procedure TfrmTableBrowser.actImportExecute(Sender: TObject);
begin
  // ����DataTransfer��Ԫ��ʵ��
end;

procedure TfrmTableBrowser.actFilterExecute(Sender: TObject);
begin
  edtFilter.SetFocus;
end;

procedure TfrmTableBrowser.ApplyFilter;
var
  FilterStr: string;
begin
  FilterStr := Trim(edtFilter.Text);

  if FilterStr = '' then
  begin
    LoadTableData;
    Exit;
  end;

  try
    FQuery.Close;
    FQuery.SQL.Text := Format('SELECT * FROM %s WHERE %s', [FTableName, FilterStr]);
    FQuery.Open;
  except
    on E: Exception do
    begin
      ShowMessage('Ӧ�ù�����ʧ��: ' + E.Message);
      LoadTableData; // ���¼���ԭʼ����
    end;
  end;
end;

procedure TfrmTableBrowser.btnApplyFilterClick(Sender: TObject);
begin
  ApplyFilter;
end;

end.
